# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:          # List of stages for jobs, and their order of execution
  #- build
  #- test
  - deploy-web-1
  - deploy-web-2

deploy-job-testing-1:      # This job runs in the deploy stage.
  stage: deploy-web-1  # It only runs when *both* jobs in the test stage complete successfully.
  tags:
    - web03.bizapp.my
  environment: 
      name: production
      url: bizappshop.my
  script:
    - echo $USER
    - pwd
    # DEPLOY to BIZAPPSHOP.MY
    - echo "Deploying application to bizappshop.my..."
    - "cd /home/<USER>/public_html/bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to ADEL
    - echo "Deploying application to adel.bizappshop.my..."
    - "cd /home/<USER>/public_html/adel.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to BYREEFA
    - echo "Deploying application to byreefa.bizappshop.my..."
    - "cd /home/<USER>/public_html/byreefa.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to DDMLEGACY
    - echo "Deploying application to ddmlegacy.bizappshop.my..."
    - "cd /home/<USER>/public_html/ddmlegacy.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to KHAIZAN
    - echo "Deploying application to khaizan.bizappshop.my..."
    - "cd /home/<USER>/public_html/khaizan.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to NFH
    - echo "Deploying application to nfh.bizappshop.my..."
    - "cd /home/<USER>/public_html/nfh.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to NUFATRADE
    - echo "Deploying application to nufatrade.bizappshop.my..."
    - "cd /home/<USER>/public_html/nufatrade.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to RIZKA
    - echo "Deploying application to rizka.bizappshop.my..."
    - "cd /home/<USER>/public_html/rizka.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to SST
    - echo "Deploying application to sst.bizappshop.my..."
    - "cd /home/<USER>/public_html/sst.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to TUNIK
    - echo "Deploying application to tunik.bizappshop.my..."
    - "cd /home/<USER>/public_html/tunik.bizappshop.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    # DEPLOY to FERYS
    - echo "Deploying application to ferys.my..."
    - "cd /home/<USER>/public_html/ferys.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    - echo "Application successfully deployed."
  only:
    - production


deploy-job-testing-2:      # This job runs in the deploy stage.
  stage: deploy-web-2  # It only runs when *both* jobs in the test stage complete successfully.
  tags:
    - bizappshop-apache-aws-ec2
  environment: 
      name: production
      url: bizappshop.my
  script:
    - echo $USER
    - pwd
    # DEPLOY to BIZAPPSHOP.MY
    - echo "Deploying application to bizappshop.my..."
    #- "cd /home/<USER>/public_html/bizappshop.my && git checkout production && git pull origin production" #git init && git 
    - echo "Application successfully deployed."
  only:
    - production

#deploy-job-2:      # This job runs in the deploy stage.
  #stage: deploy-web-2  # It only runs when *both* jobs in the test stage complete successfully.
  #tags:
    #- web-02
  #environment: 
      #name: production
      #url: minishop.bizapp.my
  #script:
    #- echo $USER
    #- pwd
    #- echo "Deploying application..."
    #- "cd /home/<USER>/public_html/minishop.bizapp.my && git checkout production && git pull origin production" #git init && git checkout production && git pull origin production
    #- echo "Application successfully deployed."
  #only:
    #- production
