# Minishop Performance Improvements

## Overview
This document outlines the performance improvements implemented to reduce page load times from 7+ seconds to under 3 seconds.

## Changes Made

### 1. HTTP Timeouts (Critical)
- Added 5-second timeouts to all external API calls
- Prevents hanging requests that could take indefinitely
- **Impact**: Guarantees maximum 5 seconds per API call

### 2. Hybrid API Call Strategy
- **Phase 1**: Sequential calls for dependent APIs (detailshop → checkHQBizappay)
- **Phase 2**: Parallel calls for independent APIs (collections, products, profile, etc.)
- **Impact**: Reduces API call time from ~15 seconds to ~3-4 seconds

### 3. Smart Caching
- 2-minute cache for API responses
- Separate cache keys for different data types
- Cache invalidation endpoint for external systems
- **Impact**: Subsequent page loads in ~500ms-1s

### 4. Performance Monitoring
- Added timing logs for all operations
- Tracks cache hit/miss ratios
- Identifies slow operations automatically

### 5. Error Handling & Safety
- Fixed "Undefined array key 0" errors in controllers (lines 675, 680)
- Fixed "Undefined array key 0" errors in Blade templates (header-menu.blade.php:333)
- Added validation for API response data
- Safe array access with fallbacks in all view files
- Try-catch blocks for HTTP pool operations
- Cache key validation before access
- Created Blade helper directives for safe array access

## Files Modified

### Controllers
- `app/Http/Controllers/ShopGridController.php` - Main performance improvements
- `app/Http/Controllers/HomeController.php` - Collections endpoint performance improvements
- `app/Http/Controllers/ApiController.php` - Added timeouts to fallback methods

### Routes
- `routes/web.php` - Added cache invalidation endpoint

### New Files
- `app/Console/Commands/ClearMinishopCache.php` - Cache management command
- `app/Helpers/PerformanceMonitor.php` - Performance monitoring helper
- `app/Helpers/ArraySafetyHelper.php` - Array safety utilities
- `app/Helpers/BladeHelper.php` - Blade template safety helpers
- `app/Providers/BladeHelperServiceProvider.php` - Blade helper service provider
- `tests/Unit/ArraySafetyTest.php` - Unit tests for safety helpers

## Usage

### Cache Management
```bash
# Clear cache for specific PID
php artisan minishop:clear-cache 375270

# Clear all cache
php artisan minishop:clear-cache --all
```

### Cache Invalidation API
```bash
# External systems can call this when data changes
POST /api/invalidate-cache/{pid}
```

### Performance Monitoring
Check logs for performance metrics:
```bash
tail -f storage/logs/laravel.log | grep "Performance:"
```

## Expected Performance

| Scenario | Before | After |
|----------|--------|-------|
| First visit (no cache) | 7+ seconds | 3-4 seconds |
| Cached visit | 7+ seconds | 500ms-1s |
| Collections endpoint | 7+ seconds | 3-4 seconds |
| API timeout scenarios | Indefinite | Max 5 seconds |

## Cache Strategy

### Cache Keys
- `shop_collection_{pid}` - Product collections
- `product_list_{pid}_{cleanurl}` - Product listings
- `profile_{pid}` - Shop profile
- `record_{pid}` - Shop records
- `happy_hour_{cleanurl}` - Happy hour data

### Cache Duration
- **2 minutes** - Balance between performance and data freshness
- Can be adjusted based on business requirements

### Cache Invalidation
1. **Automatic**: Cache expires after 2 minutes
2. **Manual**: Use artisan command
3. **API**: External systems can trigger invalidation
4. **Emergency**: `Cache::flush()` clears everything

## Monitoring

### Performance Logs
```
[2024-01-01 12:00:00] Performance: shop_grid_load took 1250.5ms
[2024-01-01 12:00:00] Performance: parallel_api_calls took 850.2ms
```

### Slow Operation Alerts
```
[2024-01-01 12:00:00] Slow operation detected: shop_grid_load took 5500ms
```

## Troubleshooting

### If performance degrades:
1. Check external API response times
2. Verify cache is working: `php artisan cache:table` (if using database cache)
3. Clear cache: `php artisan minishop:clear-cache --all`
4. Check logs for timeout errors

### If data seems stale:
1. Check cache invalidation is working
2. Reduce cache duration if needed
3. Verify external systems are calling invalidation endpoint

### If "Undefined array key" errors occur:
1. Clear view cache: `php artisan view:clear`
2. Clear config cache: `php artisan config:clear`
3. Check if BladeHelperServiceProvider is registered in config/app.php
4. Verify all view files have safety checks for array access

### Common Error Fixes:
```bash
# Clear all caches
php artisan view:clear
php artisan config:clear
php artisan cache:clear

# Check for syntax errors
php artisan route:list

# Test specific route
curl -I https://bizappshop.my/airwings
```

## Future Improvements

1. **Redis Caching**: Migrate from file cache to Redis for better performance
2. **Queue Jobs**: Move heavy processing to background jobs
3. **CDN**: Implement CDN for static assets
4. **Database Optimization**: Add local database caching layer
5. **API Optimization**: Work with external API provider to improve response times

## Configuration

### Environment Variables
```env
CACHE_DRIVER=file  # Consider changing to redis in production
```

### Cache Configuration
Edit `config/cache.php` for advanced cache settings.
