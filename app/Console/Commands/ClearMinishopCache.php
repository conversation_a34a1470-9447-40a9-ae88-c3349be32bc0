<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearMinishopCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'minishop:clear-cache {pid?} {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear minishop cache for specific PID or all cache';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pid = $this->argument('pid');
        $all = $this->option('all');

        if ($all) {
            Cache::flush();
            $this->info('All cache cleared successfully!');
            return 0;
        }

        if ($pid) {
            $cacheKeys = [
                "shop_collection_{$pid}",
                "profile_{$pid}",
                "record_{$pid}",
                "productCollection_{$pid}",
            ];

            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            // Also clear product list cache patterns
            $this->clearProductListCache($pid);

            $this->info("Cache cleared for PID: {$pid}");
        } else {
            $this->error('Please provide a PID or use --all flag');
            return 1;
        }

        return 0;
    }

    private function clearProductListCache($pid)
    {
        // Since we can't easily iterate cache keys, we'll use a pattern
        // In production, consider using Redis with pattern matching
        $commonUrls = ['bizappstore', 'airwings']; // Add more as needed
        
        foreach ($commonUrls as $url) {
            Cache::forget("product_list_{$pid}_{$url}");
            Cache::forget("happy_hour_{$url}");
        }
    }
}
