<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ApiController;

class TestCollectionsApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:collections-api {pid?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the collections and product list APIs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pid = $this->argument('pid') ?? '375270'; // Default to airwings PID
        $cleanurl = 'airwings';
        
        $this->info("Testing Collections APIs for PID: {$pid}");
        
        try {
            $apiController = new ApiController();
            
            // Test shop collection
            $this->info("\n=== Testing Shop Collection API ===");
            $shopCollection = $apiController->getShopCollection($pid);
            $this->info("Shop Collection Count: " . (is_array($shopCollection) ? count($shopCollection) : 'N/A'));
            if (!empty($shopCollection)) {
                $this->info("Sample Collection: " . ($shopCollection[0]['koleksi'] ?? 'N/A'));
            }
            
            // Test product list
            $this->info("\n=== Testing Product List API ===");
            $productList = $apiController->getHomeProductList($pid, $cleanurl);
            $this->info("Product List Count: " . (is_array($productList) ? count($productList) : 'N/A'));
            if (!empty($productList)) {
                $this->info("Sample Product: " . ($productList[0]['productname'] ?? 'N/A'));
                $this->info("Sample Product Collection IDs: " . ($productList[0]['senaraikoleksiid'] ?? 'N/A'));
            }
            
            // Test profile
            $this->info("\n=== Testing Profile API ===");
            $profile = $apiController->getProfile($pid);
            $this->info("Profile Count: " . (is_array($profile) ? count($profile) : 'N/A'));
            if (!empty($profile)) {
                $this->info("Profile STATUS: " . ($profile[0]['STATUS'] ?? 'N/A'));
            }
            
            // Simulate the arrCollProd logic
            $this->info("\n=== Simulating arrCollProd Logic ===");
            $arrCollProd = [];
            if (!empty($shopCollection) && !empty($productList)) {
                foreach ($shopCollection as $sc) {
                    $arrCollProd[$sc['id']] = [];
                    foreach ($productList as $hp) {
                        $arrSK = explode(',', $hp['senaraikoleksiid']);
                        if (in_array($sc['id'], $arrSK)) {
                            $arrCollProd[$sc['id']][] = $hp;
                        }
                    }
                }
            }
            $arrCollProd[0] = $productList; // All products
            
            $this->info("arrCollProd keys: " . implode(', ', array_keys($arrCollProd)));
            $this->info("arrCollProd[0] count: " . (isset($arrCollProd[0]) ? count($arrCollProd[0]) : 'N/A'));
            
            // Check specific collection
            if (isset($arrCollProd[0]) && count($arrCollProd[0]) > 0) {
                $this->info("✅ All products available in arrCollProd[0]");
            } else {
                $this->error("❌ No products in arrCollProd[0]");
            }
            
        } catch (\Exception $e) {
            $this->error("API call failed: " . $e->getMessage());
        }
        
        return 0;
    }
}
