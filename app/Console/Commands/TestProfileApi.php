<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ApiController;

class TestProfileApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:profile-api {pid?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the profile API to debug data retrieval';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pid = $this->argument('pid') ?? '375270'; // Default to airwings PID
        
        $this->info("Testing Profile API for PID: {$pid}");
        $this->info("Base URL: " . config('minishop.base_url'));
        $this->info("Domain: " . config('minishop.domain'));
        
        try {
            $apiController = new ApiController();
            $profile = $apiController->getProfile($pid);
            
            $this->info("API Response received:");
            $this->info("Type: " . gettype($profile));
            $this->info("Count: " . (is_array($profile) ? count($profile) : 'N/A'));
            
            if (is_array($profile) && !empty($profile)) {
                $this->info("Profile data found!");
                if (isset($profile[0])) {
                    $this->info("First element keys: " . implode(', ', array_keys($profile[0])));
                    $this->info("Sample data:");
                    $this->table(['Key', 'Value'], [
                        ['STATUS', $profile[0]['STATUS'] ?? 'N/A'],
                        ['pid', $profile[0]['pid'] ?? 'N/A'],
                        ['alamat1', $profile[0]['alamat1'] ?? 'N/A'],
                        ['negeri', $profile[0]['negeri'] ?? 'N/A'],
                        ['nohp', $profile[0]['nohp'] ?? 'N/A'],
                    ]);

                    if (isset($profile[0]['STATUS']) && $profile[0]['STATUS'] == '1') {
                        $this->info("✅ STATUS = 1 - Profile data should be displayed");
                    } else {
                        $this->warn("⚠️  STATUS = " . ($profile[0]['STATUS'] ?? 'N/A') . " - Profile data should be hidden");
                    }
                } else {
                    $this->warn("Profile array is not indexed properly");
                    $this->info("Raw data: " . json_encode($profile, JSON_PRETTY_PRINT));
                }
            } else {
                $this->error("No profile data found or invalid response");
                $this->info("Raw response: " . json_encode($profile, JSON_PRETTY_PRINT));
            }
            
        } catch (\Exception $e) {
            $this->error("API call failed: " . $e->getMessage());
            $this->error("Trace: " . $e->getTraceAsString());
        }
        
        return 0;
    }
}
