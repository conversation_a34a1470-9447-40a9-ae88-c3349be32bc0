<?php

namespace App\Helpers;

class ArraySafetyHelper
{
    /**
     * Safely get array value with default fallback
     */
    public static function safeGet($array, $key, $default = null)
    {
        return isset($array[$key]) ? $array[$key] : $default;
    }

    /**
     * Safely get nested array value
     */
    public static function safeGetNested($array, $keys, $default = null)
    {
        $current = $array;
        
        foreach ($keys as $key) {
            if (!is_array($current) || !isset($current[$key])) {
                return $default;
            }
            $current = $current[$key];
        }
        
        return $current;
    }

    /**
     * Check if array has valid first element
     */
    public static function hasValidFirstElement($array)
    {
        return is_array($array) && !empty($array) && isset($array[0]);
    }

    /**
     * Safely get first element of array
     */
    public static function getFirstElement($array, $default = [])
    {
        return self::hasValidFirstElement($array) ? $array[0] : $default;
    }

    /**
     * Safely get property from first element of array
     */
    public static function getFirstElementProperty($array, $property, $default = null)
    {
        $firstElement = self::getFirstElement($array);
        return is_array($firstElement) && isset($firstElement[$property]) ? $firstElement[$property] : $default;
    }
}
