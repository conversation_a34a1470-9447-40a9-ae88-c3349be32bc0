<?php

namespace App\Helpers;

class BladeHelper
{
    /**
     * Safely get array value for Blade templates
     */
    public static function safeArrayGet($array, $key, $default = '')
    {
        return isset($array[$key]) ? $array[$key] : $default;
    }

    /**
     * Safely get first element property for Blade templates
     */
    public static function safeFirstElement($array, $property, $default = '')
    {
        return isset($array[0][$property]) ? $array[0][$property] : $default;
    }

    /**
     * Check if array has valid first element
     */
    public static function hasFirstElement($array)
    {
        return is_array($array) && !empty($array) && isset($array[0]);
    }

    /**
     * Get profile property safely
     */
    public static function getProfileProperty($getProfile, $property, $default = '')
    {
        return isset($getProfile[0][$property]) ? $getProfile[0][$property] : $default;
    }

    /**
     * Get shop detail property safely
     */
    public static function getShopProperty($detailshop, $property, $default = '')
    {
        return isset($detailshop[0][$property]) ? $detailshop[0][$property] : $default;
    }

    /**
     * Get happy hour property safely
     */
    public static function getHappyHourProperty($happyhour, $property, $default = '')
    {
        return isset($happyhour[0][$property]) ? $happyhour[0][$property] : $default;
    }

    /**
     * Format address safely
     */
    public static function formatAddress($getProfile)
    {
        if (!isset($getProfile[0])) {
            return '';
        }

        $alamat1 = isset($getProfile[0]['alamat1']) ? $getProfile[0]['alamat1'] : '';
        $alamat2 = isset($getProfile[0]['alamat2']) ? $getProfile[0]['alamat2'] : '';
        $alamat3 = isset($getProfile[0]['alamat3']) ? $getProfile[0]['alamat3'] : '';

        return trim($alamat1 . ' ' . $alamat2 . ' ' . $alamat3);
    }

    /**
     * Get state name from code safely
     */
    public static function getStateName($getProfile)
    {
        if (!isset($getProfile[0]['negeri'])) {
            return 'MALAYSIA';
        }

        $stateMap = [
            '1' => 'JOHOR',
            '2' => 'KEDAH',
            '3' => 'KELANTAN',
            '4' => 'MELAKA',
            '5' => 'NEGERI SEMBILAN',
            '6' => 'PAHANG',
            '7' => 'PULAU PINANG',
            '8' => 'PERAK',
            '9' => 'PERLIS',
            '10' => 'SELANGOR',
            '11' => 'TERENGGANU',
            '12' => 'SABAH',
            '13' => 'SARAWAK',
            '14' => 'WILAYAH PERSEKUTUAN KUALA LUMPUR',
            '15' => 'WILAYAH PERSEKUTUAN LABUAN',
            '16' => 'WILAYAH PERSEKUTUAN PUTRAJAYA',
        ];

        return $stateMap[$getProfile[0]['negeri']] ?? 'MALAYSIA';
    }
}
