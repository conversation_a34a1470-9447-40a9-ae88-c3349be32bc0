<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

class PerformanceMonitor
{
    private static $timers = [];

    /**
     * Start timing an operation
     */
    public static function start($operation)
    {
        self::$timers[$operation] = microtime(true);
    }

    /**
     * End timing and log the result
     */
    public static function end($operation, $context = [])
    {
        if (!isset(self::$timers[$operation])) {
            return;
        }

        $duration = microtime(true) - self::$timers[$operation];
        $durationMs = round($duration * 1000, 2);

        Log::info("Performance: {$operation} took {$durationMs}ms", array_merge([
            'operation' => $operation,
            'duration_ms' => $durationMs,
            'duration_seconds' => round($duration, 3),
        ], $context));

        unset(self::$timers[$operation]);

        return $durationMs;
    }

    /**
     * Time a closure and return its result
     */
    public static function time($operation, callable $callback, $context = [])
    {
        self::start($operation);
        $result = $callback();
        self::end($operation, $context);
        return $result;
    }

    /**
     * Log slow operations (over threshold)
     */
    public static function logIfSlow($operation, $durationMs, $threshold = 1000, $context = [])
    {
        if ($durationMs > $threshold) {
            Log::warning("Slow operation detected: {$operation} took {$durationMs}ms", array_merge([
                'operation' => $operation,
                'duration_ms' => $durationMs,
                'threshold_ms' => $threshold,
            ], $context));
        }
    }
}
