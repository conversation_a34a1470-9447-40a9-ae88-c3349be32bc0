<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Exception;
use Redirect;

class ApiController extends Controller
{
    public function __construct()
    {
        $this->url = config('minishop.base_url');
        $this->domainx = config('minishop.domain');
    }

    // public static function checkshopfirst ()
    // {
    //     // to store domain
    //     $domainx = config('minishop.domain');

    //     $host = request()->getHost();
    //     $globalDomain = "BIZAPP";

    //     if($host == "STAGE.BIZAPPSHOP.MY" || $host == "SHOP.BIZAPP.MY" || "minishop.BIZAPP.MY" || $host == "minishop.mommibuddy.com" || $host == "SHOPPREVIEW.BIZAPP.MY" || $host == "127.0.0.1" || $host == "minishop_laravel.test" || $host == "localhost" || $host == "minishop-laravel.test"){
    //         $globalDomain = "BIZAPP";
    //     } else {
    //         // // ini privillege user
    //         // $globalDomain = $host;
    //         // // $globalDomain = Str::replace('SHOP', '', $globalDomain);
    //         // $globalDomain = Str::replace('BIZAPPSHOP.MY', '', $globalDomain);
    //         // $globalDomain = Str::replace('.', '', $globalDomain);

    //         // domain get from env - config for all priviledge
    //         $globalDomain = $domainx;
    //     }

    //     return $globalDomain;
    // }

    // to check sama ada ejen valid/follow hq tu
    public function affiliate ($pidejen)
    {
        $url = config('minishop.base_url');
        $checkejenstatus = Http::asForm()->post($url . 'api_name=TRACK_GET_LIST_STOKIST&TX=', [
            'pid' => $pidejen,
            'DOMAIN' => $this->domainx,
            'TOKEN' => 'aa'
        ])->throw()->json();
    
        return $checkejenstatus;
    }

    public function detailshop ($pid_mask, $cleanurl, $agentpid)
    {
        // get HQ profile detail
        $detailshop = Http::timeout(5)->asForm()->post($this->url . 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx,
            'agentpid' => $agentpid,
        ])->throw()->json();

        return $detailshop;
    }

    public function getProductCollection ($pid_mask)
    {
        $getProductCollection = Http::timeout(5)->asForm()->post('https://corrad.visionice.net/bizapp/apigenerator_VERSIX.php?' . 'api_name=TRACK_GET_LIST_KOLEKSI_MINISHOPWITHITEMONLY&TX=', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx,
        ])->json();

        return $getProductCollection;
    }

    public function getHomeProductList ($pid_mask, $cleanurl_mask)
    {
        // list all product
        $getHomeProductList = Http::timeout(5)->asForm()->post($this->url . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'DOMAIN' => $this->domainx,
            'TOKEN' => 'aa',
            'productid' => '',
            'sorttype' => '',
            'keyword' => '',
            'filterkoleksi' => ''
        ])->throw()->json();

        return $getHomeProductList;
    }

    public function getDiscountByQuantity ($pid, $productid, $quantity)
    {
        $getInfoBank = Http::asForm()->post($this->url . 'api_name=TRACK_GET_NEWPRICE4CUSTOMER&TX=', [
            'pid' => $pid,
            'productid' => $productid,
            'quantity' => $quantity,
            'DOMAIN' => $this->domainx,
            'TOKEN' => 'aa',
        ])->throw()->json();

        return $getInfoBank;
    }

    public function getHappyHour ($cleanurl_mask)
    {
        // info happy hour
        $getHappyHour = Http::timeout(5)->asForm()->post($this->url . 'api_name=TRACK_GETHAPYHOURINFO&TX=', [
            'penggunaid' => $cleanurl_mask,
            'DOMAIN' => $this->domainx,
        ])->throw()->json();

        return $getHappyHour;
    }

    public function getTopSaleProduct ($pid_mask, $cleanurl_mask)
    {
        // for top sale product
        $getTopSaleProduct = Http::timeout(5)->asForm()->post($this->url . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'DOMAIN' => $this->domainx,
            'TOKEN' => 'aa',
            'productid' => '',
            'sorttype' => 'TOPSALES',
            'keyword' => '',
            'filterkoleksi' => ''
        ])->throw()->json();

        return $getTopSaleProduct;
    }

    public function getTermCondition ($pid_mask)
    {
        $getTermCondition = Http::asForm()->post($this->url . 'api_name=TRACK_MINISHOP_TERMANDCONDITION&TX=', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        return $getTermCondition;
    }

    public function getCaraPenghantaran ($pid_mask)
    {
        $getCaraPenghantaran = Http::asForm()->post($this->url . 'api_name=GETMINISHOP_CARAPENGHANTARAN&TX=', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        return $getCaraPenghantaran;
    }

    public function getProfile ($pid_mask)
    {
        try {
            $response = Http::timeout(5)->asForm()->post($this->url . 'api_name=TRACK_GET_PROFILE&TX=', [
                'pid' => $pid_mask,
                'TOKEN' => 'aa',
                'DOMAIN' => $this->domainx
            ]);

            // Log the raw response for debugging
            \Log::info('Profile API Raw Response', [
                'pid' => $pid_mask,
                'url' => $this->url . 'api_name=TRACK_GET_PROFILE&TX=',
                'domain' => $this->domainx,
                'status' => $response->status(),
                'body' => $response->body(),
                'successful' => $response->successful()
            ]);

            if ($response->successful()) {
                $getProfile = $response->json();

                // Log the parsed response
                \Log::info('Profile API Parsed Response', [
                    'pid' => $pid_mask,
                    'response' => $getProfile,
                    'is_array' => is_array($getProfile),
                    'count' => is_array($getProfile) ? count($getProfile) : 0,
                    'has_data' => !empty($getProfile)
                ]);

                return $getProfile;
            } else {
                \Log::error('Profile API Failed', [
                    'pid' => $pid_mask,
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return [];
            }
        } catch (\Exception $e) {
            \Log::error('Profile API Exception', [
                'pid' => $pid_mask,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    public function getShopCollection ($pid_mask)
    {
        $getShopCollection = Http::timeout(5)->asForm()->post('https://corrad.visionice.net/bizapp/apigenerator_VERSIX.php?api_name=TRACK_GET_LIST_KOLEKSI_MINISHOPWITHITEMONLY&TX=', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        return $getShopCollection;
    }

    public function getRecord ($pid_mask)
    {
        $getRecord = Http::timeout(5)->asForm()->post('https://corrad.visionice.net/bizapp/apigenerator.php?api_name=TRACK_LIST_DONETRACKINGNO&TX==', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'start' => '0',
            'loadsemua' => 'NO',
            'jenissort' => '0',
            'sk' => '',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        return $getRecord;
    }

    public function getPostageCost ($pid_mask, $totalWeight)
    {
        $getPostageCost = Http::asForm()->post($this->url . 'api_name=TRACK_GET_KOSPOS_MINISHOP_ONLY&TX=', [
            'pid' => $pid_mask,
            'weight' => $totalWeight,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        return $getPostageCost;
    }

    public function getPaymentUrl ($pid, $sellingprice, $saveOrderId, $fpxKey, $agentpid)
    {
        // $getPaymentUrl = Http::asForm()->post($this->url . 'api_name=SHOP_TRACK_CREATE_BILL_TO_STOCKIST_AFFILIATE&TX=', [
        //     'pid' => $pid,
        //     'DOMAIN' => $global_domain,
        //     'TOKEN' => 'aa',
        //     'nilai' => $sellingprice,
        //     'pidstokis' => $pid,
        //     'GLOBAL_FPXKEY' => $fpxKey,
        //     'orderid' => $saveOrderId,
        //     'agentpid' => $agentpid // if no affiliate, send null || ""
        // ])->throw();

        // $body = $getPaymentUrl->body();
        // $json = json_decode($body, true);

        // dd([$pid, $sellingprice, $saveOrderId, $fpxKey, $global_domain, $agentpid]);
        // dd($json);
        // return $getPaymentUrl;


        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->url . 'api_name=SHOP_TRACK_CREATE_BILL_TO_STOCKIST_AFFILIATE&TX=',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array(
                'DOMAIN' => $this->domainx,
                'pid' => $pid,
                'TOKEN' => 'aa',
                'nilai' => $sellingprice,
                'pidstokis' => $pid,
                'GLOBAL_FPXKEY' => $fpxKey,
                'orderid' => $saveOrderId,
                'agentpid' => $agentpid),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        // dd($response);
        return $response;

    }

    public function uploadAttachment ($pid , $orderid, $filePath , $fileName)
    {
        // upload image attachment if any
        // TODO: letak random no untuk filename - issue bizapp X ?
        $upload = Http::attach('file', $filePath, $fileName)->post('https://corrad.visionice.net/bizapp/apigenerator.php?api_name=TRACK_SAVE_ATTACHMENT&TX=', [
            'pid' => $pid,
            'DOMAIN' => $this->domainx,
            'TOKEN' => 'aa',
            'orderid' => $orderid
        ])->throw();

        return $upload;
    }

    public function getProductDetail ($productid, $agentpid)
    {
        
        $getProductDetail = Http::asForm()->post($this->url . 'api_name=TRACK_GET_PRODUCT_INFO&TX=', [
            'productid' => $productid,
            'pid' => $agentpid,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx
        ])->throw()->json();

        if($getProductDetail != [] && $getProductDetail[0]['productstatus'] == '0'){
            return [];
        }
        return $getProductDetail;
    }

    public function checkHQBizappay ($hasBizappay, $cleanurl_mask)
    {
        // check if hq/seller has bizappay account
        if($hasBizappay === "N"){
            return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
        }
        else{
            return true;
        }
    }

    public function getBundlePrice($pid, array $cart)
    {

        $tempArr = [];
        $tempArr = collect($cart)->keys();
        for ($i = 0; $i < count($tempArr); ++$i) {
            $prod = $cart[$tempArr[$i]];
            $productids[] = $prod['id'];
            $productqtys[] = $prod['quantity'];

        }

        $bundle = Http::asForm()->post('https://corrad.visionice.net/bizapp/apigenerator.php?api_name=TRACK_GET_PRODUCT_DISCOUNTMULTIPRODUCT_INFO&TX=', [
            'pid' => $pid,
            'pidhq' => $pid,
            'TOKEN' => 'aa',
            'DOMAIN' => $this->domainx,
            'productid1' => $productids[0] ?? '',
            'productid2' => $productids[1] ?? '',
            'productid3' => $productids[2] ?? '',
            'productid4' => $productids[3] ?? '',
            'productid5' => $productids[4] ?? '',
            'productid6' => $productids[5] ?? '',
            'productid7' => $productids[6] ?? '',
            'productid8' => $productids[7] ?? '',
            'productid9' => $productids[8] ?? '',
            'productid10' => $productids[9] ?? '',
            'productid11' => $productids[10] ?? '',
            'productid12' => $productids[11] ?? '',
            'productid13' => $productids[12] ?? '',
            'productid14' => $productids[13] ?? '',
            'productid15' => $productids[14] ?? '',
            'productid16' => $productids[15] ?? '',
            'productid17' => $productids[16] ?? '',
            'productid18' => $productids[17] ?? '',
            'productid19' => $productids[18] ?? '',
            'productid20' => $productids[19] ?? '',

            'quantity1' => $productqtys[0] ?? '',
            'quantity2' => $productqtys[1] ?? '',
            'quantity3' => $productqtys[2] ?? '',
            'quantity4' => $productqtys[3] ?? '',
            'quantity5' => $productqtys[4] ?? '',
            'quantity6' => $productqtys[5] ?? '',
            'quantity7' => $productqtys[6] ?? '',
            'quantity8' => $productqtys[7] ?? '',
            'quantity9' => $productqtys[8] ?? '',
            'quantity10' => $productqtys[9] ?? '',
            'quantity11' => $productqtys[10] ?? '',
            'quantity12' => $productqtys[11] ?? '',
            'quantity13' => $productqtys[12] ?? '',
            'quantity14' => $productqtys[13] ?? '',
            'quantity15' => $productqtys[14] ?? '',
            'quantity16' => $productqtys[15] ?? '',
            'quantity17' => $productqtys[16] ?? '',
            'quantity18' => $productqtys[17] ?? '',
            'quantity19' => $productqtys[18] ?? '',
            'quantity20' => $productqtys[19] ?? '',
        ])->throw()->json();

        return $bundle;
    }
}
