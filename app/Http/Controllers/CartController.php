<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;

class CartController extends Controller
{
    public function indexMask ($url_param)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        $detailshop = (new ApiController)->detailshop('', $url_param, null);

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            $this->checkClearCart($pid_mask);

            $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            $hasBizappay = (new ApiController)->checkHQBizappay($detailshopx[0]['bizappayagent'], $cleanurl_mask);
            
            // check if seller has bizappay account
            if($hasBizappay === true){
                $getProfile = (new ApiController)->getProfile($pid_mask);

                $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        
                return view('cart')
                    ->with('getProfile', $getProfile)
                    ->with('url_mask', $cleanurl_mask)
                    ->with('productList', $getHomeProductList)
                    ->with('productLike', array_slice($getHomeProductList, 0, 4))
                    ->with('url_param', $cleanurl_mask);
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else
        {
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    public function indexMaskAgent ($url_param, $agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }
  
        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            $this->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{
                    
                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        $getProfile = (new ApiController)->getProfile($pid_mask);

        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);

        return view('cart')
            ->with('getProfile', $getProfile)
            ->with('url_mask', $cleanurl_mask)
            ->with('agentpid', $agentpid)
            ->with('productList', $getHomeProductList)
            ->with('productLike', array_slice($getHomeProductList, 0, 4))
            ->with('url_param', $cleanurl_mask);
    }

    public function addToCart(Request $request,$url_param)
    {
        $cart = session()->get('cart');
        // check if quantity is more than stock count
        if($request->currentStockStatus == 'N' && (int)$request->quantity > (int)$request->currentStockCount){
            return redirect()->back()->with('error','The quantity entered exceeded the stock count for this item. Please adjust your quantity');
        } 
        else {
            $decodedCart = json_decode($request->cartItems[0]);

            // if cart is empty then this the first product
            if(!$cart) {

                $finalprice = 0;
                $hasDiscount = "N";
                $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart->pid, $decodedCart->id, $request->quantity);
                // dd('item pertama: $disQuantity ' . $disQuantity . ' ' . $decodedCart->pid . ' ' . $decodedCart->id . ' ' . $request->quantity);

                // check dulu discount by quantity
                if($disQuantity === "" || $disQuantity === null){
                    $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $finalprice = number_format($decodedCart->promotionprice, 2) : $finalprice = number_format($decodedCart->price, 2);
                    $hasDiscount = "N";
                } 
                else{
                    $finalprice = $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $decodedCart->promotionprice - $disQuantity : $decodedCart->price - $disQuantity;
                    $hasDiscount = "Y";
                }

                $cart = [
                    $decodedCart->id => [
                        "id" => $decodedCart->id,
                        "name" => $decodedCart->productname,
                        "quantity" => $request->quantity,
                        "price" => $finalprice,
                        "agentprice" => number_format($decodedCart->agentprice2U, 2),
                        "photo" => $decodedCart->attachment,
                        "weight" => $decodedCart->weight,
                        "hq_url" => $url_param,
                        "hq_pid" => $decodedCart->pid,
                        "max_qty" => str_replace(',', '', $decodedCart->bilstok),
                        "discount" => 0,
                        "hasdiscount" => $hasDiscount,
                        "priceasal" => $decodedCart->PROMOSIHAPPYHOUR === "Y" ? number_format($decodedCart->promotionprice, 2) : number_format($decodedCart->price, 2),
                    ]
                ];

                // reset discount
                $cart[$decodedCart->id]["discount"] = 0;
                session()->put('cart', $cart);

                return redirect()->back()->with('successAtc', 'Product added to cart successfully!');
            }

            // check if cart items more than 20
            if(count($cart) <= 20 ){

                // if cart not empty then check if this product exist then increment quantity
                if(isset($cart[$decodedCart->id])) {

                    $quantityTemp = $cart[$decodedCart->id]['quantity'] + $request->quantity;

                    $finalprice = 0;
                    $hasDiscount = "N";
                    $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart->pid, $decodedCart->id, $quantityTemp);
                    // dd('item pertama: $disQuantity ' . $disQuantity . ' ' . $decodedCart->pid . ' ' . $decodedCart->id . ' ' . $request->quantity);

                    // check dulu discount by quantity
                    if($disQuantity === "" || $disQuantity === null){
                        $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $finalprice = number_format($decodedCart->promotionprice, 2) : $finalprice = number_format($decodedCart->price, 2);
                        $hasDiscount = "N";
                    } 
                    else{
                        $finalprice = $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $decodedCart->promotionprice - $disQuantity : $decodedCart->price - $disQuantity;
                        $hasDiscount = "Y";
                    }

                    // simpan balik dalam array product terlibat
                    $cart[$decodedCart->id]['quantity'] = $cart[$decodedCart->id]['quantity'] + $request->quantity;
                    $cart[$decodedCart->id]['price'] =  $finalprice;
                    $cart[$decodedCart->id]['hasdiscount'] =  $hasDiscount;


                    // reset discount
                    $cart[$decodedCart->id]["discount"] = 0;
                    session()->put('cart', $cart);
                    return redirect()->back()->with('successAtc', 'Product added to cart successfully!');
                } 
                else {
                    $finalprice = 0;
                    $hasDiscount = "N";
                    $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart->pid, $decodedCart->id, $request->quantity);
                    // dd('tambah item: $disQuantity ' . $disQuantity . ' ' . $decodedCart->pid . ' ' . $decodedCart->id . ' ' . $request->quantity);
    
                    // check dulu discount by quantity
                    if($disQuantity === "" || $disQuantity === null){
                        $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $finalprice = number_format($decodedCart->promotionprice, 2) : $finalprice = number_format($decodedCart->price, 2);
                        $hasDiscount = "N";
                    } 
                    else{
                        $finalprice = $decodedCart->PROMOSIHAPPYHOUR === "Y" ? $decodedCart->promotionprice - $disQuantity : $decodedCart->price - $disQuantity;
                        $hasDiscount = "Y";
                    }

                    $cart[$decodedCart->id] = [
                            "id" => $decodedCart->id,
                            "name" => $decodedCart->productname,
                            "quantity" => $request->quantity,
                            "price" => $finalprice,
                            "agentprice" => number_format($decodedCart->agentprice2U, 2),
                            "photo" => $decodedCart->attachment,
                            "weight" => $decodedCart->weight,
                            "hq_url" => $url_param,
                            "hq_pid" => $decodedCart->pid,
                            "max_qty" => str_replace(',', '', $decodedCart->bilstok),
                            "discount" => 0,
                            "hasdiscount" => $hasDiscount,
                            "priceasal" => $decodedCart->PROMOSIHAPPYHOUR === "Y" ? number_format($decodedCart->promotionprice, 2) : number_format($decodedCart->price, 2),
                    ];

                }
                // reset discount
                $cart[$decodedCart->id]["discount"] = 0;
                session()->put('cart', $cart);

                // Clear multiple cookies
                Cookie::queue(Cookie::forget('totalSaved'));
                Cookie::queue(Cookie::forget('appliedCouponCode'));
                Cookie::queue(Cookie::forget('kuponid'));

                return redirect()->back()->with('successAtc', 'Product added to cart successfully!');
            } 
            else {
                return redirect()->back()->with('error','Your cart has reached 20 items limit, please remove unwanted items or proceed to checkout');
            }
        }

    }

    public function removeCartById(Request $request)
    {
        $cart = session()->pull('cart', []);
        unset($cart[$request->id]); // Unset the index you want

        // reset discount
        // $cart[$request->productId]["discount"] = 0;
        session()->put('cart', $cart);

        // Clear multiple cookies
        Cookie::queue(Cookie::forget('totalSaved'));
        Cookie::queue(Cookie::forget('appliedCouponCode'));
        Cookie::queue(Cookie::forget('kuponid'));

        return redirect()->back();
    }

    // get product detail from API Controller
    public function recalculatePrice(Request $request)
    {
        // Get the product ID and new quantity from the request
        $pid = $request->input('pid');
        $productId = $request->input('productId');
        $newQuantity = $request->input('newQuantity');
        $dataType = $request->input('dataType');

        // to store session after inject plus minus quantity
        $cart = session()->get('cart');
    
        $disQuantity = (new ApiController)->getDiscountByQuantity($pid, $productId, $newQuantity);

        if($disQuantity === "" || $disQuantity === null){
            $cart[$productId]['hasdiscount'] = 'N';
            $cart[$productId]['price'] = $cart[$productId]['priceasal'];
            session()->put('cart', $cart);
        } else {
            $cart[$productId]['hasdiscount'] = 'Y';
            $cart[$productId]['price'] = $cart[$productId]['priceasal'] - $disQuantity;
            session()->put('cart', $cart);
        }

        // dd($cart);
    
        return response()->json(['success' => true, 'disquantity' => $disQuantity, 'pid' => $pid]);
    }
    

    public function updateQuantity(Request $request)
    {
        // Get the product ID and new quantity from the request
        $productId = $request->input('productId');
        $newQuantity = $request->input('newQuantity');
        $dataType = $request->input('dataType');
        
        $cart = session()->get('cart');

        if($dataType === 'plus'){
            // if ready stock, tambah je quantity, limit max to 500 (change max value accordingly)
            if($cart[$productId]["max_qty"] == "-100" && $newQuantity <= 500){
                $cart[$productId]["quantity"]++;

                // reset discount
                $cart[$productId]["discount"] = 0;
                session()->put('cart', $cart);
                session()->flash('success', 'Cart updated successfully');
            } else if ($cart[$productId]["quantity"] < $cart[$productId]["max_qty"]){
                $cart[$productId]["quantity"]++;

                // reset discount
                $cart[$productId]["discount"] = 0;
                session()->put('cart', $cart);
                session()->flash('success', 'Cart updated successfully');
            } else {
                session()->flash('error','Max quantity for ' . $cart[$productId]["name"] . ' has been reached');
            }
        } else if($dataType === 'minus'){
            // to prevent if quantity is 0 or negative value
            if($cart[$productId]['quantity'] <= 0){

                // reset discount
                $cart[$productId]["discount"] = 0;
                $cart = session()->pull('cart', []);
                unset($cart[$request->id]); // Unset the index you want
                session()->put('cart', $cart);
            }
            else{
                $cart[$request->productId]['quantity']--;

                // reset discount
                $cart[$productId]["discount"] = 0;
                session()->put('cart',$cart);
                session()->flash('success','Cart updated successfully');
            }
        } else if ($dataType === 'input') { // For when directly change the quantity in input field
            $newQuantity = $request->newQuantity;

            if ($newQuantity >= 1 && (($cart[$productId]["max_qty"] == "-100" && $newQuantity <= 500) || $newQuantity <= $cart[$productId]["max_qty"])) {
                $cart[$productId]["quantity"] = $newQuantity;
            
                // reset discount
                $cart[$productId]["discount"] = 0;
                session()->put('cart', $cart);
                session()->flash('success', 'Cart updated successfully');
            } else {
                session()->flash('error', 'Invalid quantity or max quantity reached for ' . $cart[$productId]["name"]);
            }
        }       
        
         // Clear multiple cookies
         Cookie::queue(Cookie::forget('totalSaved'));
         Cookie::queue(Cookie::forget('appliedCouponCode'));
         Cookie::queue(Cookie::forget('kuponid'));

        // Return the updated data back to the script
        return response()->json(['success' => true, 'newQuantity' => $cart[$productId]["quantity"], 'maxQuantity' => $cart[$productId]["max_qty"]]);
    }

    //apply discount code
    public function applyDiscountCoupon(Request $request, $url_param)
    {
        // get from another controller
        $discountCode = $request->couponCode;
        //$url_mask = UrlMask::where('url_param', $url_param)->first();


        $pid_mask = config('minishop.default_pid');

        $detailshop = (new ApiController)->detailshop('', $url_param, "");

        if($detailshop != []){
            // prooceed
            $pid_mask = $detailshop[0]['pid'];
        }
        else{
            // user not exist
            $pid_mask = config('minishop.default_pid');
        }


        $cart = session()->get('cart');
        $tempArr = [];
        $tempArr = collect($cart)->keys();
        
        // products
        $productid1 = '';
        $productqty1 = '';
        $productid2 = '';
        $productqty2 = '';
        $productid3 = '';
        $productqty3 = '';
        $productid4 = '';
        $productqty4 = '';
        $productid5 = '';
        $productqty5 = '';
        $productid6 = '';
        $productqty6 = '';
        $productid7 = '';
        $productqty7 = '';
        $productid8 = '';
        $productqty8 = '';
        $productid9 = '';
        $productqty9 = '';
        $productid10 = '';
        $productqty10 = '';
        $productid11 = '';
        $productqty11 = '';
        $productid12 = '';
        $productqty12 = '';
        $productid13 = '';
        $productqty13 = '';
        $productid14 = '';
        $productqty14 = '';
        $productid15 = '';
        $productqty15 = '';
        $productid16 = '';
        $productqty16 = '';
        $productid17 = '';
        $productqty17 = '';
        $productid18 = '';
        $productqty18 = '';
        $productid19 = '';
        $productqty19 = '';
        $productid20 = '';
        $productqty20 = '';

        $dis1 = 0;
        $dis2 = 0;
        $dis3 = 0;
        $dis4 = 0;
        $dis5 = 0;
        $dis6 = 0;
        $dis7 = 0;
        $dis8 = 0;
        $dis9 = 0;
        $dis10 = 0;
        $dis11 = 0;
        $dis12 = 0;
        $dis13 = 0;
        $dis14 = 0;
        $dis15 = 0;
        $dis16 = 0;
        $dis17 = 0;
        $dis18 = 0;
        $dis19 = 0;
        $dis20 = 0;
        
        if ($cart === null){
            return response()->json(['error' => "Can't apply coupon on an empty cart"]);
        }
        if (count($cart) == 0){
            return response()->json(['error' => "Can't apply coupon on an empty cart"]);
        }

        $getP1 = $cart[$tempArr[0]];
        $productid1 = $getP1['id'];
        $productqty1 = $getP1['quantity'];

        if(count($tempArr) > 1){
            $getP2 = $cart[$tempArr[1]];
            $productid2 = $getP2['id'];
            $productqty2 = $getP2['quantity'];
        }

        if(count($tempArr) > 2){
            $getP3 = $cart[$tempArr[2]];
            $productid3 = $getP3['id'];
            $productqty3 = $getP3['quantity'];
        }

        if(count($tempArr) > 3){
            $getP4 = $cart[$tempArr[3]];
            $productid4 = $getP4['id'];
            $productqty4 = $getP4['quantity'];
        }

        if(count($tempArr) > 4){
            $getP5 = $cart[$tempArr[4]];
            $productid5 = $getP5['id'];
            $productqty5 = $getP5['quantity'];
        }

        if(count($tempArr) > 5){
            $getP6 = $cart[$tempArr[5]];
            $productid6 = $getP6['id'];
            $productqty6 = $getP6['quantity'];
        }

        if(count($tempArr) > 6){
            $getP7 = $cart[$tempArr[6]];
            $productid7 = $getP7['id'];
            $productqty7 = $getP7['quantity'];
        }

        if(count($tempArr) > 8){
            $getP8 = $cart[$tempArr[7]];
            $productid8 = $getP8['id'];
            $productqty8 = $getP8['quantity'];
        }

        if(count($tempArr) > 9){
            $getP9 = $cart[$tempArr[8]];
            $productid9 = $getP9['id'];
            $productqty9 = $getP9['quantity'];
        }

        if(count($tempArr) > 10){
            $getP10 = $cart[$tempArr[9]];
            $productid10 = $getP10['id'];
            $productqty10 = $getP10['quantity'];
        }

        if(count($tempArr) > 11){
            $getP11 = $cart[$tempArr[10]];
            $productid11 = $getP11['id'];
            $productqty11 = $getP11['quantity'];
        }
        if(count($tempArr) > 12){
            $getP12 = $cart[$tempArr[11]];
            $productid12 = $getP12['id'];
            $productqty12 = $getP12['quantity'];
        }
        if(count($tempArr) > 13){
            $getP13 = $cart[$tempArr[12]];
            $productid13 = $getP13['id'];
            $productqty13 = $getP13['quantity'];
        }
        if(count($tempArr) > 14){
            $getP14 = $cart[$tempArr[13]];
            $productid14 = $getP14['id'];
            $productqty14 = $getP14['quantity'];
        }
        if(count($tempArr) > 15){
            $getP15 = $cart[$tempArr[14]];
            $productid15 = $getP15['id'];
            $productqty15 = $getP15['quantity'];
        }
        if(count($tempArr) > 16){
            $getP16 = $cart[$tempArr[15]];
            $productid16 = $getP16['id'];
            $productqty16 = $getP16['quantity'];
        }
        if(count($tempArr) > 17){
            $getP17 = $cart[$tempArr[16]];
            $productid17 = $getP17['id'];
            $productqty17 = $getP17['quantity'];
        }
        if(count($tempArr) > 18){
            $getP18 = $cart[$tempArr[17]];
            $productid18 = $getP18['id'];
            $productqty18 = $getP18['quantity'];
        }
        if(count($tempArr) > 19){
            $getP19 = $cart[$tempArr[18]];
            $productid19 = $getP19['id'];
            $productqty19 = $getP19['quantity'];
        }
        if(count($tempArr) > 20){
            $getP20 = $cart[$tempArr[19]];
            $productid20 = $getP20['id'];
            $productqty20 = $getP20['quantity'];
        }
        
        // make the API call
        $url = config('minishop.base_url');
        $domainx = config('minishop.domain');
        $checkCouponCode = Http::asForm()->post($url . 'api_name=TRACK_VALIDATE_KUPON&TX=', [
            'pid' => $pid_mask,
            'TOKEN' => 'aa',
            'DOMAIN' => $domainx,
            'kupon_kod' => $discountCode,
            'productid_1' => $productid1,
            'quantity_1' => $productqty1,
            'productid_2' => $productid2,
            'quantity_2' => $productqty2,
            'productid_3' => $productid3,
            'quantity_3' => $productqty3,
            'productid_4' => $productid4,
            'quantity_4' => $productqty4,
            'productid_5' => $productid5,
            'quantity_5' => $productqty5,
            'productid_6' => $productid6,
            'quantity_6' => $productqty6,
            'productid_7' => $productid7,
            'quantity_7' => $productqty7,
            'productid_8' => $productid8,
            'quantity_8' => $productqty8,
            'productid_9' => $productid9,
            'quantity_9' => $productqty9,
            'productid_10' => $productid10,
            'quantity_10' => $productqty10,
            'productid_11' => $productid11,
            'quantity_11' => $productqty11,
            'productid_12' => $productid12,
            'quantity_12' => $productqty12,
            'productid_13' => $productid13,
            'quantity_13' => $productqty13,
            'productid_14' => $productid14,
            'quantity_14' => $productqty14,
            'productid_15' => $productid15,
            'quantity_15' => $productqty15,
            'productid_16' => $productid16,
            'quantity_16' => $productqty16,
            'productid_17' => $productid17,
            'quantity_17' => $productqty17,
            'productid_18' => $productid18,
            'quantity_18' => $productqty18,
            'productid_19' => $productid19,
            'quantity_19' => $productqty19,
            'productid_20' => $productid20,
            'quantity_20' => $productqty20,

        ])->throw()->json();


        if($checkCouponCode[0]['kuponexist'] == "Y")
        {
            $cart = session()->get('cart');

            // check dulu ade bundle ke tak
            $getBundle = (new ApiController)->getBundlePrice($pid_mask, $cart);

            // tiada bundle, so assign harga diskaun
            if($getBundle[0]['FINALRETAILPRICE'] === '' || $getBundle[0]['FINALRETAILPRICE'] === '-1'){
                if($checkCouponCode[0]['amount1'] != '0')
                {
                    if($checkCouponCode[0]['discounttype1'] == "PERCENTAGE")
                    {
                        $dis1 = (float)str_replace(',', "", $getP1['price']) * (float)$checkCouponCode[0]['amount1'] / 100 * (int)$productqty1;
    
                        $dis1 < 0 ? $dis1 = 0 : null;
    
                        // update discount productid
                        $cart[$productid1]["discount"] = $dis1;
                        session()->put('cart', $cart);
                    
                    } else {
                        $dis1 = (int)$checkCouponCode[0]['amount1'] * (int)$productqty1;
    
                        $dis1 < 0 ? $dis1 = 0 : null;
    
                        // update discount productid
                        $cart[$productid1]["discount"] = $dis1;
                        session()->put('cart', $cart);
                    }
                }
                
                if($checkCouponCode[0]['amount2'] != '0')
                {
                    if($checkCouponCode[0]['discounttype2'] == "PERCENTAGE")
                    {
                        $dis2 = (float)str_replace(',', "", $getP2['price']) * (float)$checkCouponCode[0]['amount2'] / 100 * (int)$productqty2;
    
                        $dis2 < 0 ? $dis2 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid2]["discount"] = $dis2;
                        session()->put('cart', $cart);
                    } else {
                        $dis2 = (int)$checkCouponCode[0]['amount2'] * (int)$productqty2;
    
                        $dis2 < 0 ? $dis2 = 0 : null;
    
                        // update discount productid
                        $cart[$productid2]["discount"] = $dis2;
                        session()->put('cart', $cart);
                    }
                }
                
                if($checkCouponCode[0]['amount3'] != '0')
                {
                    if($checkCouponCode[0]['discounttype3'] == "PERCENTAGE")
                    {
                        $dis3 = (float)str_replace(',', "", $getP3['price']) * (float)$checkCouponCode[0]['amount3'] / 100 * (int)$productqty3;
    
                        $dis3 < 0 ? $dis3 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid3]["discount"] = $dis3;
                        session()->put('cart', $cart);       
                    } else {
                        $dis3 = (int)$checkCouponCode[0]['amount3'] * (int)$productqty3;
    
                        $dis3 < 0 ? $dis3 = 0 : null;
    
                        // update discount productid
                        $cart[$productid3]["discount"] = $dis3;
                        session()->put('cart', $cart);
                    }
                }
                
                if($checkCouponCode[0]['amount4'] != '0')
                {
                    if($checkCouponCode[0]['discounttype4'] == "PERCENTAGE")
                    {
                        $dis4 = (float)str_replace(',', "", $getP4['price']) * (float)$checkCouponCode[0]['amount4'] / 100 * (int)$productqty4;
    
                        $dis4 < 0 ? $dis4 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid4]["discount"] = $dis4;
                        session()->put('cart', $cart);
                    } else {
                        $dis4 = (int)$checkCouponCode[0]['amount4'] * (int)$productqty4;
    
                        $dis4 < 0 ? $dis4 = 0 : null;
    
                        // update discount productid
                        $cart[$productid4]["discount"] = $dis4;
                        session()->put('cart', $cart); 
                    }
                }
                
                if($checkCouponCode[0]['amount5'] != '0')
                {
                    if($checkCouponCode[0]['discounttype5'] == "PERCENTAGE")
                    {
                        $dis5 = (float)str_replace(',', "", $getP5['price']) * (float)$checkCouponCode[0]['amount5'] / 100 * (int)$productqty5;
    
                        $dis5 < 0 ? $dis5 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid5]["discount"] = $dis5;
                        session()->put('cart', $cart); 
                    } else {
                        $dis5 = (int)$checkCouponCode[0]['amount5'] * (int)$productqty5;
    
                        $dis5 < 0 ? $dis5 = 0 : null;
    
                        // update discount productid
                        $cart[$productid5]["discount"] = $dis5;
                        session()->put('cart', $cart); 
                    }
                }
    
                if($checkCouponCode[0]['amount6'] != '0')
                {
                    if($checkCouponCode[0]['discounttype6'] == "PERCENTAGE")
                    {
                        $dis6 = (float)str_replace(',', "", $getP6['price']) * (float)$checkCouponCode[0]['amount6'] / 100 * (int)$productqty6;
    
                        $dis6 < 0 ? $dis6 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid6]["discount"] = $dis6;
                        session()->put('cart', $cart); 
                    } else {
                        $dis6 = (int)$checkCouponCode[0]['amount6'] * (int)$productqty6;
    
                        $dis6 < 0 ? $dis6 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid6]["discount"] = $dis6;
                        session()->put('cart', $cart);
                    }
                }
                
                if($checkCouponCode[0]['amount7'] != '0')
                {
                    if($checkCouponCode[0]['discounttype7'] == "PERCENTAGE")
                    {
                        $dis7 = (float)str_replace(',', "", $getP7['price']) * (float)$checkCouponCode[0]['amount7'] / 100 * (int)$productqty7;
    
                        $dis7 < 0 ? $dis7 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid7]["discount"] = $dis7;
                        session()->put('cart', $cart);
                    } else {
                        $dis7 = (int)$checkCouponCode[0]['amount7'] * (int)$productqty7;
    
                        $dis7 < 0 ? $dis7 = 0 : null;
    
                        // update discount productid
                        $cart[$productid7]["discount"] = $dis7;
                        session()->put('cart', $cart);
                    }
                }
    
                if($checkCouponCode[0]['amount8'] != '0')
                {
                    if($checkCouponCode[0]['discounttype8'] == "PERCENTAGE")
                    {
                        $dis8 = (float)str_replace(',', "", $getP8['price']) * (float)$checkCouponCode[0]['amount8'] / 100 * (int)$productqty8;
    
                        $dis8 < 0 ? $dis8 = 0 : null;
                            
                        // update discount productid
                        $cart[$productid8]["discount"] = $dis8;
                        session()->put('cart', $cart);
                    } else {
                        $dis8 = (int)$checkCouponCode[0]['amount8'] * (int)$productqty8;
    
                        $dis8 < 0 ? $dis8 = 0 : null;
    
                        // update discount productid
                        $cart[$productid8]["discount"] = $dis8;
                        session()->put('cart', $cart);
                    }
                }
    
                if($checkCouponCode[0]['amount9'] != '0')
                {
                    if($checkCouponCode[0]['discounttype9'] == "PERCENTAGE")
                    {
                        $dis9 = (float)str_replace(',', "", $getP9['price']) * (float)$checkCouponCode[0]['amount9'] / 100 * (int)$productqty9;
    
                        $dis9 < 0 ? $dis9 = 0 : null;
                        
                        // update discount productid
                        $cart[$productid9]["discount"] = $dis9;
                        session()->put('cart', $cart);
                    } else {
                        $dis9 = (int)$checkCouponCode[0]['amount9'] * (int)$productqty9;
    
                        $dis9 < 0 ? $dis9 = 0 : null;
    
                        // update discount productid
                        $cart[$productid9]["discount"] = $dis9;
                        session()->put('cart', $cart);
                    }
                }
    
                if($checkCouponCode[0]['amount10'] != '0')
                {
                    if($checkCouponCode[0]['discounttype10'] == "PERCENTAGE")
                    {
                        $dis10 = (float)str_replace(',', "", $getP10['price']) * (float)$checkCouponCode[0]['amount10'] / 100 * (int)$productqty10;
    
                        $dis10 < 0 ? $dis10 = 0 : null;
                        
                        // update discount productid
                        $cart[$productid10]["discount"] = $dis10;
                        session()->put('cart', $cart);
                    } else {
                        $dis10 = (int)$checkCouponCode[0]['amount10'] * (int)$productqty10;
    
                        $dis10 < 0 ? $dis10 = 0 : null;
    
                        // update discount productid
                        $cart[$productid10]["discount"] = $dis10;
                        session()->put('cart', $cart);
                    }
                }
    
                $totalSaved = $dis1 + $dis2 + $dis3 + $dis4 + $dis5 + $dis6 + $dis7 + $dis8 + $dis9 + $dis10 + $dis11 + $dis12 + $dis13 + $dis14 + $dis15 + $dis16 + $dis17 + $dis18 + $dis19 + $dis20;
                $savedValueCookie = Cookie::queue('totalSaved', $totalSaved, 60); // save cookie for 60 minutes
                $savedCouponCode = Cookie::queue('appliedCouponCode', $discountCode, 60); // save cookie for 60 minutes
    
                Cookie::queue('kuponid', $checkCouponCode[0]['kupon_id'], 60);
    
                /// to display type discount to user
                if($checkCouponCode[0]['jenisdiskaun_general'] == "FIX")
                {
                    $typediscount = "RM " . $checkCouponCode[0]['amount_general'];
                } else {
                    $typediscount = $checkCouponCode[0]['amount_general'] . "%";
                }
    
                return response()->json([
                    'success' => "Congratulation! You are entitled to get $typediscount discount for each product that has been specified by us (if any)", 
                    'totalSaved' => $totalSaved, 
                    'cart' => $cart, 
                    'bundlePrice' => '',
                    'discountCode' => $discountCode
                ]);
            
            } else{

                // ada bundle, so assign bundle attribute
                $totalSaved = 0;
                $typediscount = 'Bundle Product';
                $discountCode = '';
                $savedCouponCode = Cookie::queue('appliedCouponCode', $discountCode, 60);
                Cookie::queue('kuponid', $checkCouponCode[0]['kupon_id'], 60);

                return response()->json([
                    'success' => "Congratulation! You are entitled to get $typediscount discount for each product that has been specified by us (if any)", 
                    'totalSaved' => $totalSaved, 
                    'cart' => $cart, 
                    'bundlePrice' => $getBundle[0]['FINALRETAILPRICE'],
                    'discountCode' => $discountCode
                ]);
            }
            
        } else {
            return response()->json(['error' => "Coupon code ($discountCode) is not valid"]);
        }
    }
    
    public function checkClearCart($pid_mask)
    {
        $cart  = session()->get('cart');

        if($cart)
        {
            // check if products in cart from different HQ are present
            foreach ($cart as $item)
            {
                if ($item['hq_pid'] != $pid_mask)
                {
                    session()->forget('cart');
                    session()->flush();
                    break;
                }
            }
        }
        return;
    }

}
