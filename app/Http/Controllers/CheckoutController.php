<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ApiController;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;

class CheckoutController extends Controller
{
    public function indexMask (Request $request, $url_param)
    {
        // get from another controller
        $detailshop = (new ApiController)->detailshop('', $url_param, null);

        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');

        if($detailshop != []){
            // prooceed
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            $hasBizappay = (new ApiController)->checkHQBizappay($detailshopx[0]['bizappayagent'], $cleanurl_mask);
            
            // check if seller has bizappay account
            if($hasBizappay === true){
                $cart = session()->get('cart');
                $totalWeight = 0;
                $subtotalPrice = 0;
                $subtotalAgentPrice = 0;
                $totalSellingPrice = $request->totalSellingPrice || 0;
        
                if(isset($cart)) {
                    foreach($cart as $id => $item){
                        // assign and want to pass these value to blade

                        if($item['hasdiscount'] === "Y"){
                            $subtotalPrice += str_replace(",","", $item['price']) * $item['quantity'];
                        } else {
                            $subtotalPrice += str_replace(",","", $item['priceasal']) * $item['quantity'];
                        } 

                        $totalWeight = $totalWeight + ($item['weight'] * $item['quantity']);
                        $subtotalAgentPrice += str_replace(",","", $item['agentprice']) * $item['quantity'];
                    }
                }
        
                $getPostageCost = (new ApiController)->getPostageCost($pid_mask, $totalWeight);
                $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
                $getProfile = (new ApiController)->getProfile($pid_mask);
                $getCaraPenghantaran = (new ApiController)->getCaraPenghantaran($pid_mask);
                // check bundle dulu
                $getBundlex = $this->checkBundle($pid_mask);
        
                return view('checkout')
                ->with('postageCost',$getPostageCost)
                ->with('subtotalPrice', $subtotalPrice)
                ->with('subtotalAgentPrice', $subtotalAgentPrice)
                ->with('totalSellingPrice',$totalSellingPrice)
                ->with('url_mask', $cleanurl_mask)
                ->with('url_param', $cleanurl_mask)
                ->with('productList', $getHomeProductList)
                ->with('getProfile', $getProfile)
                ->with('caraPenghantaran', $getCaraPenghantaran)
                ->with('detailshop', $detailshop)
                ->with('getBundlex', $getBundlex)
                ->with('totalWeight',$totalWeight);
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else{
            // user not exist
            $error_status = 'yes';
            return redirect()->away('https://www.bizapp.com.my/');
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    public function indexMaskAgent (Request $request, $url_param, $agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{
                    
                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away('https://www.bizapp.com.my/');
            // return view('landing-lead')->with('error_status', $error_status);
        }

        $cart = session()->get('cart');
        $totalWeight = 0;
        $subtotalPrice = 0;
        $subtotalAgentPrice = 0;
        $totalSellingPrice = $request->totalSellingPrice || 0;

        if(isset($cart)) {
            foreach($cart as $id => $item){
                // assign and want to pass these value to blade

                if($item['hasdiscount'] === "Y"){
                    $subtotalPrice += str_replace(",","", $item['price']) * $item['quantity'];
                } else {
                    $subtotalPrice += str_replace(",","", $item['priceasal']) * $item['quantity'];
                }

                $totalWeight = $totalWeight + ($item['weight'] * $item['quantity']);
                $subtotalAgentPrice += str_replace(",","", $item['agentprice']) * $item['quantity'];
            }
        }

        $getPostageCost = (new ApiController)->getPostageCost($pid_mask, $totalWeight);
        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        $getProfile = (new ApiController)->getProfile($pid_mask);
        $getCaraPenghantaran = (new ApiController)->getCaraPenghantaran($pid_mask);
         // check bundle dulu
         $getBundlex = $this->checkBundle($pid_mask);

        return view('checkout')
        ->with('postageCost',$getPostageCost)
        ->with('subtotalPrice', $subtotalPrice)
        ->with('subtotalAgentPrice', $subtotalAgentPrice)
        ->with('totalSellingPrice',$totalSellingPrice)
        ->with('url_mask', $cleanurl_mask)
        ->with('url_param', $cleanurl_mask)
        ->with('agentpid', $agentpid)
        ->with('productList', $getHomeProductList)
        ->with('getProfile', $getProfile)
        ->with('detailshop', $detailshop)
        ->with('getBundlex', $getBundlex)
        ->with('caraPenghantaran', $getCaraPenghantaran)
        ->with('totalWeight',$totalWeight);
    }

    public function processCheckout(Request $request)
    {
        // pakai dari frontend - hidden type
        $cleanurl_maskv2 = $request->cleanurl_maskv2;
        $pid_maskv2 = $request->pid_maskv2;


        // get from another controller

        if($request->hasFile('proof')){
            $proof = $request->file('proof');
            $fileName = $pid_maskv2.'_'.time().'.'.$proof->getClientOriginalExtension();
            $proof->move(public_path('upload/image/'.$cleanurl_maskv2.'/'), $fileName);
        }

        $cart  = session()->get('cart');
        if(!$cart){
            return redirect()->back()->with('error','Cannot checkout with an empty cart. Please add product to cart before proceed.');
        }
        $tempArr = [];
        $tempArr = collect($cart)->keys();

        $pid = $pid_maskv2;
        $name = $request->first_name . ' ' . $request->last_name;
        $address = $request->address1 . ', ' . $request->address2 . ', ' . $request->postcode . ', ' . $request->state . ', ' . $request->country;
        $hpno = $request->hpno;
        $email = $request->email;
        $sellingprice = $request->totalPrice;
        $hargakospos = $request->postCost;
        $totalWeight = $request->totalWeight;
        $date = Carbon::now();
        $formatedDate = $date->format('d-m-Y');
        $agentpid = $request->agent_pid;
       
        // if tiada ejen, assign to ""
        isset($agentpid) ? $agentpid = $agentpid : $agentpid = "";

        // $attachment = $request->proof;


        // total kuantiti yg user pilih
        $tempkuantiti = 0;

        for ($i = 0; $i < count($tempArr); ++$i) {
            $prod = $cart[$tempArr[$i]];
            $productids[] = $prod['id'];
            $productqtys[] = $prod['quantity'];

            $tempkuantiti = $prod['quantity'];
        }


        // set default payment to fpx
        $request->pay = 'FPX';
        $payment = $request->pay;

        if($request->delivery_method === "CODK"){
            $payment = 'MANUAL';
        } else{
            $payment = 'FPX';
        }

        // check if products in cart from different HQ are present
        foreach ($cart as $item) {

            if (isset($item['hq_url']) && $item['hq_url'] != $cleanurl_maskv2) {
                // dd($item['hq_url'] != $url_param);
                session()->forget('cart');
                session()->flush();
                return redirect()->back()->with('error',"Some of your items in cart are not from '$cleanurl_maskv2' , Please re-add the products into your cart and proceed to checkout.");
                break;
            }
        }

        // create seperate function
        $minishop_minimumunit = $request->minishop_minimumunit;
        $minishop_kospenghantaran_wajib = $request->minishop_kospenghantaran_wajib;
        $minishop_attachmentfile_wajib = $request->minishop_attachmentfile_wajib;

        // jika user tak pilih option payment
        // if($request->pay == null){
        //     return redirect()->back()->with('error', "Please select payment method.")->withInput();
        // }
        // jika kuantiti kurang drpd kuantiti had belian yg ditetapkan HQ
        if( (int)$minishop_minimumunit > (int)$tempkuantiti ){
            return redirect()->back()->with('error', "The minimum order are $minishop_minimumunit unit.")->withInput();
        }
        // jika kospos wajib tapi user tak pilih kospos
        else if($minishop_kospenghantaran_wajib == "1" && ($hargakospos == "" || $hargakospos == null)){
            return redirect()->back()->with('error', "Please select postage.")->withInput();
        }
        // jika user wajib upload attachment for proof payment
        // else if($minishop_attachmentfile_wajib == "1" && $request->pay == 'pay_cod' && ($request->proof == null || $request->proof == "")){
        //     return redirect()->back()->with('error', "Please upload file attachment for payment proof.")->withInput();
        // }


        $fpxKey = rand(1000000, 9999999).str_pad(3, STR_PAD_LEFT); // generate random number for FPXKEY e.g. 9421492112451264


        // make the API call
        $url = config('minishop.base_url');
        $domainx = config('minishop.domain');
        $saveOrder = Http::asForm()->post($url . 'api_name=SHOP_TRACK_SAVE_ORDER_MULTIPLE_NEW_ATTACHMENT&TX=', [
            'pid' => $pid,
            'TOKEN' => 'aa',
            'DOMAIN' => $domainx,
            'name' => $name,
            'address' => $address,
            'hpno' => $hpno,
            'email' => $email,
            'sellingprice' => $sellingprice,
            'hargakospos_id' => $hargakospos,
            'weight' => $totalWeight,
            'productid1' => $productids[0],
            'productid2' => $productids[1] ?? '',
            'productid3' => $productids[2] ?? '',
            'productid4' => $productids[3] ?? '',
            'productid5' => $productids[4] ?? '',
            'productid6' => $productids[5] ?? '',
            'productid7' => $productids[6] ?? '',
            'productid8' => $productids[7] ?? '',
            'productid9' => $productids[8] ?? '',
            'productid10' => $productids[9] ?? '',
            'productid11' => $productids[10] ?? '',
            'productid12' => $productids[11] ?? '',
            'productid13' => $productids[12] ?? '',
            'productid14' => $productids[13] ?? '',
            'productid15' => $productids[14] ?? '',
            'productid16' => $productids[15] ?? '',
            'productid17' => $productids[16] ?? '',
            'productid18' => $productids[17] ?? '',
            'productid19' => $productids[18] ?? '',
            'productid20' => $productids[19] ?? '',
            'quantity1' => $productqtys[0],
            'quantity2' => $productqtys[1] ?? '',
            'quantity3' => $productqtys[2] ?? '',
            'quantity4' => $productqtys[3] ?? '',
            'quantity5' => $productqtys[4] ?? '',
            'quantity6' => $productqtys[5] ?? '',
            'quantity7' => $productqtys[6] ?? '',
            'quantity8' => $productqtys[7] ?? '',
            'quantity9' => $productqtys[8] ?? '',
            'quantity10' => $productqtys[9] ?? '',
            'quantity11' => $productqtys[10] ?? '',
            'quantity12' => $productqtys[11] ?? '',
            'quantity13' => $productqtys[12] ?? '',
            'quantity14' => $productqtys[13] ?? '',
            'quantity15' => $productqtys[14] ?? '',
            'quantity16' => $productqtys[15] ?? '',
            'quantity17' => $productqtys[16] ?? '',
            'quantity18' => $productqtys[17] ?? '',
            'quantity19' => $productqtys[18] ?? '',
            'quantity20' => $productqtys[19] ?? '',
            'fpxkey' => $payment == 'FPX' ? $fpxKey : "",
            'tarikhsubmit' => $formatedDate,
            'jenispenghantaran' => $request->delivery_method,
            'note' => $request->bundleName === '' || $request->bundleName === null ? $request->note : $request->note . '(' . $request->bundleName . ')',
            'kupon_id' => request()->cookie('kuponid'),
            'actualpaidtostockistprice' => $sellingprice,
            'shouldpayingprice' => $sellingprice,
            'agentpid' => $agentpid,

        ])->throw()->json();


        if($saveOrder[0]['STATUS'] == "1"){

            // upload gambar attachment jika ada -
            if($request->hasFile('proof')){
                $imagePath = File::get(public_path('upload/image/'.$cleanurl_maskv2.'/'.$fileName));
                $imageName = $fileName;
                // $this->uploadattachment($pid, $saveOrder[0]['ID'], $imagePath, $imageName);
                $uploadimg = (new ApiController)->uploadAttachment($pid, $saveOrder[0]['ID'], $imagePath, $imageName);
                // return $uploadimg;
            }

            if($payment == 'FPX'){
               $tt = (new ApiController)->getPaymentUrl($pid, $sellingprice, $saveOrder[0]['ID'], $fpxKey, $agentpid);

                // temporary - try to forget session on payment callback if successful = forget
                session()->forget('cart');
                session()->flush();

                // Clear multiple cookies
                Cookie::queue(Cookie::forget('totalSaved'));
                Cookie::queue(Cookie::forget('appliedCouponCode'));
                Cookie::queue(Cookie::forget('kuponid'));

                return redirect()->away($tt);

            } else {

                // success manual payment
                session()->forget('cart');
                session()->flush();

                // Clear multiple cookies
                Cookie::queue(Cookie::forget('totalSaved'));
                Cookie::queue(Cookie::forget('appliedCouponCode'));
                Cookie::queue(Cookie::forget('kuponid'));
                
                // if affiliate exist, redirect to home affiliate
                if($agentpid != ""){
                    return redirect()->route('homeMaskAgent', ['url_param' => $cleanurl_maskv2, 'agent_pid' => $agentpid])->withCookie(Cookie::forget('totalSaved'))
                    ->withCookie(Cookie::forget('appliedCouponCode'))
                    ->with('paymentDone','Payment Done');
                } 
                else{
                    return redirect()->route('homeMask', ['url_param' => $cleanurl_maskv2])->withCookie(Cookie::forget('totalSaved'))
                    ->withCookie(Cookie::forget('appliedCouponCode'))
                    ->with('paymentDone','Payment Done');
                }

            }

        }
        else if($saveOrder[0]['STATUS'] == "9"){
            // error
            // popup msg-> Maaf! KOS PENGHANTARAN ini tidak boleh digunakan untuk penghantaran ke destinasi penerima.
            //             Sorry! This SHIPPING COST cannot be used for delivery to the recipient's destination.
            return redirect()->back()->with('error',"Sorry, This SHIPPING COST cannot be used for delivery to recipient's destination.")->withInput();
        }
        else if($saveOrder[0]['STATUS'] == "12"){
            // error
            // popup msg-> Maaf! KOS PENGHANTARAN ini tidak boleh dipilih bersama dengan CARA PENGHANTARAN pilihan anda.
            //             Sorry! This SHIPPING COST cannot be selected along with the DELIVERY METHOD of your choice.
            return redirect()->back()->with('error',"Sorry, This SHIPPING COST cannot be selected along with the DELIVERY METHOD of your choice.")->withInput();
        }
        else if($saveOrder[0]['STATUS'] == "2"){
            // error
            // popup msg-> Maaf! Sila pilih kos penghantaran mengikut zon yang tepat (SEMENANJUNG/SABAH/SARAWAK).
            //             Sorry! Please select the shipping cost according to the exact zone (SEMENAJNJUNG/SABAH/SARAWAK).
            return redirect()->back()->with('error',"Sorry, Please select the shipping cost according to the exact zone (SEMENANJUNG/SABAH/SARAWAK).")->withInput();
        }
        else{
            // error - ada item sangkut (out of stock)
            // popup msg-> The item $saveOrder[0]['itemsangkut'] is out if stock. Please remove the item and submit again.
            return redirect()->back()->with('error',"The item " . $saveOrder[0]['itemsangkut'] . " is out of stock. Please remove the item and submit again.")->withInput();
        }
    }

    public function checkBundle($pid){
        
        $cart = session()->get('cart');

        if(!$cart){
            session()->put('bundleName', '');
            session()->put('bundlePrice', '');
            return '';
        } else {

            $bundle = (new ApiController)->getBundlePrice($pid, $cart);
            session()->put('bundlePrice', $bundle[0]['FINALRETAILPRICE']);
            session()->put('bundleName', $bundle[0]['BUNDLE_discountmultiproductname']);
            
            return $bundle[0]['FINALRETAILPRICE'];
        }

    }
}
