<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ApiController;

class ContactController extends Controller
{

    public function indexMask ($url_param)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        // get from another controller
        $detailshop = (new ApiController)->detailshop('', $url_param, null);

        if($detailshop != []){
            // prooceed
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            $hasBizappay = (new ApiController)->checkHQBizappay($detailshopx[0]['bizappayagent'], $cleanurl_mask);
            
            // check if seller has bizappay account
            if($hasBizappay === true){
                $getTermCondition = (new ApiController)->getTermCondition($pid_mask);
                $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
                $getProfile = (new ApiController)->getProfile($pid_mask);

                return view('contact')
                ->with('getTermCondition', $getTermCondition)
                ->with('url_param', $cleanurl_mask)
                ->with('productList', $getHomeProductList)
                ->with('url_mask', $cleanurl_mask)
                ->with('getProfile', $getProfile);
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else{
            // user not exist
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    public function indexMaskAgent ($url_param, $agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{
                    
                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        // get HQ term and condition
        $getTermCondition = (new ApiController)->getTermCondition($pid_mask);
        // list all product
        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        // getProfile from ApiController
        $getProfile = (new ApiController)->getProfile($pid_mask);

        return view('contact')
        ->with('getTermCondition', $getTermCondition)
        ->with('url_param', $cleanurl_mask)
        ->with('productList', $getHomeProductList)
        ->with('url_mask', $cleanurl_mask)
        ->with('agentpid', $agentpid)
        ->with('getProfile', $getProfile);
    }

}
