<?php

namespace App\Http\Controllers;

use App\Models\UrlMask;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Client\Pool;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\CartController;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Helpers\PerformanceMonitor;

class HomeController extends Controller
{

    // public function indexMask($url_param)
    // {
    //     $pid_mask = config('minishop.default_pid');
    //     $cleanurl_mask = config('minishop.default_cleanurl');
    //     $bizappurl = config('minishop.bizappurl');

    //     $detailshop = (new ApiController)->detailshop('', $url_param, null);

    //     if($detailshop != [])
    //     {
    //         $pid_mask = $detailshop[0]['pid'];
    //         $cleanurl_mask = $url_param;

    //         (new CartController)->checkClearCart($pid_mask);
    //     }
    //     else
    //     {
    //         $error_status = 'yes';
    //         return redirect()->away($bizappurl);
    //         // return view('landing-lead')->with('error_status', $error_status);
    //     }

    //     $getProductCollection = (new ApiController)->getProductCollection($pid_mask);
    //     $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
    //     $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
    //     $getTopSaleProduct = (new ApiController)->getTopSaleProduct($pid_mask, $cleanurl_mask);
    //     $getProfile = (new ApiController)->getProfile($pid_mask);
    //     $getRecord = (new ApiController)->getRecord($pid_mask);

    //     return view('index')
    //         ->with('collectionList', array_slice($getProductCollection, 0, 4))
    //         ->with('happyhour', $getHappyHour)
    //         ->with('detailshop', $detailshop)
    //         ->with('url_mask', $cleanurl_mask)
    //         ->with('url_param', $cleanurl_mask)
    //         ->with('getProfile', $getProfile)
    //         ->with('recordList', $getRecord)
    //         ->with('topsaleproduct', array_slice($getTopSaleProduct,0,7))
    //         ->with('productList', array_slice($getHomeProductList,0,7));
    // }
    
    public function indexMask($url_param)
    {
    // Retrieve default configuration values.
    $pid_mask = config('minishop.default_pid');
    $cleanurl_mask = config('minishop.default_cleanurl');
    $bizappurl = config('minishop.bizappurl');

    // Cache the detailshop query result for 1 minute.
    // The cache key is built using the url_param.
    $detailshop = Cache::remember('detailshop_' . $url_param, now()->addMinute(), function () use ($url_param) {
        return (new ApiController)->detailshop('', $url_param, null);
    });

    // Check if detailshop has valid data.
    if (!empty($detailshop)) {
        // Update pid_mask and cleanurl_mask based on detailshop results.
        $pid_mask = $detailshop[0]['pid'];
        $cleanurl_mask = $url_param;

        // Clear the cart if needed for this pid.
        (new CartController)->checkClearCart($pid_mask);
    } else {
        // If no detailshop data, redirect to the bizapp URL.
        $error_status = 'yes';
        return redirect()->away($bizappurl);
        // Alternatively: return view('landing-lead')->with('error_status', $error_status);
    }

    // Get data using parallel API calls with caching for better performance
    $cachedData = $this->getHomeParallelCachedData($pid_mask, $cleanurl_mask);

    $getProductCollection = $cachedData['productCollection'] ?? [];
    $getHomeProductList = $cachedData['homeProductList'] ?? [];
    $getHappyHour = $cachedData['happyHour'] ?? [];
    $getTopSaleProduct = $cachedData['topSaleProduct'] ?? [];
    $getProfile = $cachedData['profile'] ?? [];
    $getRecord = $cachedData['record'] ?? [];

    // Return the view with the data, slicing arrays where necessary.
    return view('index')
        ->with('collectionList', array_slice($getProductCollection, 0, 4))
        ->with('happyhour', $getHappyHour)
        ->with('detailshop', $detailshop)
        ->with('url_mask', $cleanurl_mask)
        ->with('url_param', $cleanurl_mask)
        ->with('getProfile', $getProfile)
        ->with('recordList', $getRecord)
        ->with('topsaleproduct', array_slice($getTopSaleProduct, 0, 7))
        ->with('productList', array_slice($getHomeProductList, 0, 7));
}

    public function indexMaskAgent($url_param, $agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        // $agentpid = "0";

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;

            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                    // check if agent has bizappay account
                    if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }

                }
                else{
                    $agentpid = "";
                }

            }
            else{

                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        $getProductCollection = (new ApiController)->getProductCollection($pid_mask);
        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
        $getTopSaleProduct = (new ApiController)->getTopSaleProduct($pid_mask, $cleanurl_mask);
        $getProfile = (new ApiController)->getProfile($pid_mask);
        $getRecord = (new ApiController)->getRecord($pid_mask);

        return view('index')
            ->with('collectionList', array_slice($getProductCollection, 0, 4))
            ->with('happyhour', $getHappyHour)
            ->with('detailshop', $detailshop)
            ->with('url_mask', $cleanurl_mask)
            ->with('url_param', $cleanurl_mask)
            ->with('agentpid', $agentpid)
            ->with('getProfile', $getProfile)
            ->with('recordList', $getRecord)
            ->with('topsaleproduct', array_slice($getTopSaleProduct, 0, 7))
            ->with('productList', array_slice($getHomeProductList, 0, 7));
    }

    public function directory ()
    {
        // $url_mask = UrlMask::get();
        $url_mask = [];

        return view('directory', compact('url_mask'));
    }

    public function directoryEdit (Request $request)
    {
        $url_mask = UrlMask::find($request->id);

        if($url_mask)
        {
            UrlMask::where('id', $request->id)
                ->where('cleanurl', $request->cleanurl)
                ->update([
                    'url_param'        =>  $request->inputUrl,
                    'updated_at'    =>  Carbon::now()->format('Y-m-d'),
                ]);

            return true;
        }
        return false;
    }

    public function directoryLoginPost (Request $request)
    {
        if($request->domain == "bizapp.my" && $request->id_pengguna == "bizapp" && $request->katalaluan == "xs2root1")
        {
            $url_mask = UrlMask::get();

            return view('directory', compact('url_mask'));
        }

        $url = config('minishop.base_url');
        $loginAuth = Http::asForm()->post($url . 'api_name=TRACK_LOGIN&TX=', [
            'domain' => $request->domain,
            'username' => $request->id_pengguna,
            'password' => $request->katalaluan,
            'platform' => '',
            'regid' => ''
        ])->throw()->json();

        if($loginAuth[0]['STATUS'] == "1")
        {
            $url_mask = UrlMask::where('cleanurl', $request->id_pengguna)->get();

            return view('directory', compact('url_mask'));
        }
        else if ($loginAuth[0]['STATUS'] == "5")
        {
            return redirect()->route('directoryLogin')->with('danger', 'Akaun BIZAPP HQ anda telah tamat');
        }
        else
        {
            return redirect()->route('directoryLogin')->with('danger', 'Data tidak wujud di pangkalan data');
        }
    }

    public function claimcoupon (Request $request, $url_param)
    {
        $pid_mask = $request->pid_mask;

        // check shop first (privillege user)

        $url = config('minishop.base_url');
        $domainx = config('minishop.domain');
        // $url_mask = UrlMask::where('url_param', $url_param)->first();

        $customer_email = $request->emailclaimcoupon;

        $this->validate($request, [
            'emailclaimcoupon' => 'required|email:rfc,dns,filter,spoof',
          ]);

        Http::asForm()->post($url . 'api_name=TRACK_CLAIM_KUPON&TX=', [
            'pid' => $pid_mask,
            'customer_email' => $customer_email,
            'DOMAIN' => $domainx,
            'TOKEN' => 'aa',

        ])->throw()->json();


        return back()->with('claimcoupon', ' ');
    }

    // Collection
    public function koleksiToGrid ($url_param, $koleksiid)
    {
        PerformanceMonitor::start('collection_load');

        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        // Phase 1: Sequential dependent API calls with timeout
        try {
            $detailshop = Http::timeout(5)->asForm()->post(
                config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=',
                [
                    'pid' => '',
                    'cleanurl' => $url_param,
                    'TOKEN' => 'aa',
                    'DOMAIN' => config('minishop.domain'),
                    'agentpid' => null,
                ]
            )->throw()->json();
        } catch (\Exception $e) {
            // Fallback to original method if new approach fails
            $detailshop = (new ApiController)->detailshop('', $url_param, null);
        }
        if($detailshop != [] && isset($detailshop[0]['pid']))
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;

            (new CartController)->checkClearCart($pid_mask);

            // Continue with dependent calls (still sequential)
            try {
                $detailshopx = Http::timeout(5)->asForm()->post(
                    config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=',
                    [
                        'pid' => $pid_mask,
                        'cleanurl' => $url_param,
                        'TOKEN' => 'aa',
                        'DOMAIN' => config('minishop.domain'),
                        'agentpid' => $pid_mask,
                    ]
                )->throw()->json();
            } catch (\Exception $e) {
                // Fallback to original method
                $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            }

            $hasBizappay = (new ApiController)->checkHQBizappay(
                isset($detailshopx[0]['bizappayagent']) ? $detailshopx[0]['bizappayagent'] : 'N',
                $cleanurl_mask
            );
            // check if seller has bizappay account
            if($hasBizappay === true){
                $sortSelect = [
                    ["sort"=>"CHARLOWHIGH",
                    "text"=>"Alphabet (A-Z)" ],
                    ["sort"=>"CHARHIGHLOW",
                    "text"=>"Alphabet (Z-A)" ],
                    ["sort"=>"AMOUNTLOWHIGH",
                    "text"=>"Price (Low to High)" ],
                    ["sort"=>"AMOUNTHIGHLOW",
                    "text"=>"Price (High to Low)" ],
                    ["sort"=>"ENTRYOLDNEW",
                    "text"=>"Date (Old to New)" ],
                    ["sort"=>"ENTRYNEWOLD",
                    "text"=>"Date (New to Old)" ]
                ];

                // Temporarily use original sequential API calls (like homepage) for debugging
                $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
                $getShopCollection = (new ApiController)->getShopCollection($pid_mask);

                $getShopCollection === "" ? $getShopCollection = [] : $getShopCollection = $getShopCollection;

                array_push($getShopCollection, [
                    "id" => "0",
                    "koleksi" => "ALL COLLECTION",
                    "noofproducts" => ""
                ]);

                $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
                $getProfile = (new ApiController)->getProfile($pid_mask);
                // comment out get record for now to improve overall performance (up to 6 seconds loading time improvement)
                // $getRecord = (new ApiController)->getRecord($pid_mask);

                // Optimized data processing using collections
                $arrCollProd = [];
                if (!empty($getShopCollection) && !empty($getHomeProductList)) {
                    foreach ($getShopCollection as $sc) {
                        $arrCollProd[$sc['id']] = [];
                        foreach ($getHomeProductList as $hp) {
                            $arrSK = explode(',', $hp['senaraikoleksiid']);
                            if (in_array($sc['id'], $arrSK)) {
                                $arrCollProd[$sc['id']][] = $hp;
                            }
                        }
                    }
                }
                $arrCollProd[0] = $getHomeProductList;

                // Debug log for collections endpoint
                \Log::info('Collections endpoint data before rendering', [
                    'url_param' => $url_param,
                    'koleksiid' => $koleksiid,
                    'pid' => $pid_mask,
                    'products_count' => count($getHomeProductList),
                    'collections_count' => count($getShopCollection),
                    'profile_count' => is_array($getProfile) ? count($getProfile) : 0,
                    'profile_status' => isset($getProfile[0]['STATUS']) ? $getProfile[0]['STATUS'] : 'N/A',
                    'sample_products' => array_slice($getHomeProductList, 0, 2), // First 2 products for debugging
                    'arrCollProd_keys' => array_keys($arrCollProd),
                    'arrCollProd_0_count' => isset($arrCollProd[0]) ? count($arrCollProd[0]) : 0
                ]);

                PerformanceMonitor::end('collection_load', [
                    'url_param' => $url_param,
                    'koleksiid' => $koleksiid,
                    'pid' => $pid_mask,
                    'products_count' => count($getHomeProductList),
                    'collections_count' => count($getShopCollection)
                ]);

                return view('shop-grid')
                    ->with('collectionList', $getShopCollection)
                    ->with('sortSelect', $sortSelect)
                    ->with('url_param', $cleanurl_mask)
                    ->with('url_mask', $cleanurl_mask)
                    ->with('productList', $getHomeProductList)
                    ->with('getProfile', $getProfile)
                    ->with('recordList', [])
                    ->with('happyhour', $getHappyHour)
                    ->with('arrCollProd', $arrCollProd)
                    ->with('pid', $pid_mask)
                    ->with('temp', $koleksiid);
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else
        {
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    // Collection Affiliate
    public function koleksiToGridAgent ($url_param, $koleksiid, $agent_pid = null)
    {
        PerformanceMonitor::start('collection_agent_load');

        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [] && isset($detailshop[0]['pid']))
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;

            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{

                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        $sortSelect = [
            ["sort"=>"CHARLOWHIGH",
            "text"=>"Alphabet (A-Z)" ],
            ["sort"=>"CHARHIGHLOW",
            "text"=>"Alphabet (Z-A)" ],
            ["sort"=>"AMOUNTLOWHIGH",
            "text"=>"Price (Low to High)" ],
            ["sort"=>"AMOUNTHIGHLOW",
            "text"=>"Price (High to Low)" ],
            ["sort"=>"ENTRYOLDNEW",
            "text"=>"Date (Old to New)" ],
            ["sort"=>"ENTRYNEWOLD",
            "text"=>"Date (New to Old)" ]
        ];

        // Temporarily use original sequential API calls for debugging
        $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
        $getShopCollection = (new ApiController)->getShopCollection($pid_mask);

        $getShopCollection === "" ? $getShopCollection = [] : $getShopCollection = $getShopCollection;

        array_push($getShopCollection, [
            "id" => "0",
            "koleksi" => "ALL COLLECTION",
            "noofproducts" => ""
        ]);

        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        $getProfile = (new ApiController)->getProfile($pid_mask);
        $getRecord = (new ApiController)->getRecord($pid_mask);

        // Optimized data processing using collections
        $arrCollProd = [];
        if (!empty($getShopCollection) && !empty($getHomeProductList)) {
            foreach ($getShopCollection as $sc) {
                $arrCollProd[$sc['id']] = [];
                foreach ($getHomeProductList as $hp) {
                    $arrSK = explode(',', $hp['senaraikoleksiid']);
                    if (in_array($sc['id'], $arrSK)) {
                        $arrCollProd[$sc['id']][] = $hp;
                    }
                }
            }
        }
        $arrCollProd[0] = $getHomeProductList;

        PerformanceMonitor::end('collection_agent_load', [
            'url_param' => $url_param,
            'koleksiid' => $koleksiid,
            'agent_pid' => $agent_pid,
            'pid' => $pid_mask,
            'products_count' => count($getHomeProductList),
            'collections_count' => count($getShopCollection)
        ]);

        return view('shop-grid')
            ->with('collectionList', $getShopCollection)
            ->with('sortSelect', $sortSelect)
            ->with('url_param', $cleanurl_mask)
            ->with('url_mask', $cleanurl_mask)
            ->with('agent_pid', $agent_pid)
            ->with('agentpid', $agentpid)
            ->with('productList', $getHomeProductList)
            ->with('getProfile', $getProfile)
            ->with('recordList', $getRecord)
            ->with('happyhour', $getHappyHour)
            ->with('arrCollProd', $arrCollProd)
            ->with('temp', $koleksiid);
    }

    /**
     * Get data using parallel API calls with caching for collections
     */
    private function getCollectionParallelCachedData($pid_mask, $cleanurl_mask)
    {
        PerformanceMonitor::start('collection_parallel_api_calls');

        $cacheKeys = [
            'shopCollection' => "shop_collection_{$pid_mask}",
            'productList' => "product_list_{$pid_mask}_{$cleanurl_mask}",
            'profile' => "profile_{$pid_mask}",
            'happyHour' => "happy_hour_{$cleanurl_mask}",
        ];

        // Check what's already cached
        $cached = [];
        $needsFetch = [];

        foreach($cacheKeys as $key => $cacheKey) {
            $data = Cache::get($cacheKey);
            if($data && is_array($data)) {
                $cached[$key] = $data;
            } else {
                $needsFetch[] = $key;
                // Initialize with empty array as fallback
                $cached[$key] = [];
            }
        }

        // Fetch missing data in parallel
        if(!empty($needsFetch)) {
            try {
                $responses = Http::pool(function (Pool $pool) use ($needsFetch, $pid_mask, $cleanurl_mask) {
                    $calls = [];

                    if(in_array('shopCollection', $needsFetch)) {
                        $calls['shopCollection'] = $pool->timeout(5)->asForm()->post(
                            'https://corrad.visionice.net/bizapp/apigenerator_VERSIX.php?api_name=TRACK_GET_LIST_KOLEKSI_MINISHOPWITHITEMONLY&TX=',
                            [
                                'pid' => $pid_mask,
                                'TOKEN' => 'aa',
                                'DOMAIN' => config('minishop.domain')
                            ]
                        );
                    }

                if(in_array('productList', $needsFetch)) {
                    $calls['productList'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=',
                        [
                            'pid' => $pid_mask,
                            'cleanurl' => $cleanurl_mask,
                            'DOMAIN' => config('minishop.domain'),
                            'TOKEN' => 'aa',
                            'productid' => '',
                            'sorttype' => '',
                            'keyword' => '',
                            'filterkoleksi' => ''
                        ]
                    );
                }

                if(in_array('profile', $needsFetch)) {
                    $calls['profile'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GET_PROFILE&TX=',
                        [
                            'pid' => $pid_mask,
                            'TOKEN' => 'aa',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );
                }

                if(in_array('happyHour', $needsFetch)) {
                    $calls['happyHour'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_HAPPYHOUR&TX=',
                        [
                            'cleanurl' => $cleanurl_mask,
                            'TOKEN' => 'aa',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );
                }

                return $calls;
            });

            // Cache the fresh data (2 minute cache)
            foreach($responses as $key => $response) {
                // Ensure the key exists in our cache keys mapping
                if(!isset($cacheKeys[$key])) {
                    continue; // Skip unknown keys
                }

                if($response && $response->successful()) {
                    $data = $response->json();
                    // Ensure data is valid before caching
                    if($data !== null && is_array($data)) {
                        Cache::put($cacheKeys[$key], $data, now()->addMinutes(10));
                        $cached[$key] = $data;
                    } else {
                        // Invalid response data, use empty array
                        $cached[$key] = [];
                    }
                } else {
                    // Fallback to empty array if API fails
                    $cached[$key] = [];
                }
            }
            } catch (\Exception $e) {
                // If parallel calls fail, set empty arrays for missing data
                foreach($needsFetch as $key) {
                    $cached[$key] = [];
                }
            }
        }

        PerformanceMonitor::end('collection_parallel_api_calls', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'cache_hits' => count($cached) - count($needsFetch),
            'api_calls_made' => count($needsFetch)
        ]);

        return $cached;
    }

    /**
     * Get data using parallel API calls with caching for home page
     */
    private function getHomeParallelCachedData($pid_mask, $cleanurl_mask)
    {
        PerformanceMonitor::start('home_parallel_api_calls');

        $cacheKeys = [
            'productCollection' => "productCollection_{$pid_mask}",
            'homeProductList' => "homeProductList_{$pid_mask}_{$cleanurl_mask}",
            'happyHour' => "happyHour_{$cleanurl_mask}",
            'topSaleProduct' => "topSaleProduct_{$pid_mask}_{$cleanurl_mask}",
            'profile' => "profile_{$pid_mask}",
            'record' => "record_{$pid_mask}",
        ];

        // Check what's already cached
        $cached = [];
        $needsFetch = [];

        foreach($cacheKeys as $key => $cacheKey) {
            $data = Cache::get($cacheKey);
            if($data && is_array($data)) {
                $cached[$key] = $data;
            } else {
                $needsFetch[] = $key;
                // Initialize with empty array as fallback
                $cached[$key] = [];
            }
        }

        // Fetch missing data in parallel
        if(!empty($needsFetch)) {
            try {
                $responses = Http::pool(function (Pool $pool) use ($needsFetch, $pid_mask, $cleanurl_mask) {
                    $calls = [];

                    if(in_array('productCollection', $needsFetch)) {
                        $calls['productCollection'] = $pool->timeout(5)->asForm()->post(
                            config('minishop.base_url') . 'api_name=TRACK_GET_LIST_KOLEKSI_MINISHOPWITHITEMONLY&TX=',
                            [
                                'pid' => $pid_mask,
                                'TOKEN' => 'aa',
                                'DOMAIN' => config('minishop.domain')
                            ]
                        );
                    }

                    if(in_array('homeProductList', $needsFetch)) {
                        $calls['homeProductList'] = $pool->timeout(5)->asForm()->post(
                            config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=',
                            [
                                'pid' => $pid_mask,
                                'cleanurl' => $cleanurl_mask,
                                'DOMAIN' => config('minishop.domain'),
                                'TOKEN' => 'aa',
                                'productid' => '',
                                'sorttype' => 'LATEST',
                                'keyword' => '',
                                'filterkoleksi' => ''
                            ]
                        );
                    }

                    if(in_array('happyHour', $needsFetch)) {
                        $calls['happyHour'] = $pool->timeout(5)->asForm()->post(
                            config('minishop.base_url') . 'api_name=TRACK_GETHAPYHOURINFO&TX=',
                            [
                                'penggunaid' => $cleanurl_mask,
                                'DOMAIN' => config('minishop.domain')
                            ]
                        );
                    }

                    if(in_array('topSaleProduct', $needsFetch)) {
                        $calls['topSaleProduct'] = $pool->timeout(5)->asForm()->post(
                            config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=',
                            [
                                'pid' => $pid_mask,
                                'cleanurl' => $cleanurl_mask,
                                'DOMAIN' => config('minishop.domain'),
                                'TOKEN' => 'aa',
                                'productid' => '',
                                'sorttype' => 'TOPSALES',
                                'keyword' => '',
                                'filterkoleksi' => ''
                            ]
                        );
                    }

                    if(in_array('profile', $needsFetch)) {
                        $calls['profile'] = $pool->timeout(5)->asForm()->post(
                            config('minishop.base_url') . 'api_name=TRACK_GET_PROFILE&TX=',
                            [
                                'pid' => $pid_mask,
                                'TOKEN' => 'aa',
                                'DOMAIN' => config('minishop.domain')
                            ]
                        );
                    }

                    if(in_array('record', $needsFetch)) {
                        $calls['record'] = $pool->timeout(5)->asForm()->post(
                            'https://corrad.visionice.net/bizapp/apigenerator.php?api_name=TRACK_LIST_DONETRACKINGNO&TX==',
                            [
                                'pid' => $pid_mask,
                                'TOKEN' => 'aa',
                                'start' => '0',
                                'loadsemua' => 'NO',
                                'jenissort' => '0',
                                'sk' => '',
                                'DOMAIN' => config('minishop.domain')
                            ]
                        );
                    }

                    return $calls;
                });

                // Cache the fresh data (5 minute cache for better performance)
                foreach($responses as $key => $response) {
                    // Ensure the key exists in our cache keys mapping
                    if(!isset($cacheKeys[$key])) {
                        continue; // Skip unknown keys
                    }

                    if($response && $response->successful()) {
                        $data = $response->json();
                        // Ensure data is valid before caching
                        if($data !== null && is_array($data)) {
                            Cache::put($cacheKeys[$key], $data, now()->addMinutes(5));
                            $cached[$key] = $data;
                        } else {
                            // Invalid response data, use empty array
                            $cached[$key] = [];
                        }
                    } else {
                        // Fallback to empty array if API fails
                        $cached[$key] = [];
                    }
                }
            } catch (\Exception $e) {
                // If parallel calls fail, set empty arrays for missing data
                foreach($needsFetch as $key) {
                    $cached[$key] = [];
                }
                \Log::error('Home parallel API calls failed', [
                    'error' => $e->getMessage(),
                    'pid' => $pid_mask,
                    'cleanurl' => $cleanurl_mask
                ]);
            }
        }

        // Debug log the cached data
        \Log::info('Home Parallel API Cache Results', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'cache_hits' => count($cached) - count($needsFetch),
            'api_calls_made' => count($needsFetch),
            'profile_data' => $cached['profile'] ?? 'not_set',
            'profile_count' => isset($cached['profile']) && is_array($cached['profile']) ? count($cached['profile']) : 0
        ]);

        PerformanceMonitor::end('home_parallel_api_calls', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'cache_hits' => count($cached) - count($needsFetch),
            'api_calls_made' => count($needsFetch)
        ]);

        return $cached;
    }
}
