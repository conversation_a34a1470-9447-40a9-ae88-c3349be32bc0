<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Redirect;

class LandingController extends Controller
{
    public function landingView ()
    {
        $error_status = 'no';
        return view('landing-lead')->with('error_status', $error_status);
    }

    public function landingSubmit (Request $request)
    {
        $error_status = "no";
        // check shop first (privillege user)
        $detailshop = (new ApiController)->detailshop('', $request->url_param, null);

        if($detailshop != [])
        {
            return redirect()->route('homeMask', [$request->url_param]);
        }
        else
        {
            $error_status = 'yes';
            return view('landing-lead')->with('error_status', $error_status);
        }
    }
}
