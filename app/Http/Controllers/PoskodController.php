<?php

namespace App\Http\Controllers;

use App\Models\State;
use App\Models\Poskod;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PoskodController extends Controller
{
    public function getPoskod ($poskod)
    {
        // $state_code = Poskod::where('poskod', $poskod)->pluck('state_code')->first();
        // $state_name = State::where('state_code', $state_code)->pluck('state_name')->first();

        // $getPoskod['state_code'] = $state_code;
        // $getPoskod['state_name'] = $state_name;

        $postal = DB::table('postcode')
                    ->select('bandar', 'state_code', 'country_code')
                    ->where('poskod', '=', $poskod)
                    ->first();

        $getPoskod['postal'] = $postal;
        if(isset($postal))
        {
            $country = Country::where('country_code', $postal->country_code)->first();
            $getPoskod['country'] = $country->country_name;
        }
        else
        {
            $getPoskod['country'] = null;
        }

        if(isset($postal->state_code))
        {
            $state = State::where('state_code', $postal->state_code)->first();
            $getPoskod['state'] = $state->state_name;
            $getPoskod['bandar'] = $postal->bandar;
        }
        else
        {
            $getPoskod['state'] = null;
            $getPoskod['bandar'] = null;
        }

        // dd($getPoskod);

        return response()->json($getPoskod);
    }
}
