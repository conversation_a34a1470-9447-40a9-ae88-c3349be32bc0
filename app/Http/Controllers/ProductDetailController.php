<?php

namespace App\Http\Controllers;

class ProductDetailController extends Controller
{
    public function indexMask($url_param, $productid)
    {
        // get from another controller

        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        $detailshop = (new ApiController)->detailshop('', $url_param, null);
        if($detailshop != []){
            // proceed
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            $hasBizappay = (new ApiController)->checkHQBizappay($detailshopx[0]['bizappayagent'], $cleanurl_mask);
            
            // check if seller has bizappay account
            if($hasBizappay === true){
                $getProductDetail = (new ApiController)->getProductDetail($productid, $pid_mask);
                
                if($getProductDetail === []){
                    return redirect()->route('homeMask', $cleanurl_mask)->with('productnotfound','');
                }
                $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
                $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
                $getProfile = (new ApiController)->getProfile($pid_mask);

                // check if productID have been changed, so url not valid
                // we redirect to shop-grid
                if($getProductDetail === []){
                    return redirect()->route('koleksi-to-grid', ['url_param' => $cleanurl_mask, 'koleksiid' => 'all-collection']);
                } else{
                    return view('product-detail')
                    ->with('productDetail',$getProductDetail[0])
                    ->with('url_param', $cleanurl_mask)
                    ->with('url_mask', $cleanurl_mask)
                    ->with('getProfile', $getProfile)
                    ->with('happyhour', $getHappyHour)
                    ->with('productList',array_slice($getHomeProductList,0,4))
                    ->with('temp', "0");
                }
                
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else{
            // user not exist
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    public function indexMaskAgent($url_param, $productid, $agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [])
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                    $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{
                    
                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        // even affiliate, passkan HQ ID untuk display harga ikut ID HQ
        $getProductDetail = (new ApiController)->getProductDetail($productid, $pid_mask);
        if($getProductDetail === []){
            return redirect()->route('homeMask', $cleanurl_mask)->with('productnotfound','');
        }
        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
        $getProfile = (new ApiController)->getProfile($pid_mask);

        // check if productID have been changed, so url not valid
        // we redirect to shop-grid
        if($getProductDetail === []){
            return redirect()->route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agent_pid]);
        } else{
            return view('product-detail')
            ->with('productDetail',$getProductDetail[0])
            ->with('url_param', $cleanurl_mask)
            ->with('url_mask', $cleanurl_mask)
            ->with('agentpid', $agentpid)
            ->with('getProfile', $getProfile)
            ->with('happyhour', $getHappyHour)
            ->with('productList',array_slice($getHomeProductList,0,4))
            ->with('temp', "0");
        }
        
    }
}
