<?php

namespace App\Http\Controllers;

use App\Http\Controllers\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Client\Pool;
use App\Helpers\PerformanceMonitor;


class ShopGridController extends Controller
{
    public function indexMask ($url_param)
    {
        PerformanceMonitor::start('shop_grid_load');

        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        // Phase 1: Sequential dependent API calls with timeout
        try {
            $detailshop = Http::timeout(5)->asForm()->post(
                config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=',
                [
                    'pid' => '',
                    'cleanurl' => $url_param,
                    'TOKEN' => 'aa',
                    'DOMAIN' => config('minishop.domain'),
                    'agentpid' => null,
                ]
            )->throw()->json();
        } catch (\Exception $e) {
            // Fallback to original method if new approach fails
            $detailshop = (new ApiController)->detailshop('', $url_param, null);
        }

        if($detailshop != [] && isset($detailshop[0]['pid']))
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;

            (new CartController)->checkClearCart($pid_mask);

            // Continue with dependent calls (still sequential)
            try {
                $detailshopx = Http::timeout(5)->asForm()->post(
                    config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=',
                    [
                        'pid' => $pid_mask,
                        'cleanurl' => $url_param,
                        'TOKEN' => 'aa',
                        'DOMAIN' => config('minishop.domain'),
                        'agentpid' => $pid_mask,
                    ]
                )->throw()->json();
            } catch (\Exception $e) {
                // Fallback to original method
                $detailshopx = (new ApiController)->detailshop($pid_mask, $url_param, $pid_mask);
            }

            $hasBizappay = (new ApiController)->checkHQBizappay(
                isset($detailshopx[0]['bizappayagent']) ? $detailshopx[0]['bizappayagent'] : 'N',
                $cleanurl_mask
            );
            
            // check if seller has bizappay account
            if($hasBizappay === true){
                $sortSelect = [
                    ["sort"=>"CHARLOWHIGH",
                    "text"=>"Alphabet (A-Z)" ],
                    ["sort"=>"CHARHIGHLOW",
                    "text"=>"Alphabet (Z-A)" ],
                    ["sort"=>"AMOUNTLOWHIGH",
                    "text"=>"Price (Low to High)" ],
                    ["sort"=>"AMOUNTHIGHLOW",
                    "text"=>"Price (High to Low)" ],
                    ["sort"=>"ENTRYOLDNEW",
                    "text"=>"Date (Old to New)" ],
                    ["sort"=>"ENTRYNEWOLD",
                    "text"=>"Date (New to Old)" ]
                ];
        
                // Phase 2: Parallel independent API calls with caching
                $cachedData = $this->getParallelCachedData($pid_mask, $cleanurl_mask);

                $getHappyHour = $cachedData['happyHour'] ?? [];
                $getShopCollection = $cachedData['shopCollection'] ?? [];

                $getShopCollection === "" ? $getShopCollection = [] : $getShopCollection = $getShopCollection;

                array_push($getShopCollection, [
                    "id" => "0",
                    "koleksi" => "ALL COLLECTION",
                    "noofproducts" => ""
                ]);

                $getHomeProductList = $cachedData['productList'] ?? [];
                $getProfile = $cachedData['profile'] ?? [];
                $getRecord = $cachedData['record'] ?? [];

                // Fallback: If profile is empty, try the original API method
                if (empty($getProfile)) {
                    \Log::info('Profile data empty, trying fallback API call', ['pid' => $pid_mask]);
                    try {
                        $getProfile = (new ApiController)->getProfile($pid_mask);
                        \Log::info('Fallback profile API result', [
                            'pid' => $pid_mask,
                            'profile_count' => is_array($getProfile) ? count($getProfile) : 0,
                            'profile_data' => $getProfile
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Fallback profile API failed', [
                            'pid' => $pid_mask,
                            'error' => $e->getMessage()
                        ]);
                        $getProfile = [];
                    }
                }
        
                $arrCollProd = array();
                $arrSK = array();
        
                foreach ($getShopCollection as $sc)
                {
                    foreach ($getHomeProductList as $hp)
                    {
                        $arrSK = explode(',', $hp['senaraikoleksiid']);
        
                        if(count($arrSK) > 1)
                        {
                            foreach($arrSK as $ask)
                            {
                                if($ask === $sc['id'])
                                {
                                    $arrCollProd[$sc['id']][] = $hp;
                                }
                            }
                        }
                        else
                        {
                            if($hp['senaraikoleksiid'] === $sc['id'])
                            {
                                $arrCollProd[$sc['id']][] = $hp;
                            }
                        }
                    }
                }
                $arrCollProd[0] = $getHomeProductList;
        
                // Final debug log before rendering view
                \Log::info('Final data before rendering shop-grid view', [
                    'url_param' => $url_param,
                    'pid' => $pid_mask,
                    'profile_count' => is_array($getProfile) ? count($getProfile) : 0,
                    'profile_status' => isset($getProfile[0]['STATUS']) ? $getProfile[0]['STATUS'] : 'N/A',
                    'profile_has_data' => !empty($getProfile),
                    'products_count' => count($getHomeProductList),
                    'collections_count' => count($getShopCollection)
                ]);

                PerformanceMonitor::end('shop_grid_load', [
                    'url_param' => $url_param,
                    'pid' => $pid_mask,
                    'products_count' => count($getHomeProductList),
                    'collections_count' => count($getShopCollection)
                ]);

                return view('shop-grid')
                    ->with('collectionList', $getShopCollection)
                    ->with('sortSelect', $sortSelect)
                    ->with('url_param', $cleanurl_mask)
                    ->with('url_mask', $cleanurl_mask)
                    ->with('productList', $getHomeProductList)
                    ->with('getProfile', $getProfile)
                    ->with('recordList', $getRecord)
                    ->with('happyhour', $getHappyHour)
                    ->with('arrCollProd', $arrCollProd)
                    ->with('pid', $pid_mask);
            }
            else{
                return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
            }
        }
        else
        {
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }
    }

    public function indexMaskAgent ($url_param ,$agent_pid = null)
    {
        $pid_mask = config('minishop.default_pid');
        $cleanurl_mask = config('minishop.default_cleanurl');
        $bizappurl = config('minishop.bizappurl');

        if(isset($agent_pid)){
            $agent_pid = $agent_pid;
        } else{
            $agent_pid = "";
        }

        // get from another controller
        $checkAgent = (new ApiController)->affiliate($agent_pid);
        $detailshop = (new ApiController)->detailshop('', $url_param, $agent_pid);

        if($detailshop != [] && isset($detailshop[0]['pid']))
        {
            $pid_mask = $detailshop[0]['pid'];
            $cleanurl_mask = $url_param;
            
            (new CartController)->checkClearCart($pid_mask);

            // dah follow
            if($checkAgent != []){
                // dd($checkAgent[2]['stokistpid']);

                $i = 0;
                $aa = '';
                do{
                     $aa = $checkAgent[$i]['stokistpid'];
                    $i++;

                    if($i == count($checkAgent)){
                        break;
                    }
                }
                while($checkAgent[$i-1]['stokistpid'] != $pid_mask);

                if($aa == $pid_mask){
                    // store agentpid to session
                    $hasAffiliate = session()->get('affiliate');
                    $agentpid = $agent_pid;

                     // check if agent has bizappay account
                     if($detailshop[0]['bizappayagent'] == "N"){
                        return redirect()->route('homeMask', $cleanurl_mask)->with('agentnobizappay', ' ');
                    }
                }
                else{
                    $agentpid = "";
                }

            }
            else{
                    
                $agentpid = "";
                return redirect()->route('homeMask', [$cleanurl_mask, $agentpid]);
            }
        }
        else
        {
            $agentpid = "";
            $error_status = 'yes';
            return redirect()->away($bizappurl);
            // return view('landing-lead')->with('error_status', $error_status);
        }

        $sortSelect = [
            ["sort"=>"CHARLOWHIGH",
            "text"=>"Alphabet (A-Z)" ],
            ["sort"=>"CHARHIGHLOW",
            "text"=>"Alphabet (Z-A)" ],
            ["sort"=>"AMOUNTLOWHIGH",
            "text"=>"Price (Low to High)" ],
            ["sort"=>"AMOUNTHIGHLOW",
            "text"=>"Price (High to Low)" ],
            ["sort"=>"ENTRYOLDNEW",
            "text"=>"Date (Old to New)" ],
            ["sort"=>"ENTRYNEWOLD",
            "text"=>"Date (New to Old)" ]
        ];

        $getHappyHour = (new ApiController)->getHappyHour($cleanurl_mask);
        $getShopCollection = (new ApiController)->getShopCollection($pid_mask);

        $getShopCollection === "" ? $getShopCollection = [] : $getShopCollection = $getShopCollection;

        array_push($getShopCollection, [
            "id" => "0",
            "koleksi" => "ALL COLLECTION",
            "noofproducts" => ""
        ]);

        $getHomeProductList = (new ApiController)->getHomeProductList($pid_mask, $cleanurl_mask);
        $getProfile = (new ApiController)->getProfile($pid_mask);
        $getRecord = (new ApiController)->getRecord($pid_mask);

        $arrCollProd = array();
        $arrSK = array();

        foreach ($getShopCollection as $sc)
        {
            foreach ($getHomeProductList as $hp)
            {
                $arrSK = explode(',', $hp['senaraikoleksiid']);

                if(count($arrSK) > 1)
                {
                    foreach($arrSK as $ask)
                    {
                        if($ask === $sc['id'])
                        {
                            $arrCollProd[$sc['id']][] = $hp;
                        }
                    }
                }
                else
                {
                    if($hp['senaraikoleksiid'] === $sc['id'])
                    {
                        $arrCollProd[$sc['id']][] = $hp;
                    }
                }
            }
        }
        $arrCollProd[0] = $getHomeProductList;

        return view('shop-grid')
            ->with('collectionList', $getShopCollection)
            ->with('sortSelect', $sortSelect)
            ->with('url_param', $cleanurl_mask)
            ->with('url_mask', $cleanurl_mask)
            ->with('agent_pid', $agent_pid)
            ->with('agentpid', $agentpid)
            ->with('productList', $getHomeProductList)
            ->with('getProfile', $getProfile)
            ->with('recordList', $getRecord)
            ->with('happyhour', $getHappyHour)
            ->with('arrCollProd', $arrCollProd)
            ->with('pid', $pid_mask);
    }

    // shop-grid add to cart function
    public function shopGridAtc(Request $request)
    {
        $url_param = $request->input('url_param');
        $productid = $request->input('product_id');
        $pid_mask = $request->input('pid_mask'); // 375270 

        $getProductDetail = (new ApiController)->getProductDetail($productid, $pid_mask);
        // dd($getProductDetail);

        $quantity = "1";

        $cart = session()->get('cart');

        // Check if product detail is valid
        if(empty($getProductDetail) || !isset($getProductDetail[0])) {
            return response()->json(['success' => false, 'errorAtc' => 'Product not found or invalid product data']);
        }

        // check if quantity is more than stock count
        if($getProductDetail[0]["statusstok"] == 'N' && (int)$quantity > (int)$getProductDetail[0]["bilstok"]){

            return response()->json(['success' => true, 'errorAtc' => 'The quantity entered exceeded the stock count for this item. Please adjust your quantity']);
        }
        else {
            $decodedCart = $getProductDetail[0];

            // if cart is empty then this the first product
            if(!$cart) {

                $finalprice = 0;
                $hasDiscount = "N";
                $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart["pid"], $decodedCart["id"], $quantity);

                // check dulu discount by quantity
                if($disQuantity === "" || $disQuantity === null){
                    $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $finalprice = number_format($decodedCart["promotionprice"], 2) : $finalprice = number_format($decodedCart["price"], 2);
                    $hasDiscount = "N";
                } 
                else{
                    $finalprice = $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $decodedCart["promotionprice"] - $disQuantity : $decodedCart["price"] - $disQuantity;
                    $hasDiscount = "Y";
                }

                $cart = [
                    $decodedCart["id"] => [
                        "id" => $decodedCart["id"],
                        "name" => $decodedCart["productname"],
                        "quantity" => $quantity,
                        "price" => $finalprice,
                        "agentprice" => number_format($decodedCart["agentprice2U"], 2),
                        "photo" => $decodedCart["attachment"],
                        "weight" => $decodedCart["weight"],
                        "hq_url" => $url_param,
                        "hq_pid" => $decodedCart["pid"],
                        "max_qty" => str_replace(',', '', $decodedCart["bilstok"]),
                        "discount" => 0,
                        "hasdiscount" => $hasDiscount,
                        "priceasal" => $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? number_format($decodedCart["promotionprice"], 2) : number_format($decodedCart["price"], 2),
                    ]
                ];

                // reset discount
                $cart[$decodedCart["id"]]["discount"] = 0;
                session()->put('cart', $cart);

                $cartItemCount = count($cart);

                return response()->json(['success' => true, 'successAtc' => 'Product added to cart successfully!', 'cartItemCount' => $cartItemCount]);
            }

            // check if cart items more than 20
            if(count($cart) <= 20 ){

                // if cart not empty then check if this product exist then increment quantity
                if(isset($cart[$decodedCart["id"]])) {

                    $quantityTemp = $cart[$decodedCart["id"]]['quantity'] + $quantity;

                    $finalprice = 0;
                    $hasDiscount = "N";
                    $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart["pid"], $decodedCart["id"], $quantityTemp);

                    // check dulu discount by quantity
                    if($disQuantity === "" || $disQuantity === null){
                        $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $finalprice = number_format($decodedCart["promotionprice"], 2) : $finalprice = number_format($decodedCart["price"], 2);
                        $hasDiscount = "N";
                    } 
                    else{
                        $finalprice = $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $decodedCart["promotionprice"] - $disQuantity : $decodedCart["price"] - $disQuantity;
                        $hasDiscount = "Y";
                    }

                    // simpan balik dalam array product terlibat
                    $cart[$decodedCart["id"]]['quantity'] = $cart[$decodedCart["id"]]['quantity'] + $quantity;
                    $cart[$decodedCart["id"]]['price'] =  $finalprice;
                    $cart[$decodedCart["id"]]['hasdiscount'] =  $hasDiscount;


                    // reset discount
                    $cart[$decodedCart["id"]]["discount"] = 0;
                    session()->put('cart', $cart);

                    $cartItemCount = count($cart);
                    
                    return response()->json(['success' => true, 'successAtc' => 'Product added to cart successfully!', 'cartItemCount' => $cartItemCount]);
                } 
                else {
                    $finalprice = 0;
                    $hasDiscount = "N";
                    $disQuantity = (new ApiController)->getDiscountByQuantity($decodedCart["pid"], $decodedCart["id"], $quantity);
    
                    // check dulu discount by quantity
                    if($disQuantity === "" || $disQuantity === null){
                        $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $finalprice = number_format($decodedCart["promotionprice"], 2) : $finalprice = number_format($decodedCart["price"], 2);
                        $hasDiscount = "N";
                    } 
                    else{
                        $finalprice = $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? $decodedCart["promotionprice"] - $disQuantity : $decodedCart["price"] - $disQuantity;
                        $hasDiscount = "Y";
                    }

                    $cart[$decodedCart["id"]] = [
                            "id" => $decodedCart["id"],
                            "name" => $decodedCart["productname"],
                            "quantity" => $quantity,
                            "price" => $finalprice,
                            "agentprice" => number_format($decodedCart["agentprice2U"], 2),
                            "photo" => $decodedCart["attachment"],
                            "weight" => $decodedCart["weight"],
                            "hq_url" => $url_param,
                            "hq_pid" => $decodedCart["pid"],
                            "max_qty" => str_replace(',', '', $decodedCart["bilstok"]),
                            "discount" => 0,
                            "hasdiscount" => $hasDiscount,
                            "priceasal" => $decodedCart["PROMOSIHAPPYHOUR"] === "Y" ? number_format($decodedCart["promotionprice"], 2) : number_format($decodedCart["price"], 2),
                    ];

                }
                // reset discount
                $cart[$decodedCart["id"]]["discount"] = 0;
                session()->put('cart', $cart);

                $cartItemCount = count($cart);

                // Clear multiple cookies
                Cookie::queue(Cookie::forget('totalSaved'));
                Cookie::queue(Cookie::forget('appliedCouponCode'));
                Cookie::queue(Cookie::forget('kuponid'));
                
                return response()->json(['success' => true, 'successAtc' => 'Product added to cart successfully!', 'cartItemCount' => $cartItemCount]);
            } 
            else {                
                return response()->json(['success' => true, 'errorAtc' => 'Your cart has reached 20 items limit, please remove unwanted items or proceed to checkout']);
            }
        }
    }

    /**
     * Get data using parallel API calls with caching
     */
    private function getParallelCachedData($pid_mask, $cleanurl_mask)
    {
        PerformanceMonitor::start('parallel_api_calls');

        $cacheKeys = [
            'shopCollection' => "shop_collection_{$pid_mask}",
            'productList' => "product_list_{$pid_mask}_{$cleanurl_mask}",
            'profile' => "profile_{$pid_mask}",
            'record' => "record_{$pid_mask}",
            'happyHour' => "happy_hour_{$cleanurl_mask}",
        ];

        // Check what's already cached
        $cached = [];
        $needsFetch = [];

        foreach($cacheKeys as $key => $cacheKey) {
            $data = Cache::get($cacheKey);
            if($data && is_array($data)) {
                $cached[$key] = $data;
            } else {
                $needsFetch[] = $key;
                // Initialize with empty array as fallback
                $cached[$key] = [];
            }
        }

        // Fetch missing data in parallel
        if(!empty($needsFetch)) {
            try {
                $apiController = new ApiController();
                $responses = Http::pool(function (Pool $pool) use ($needsFetch, $pid_mask, $cleanurl_mask, $apiController) {
                $calls = [];

                if(in_array('shopCollection', $needsFetch)) {
                    $calls['shopCollection'] = $pool->timeout(5)->asForm()->post(
                        'https://corrad.visionice.net/bizapp/apigenerator_VERSIX.php?api_name=TRACK_GET_LIST_KOLEKSI_MINISHOPWITHITEMONLY&TX=',
                        [
                            'pid' => $pid_mask,
                            'TOKEN' => 'aa',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );
                }

                if(in_array('productList', $needsFetch)) {
                    $calls['productList'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_DETAILS&TX=',
                        [
                            'pid' => $pid_mask,
                            'cleanurl' => $cleanurl_mask,
                            'DOMAIN' => config('minishop.domain'),
                            'TOKEN' => 'aa',
                            'productid' => '',
                            'sorttype' => '',
                            'keyword' => '',
                            'filterkoleksi' => ''
                        ]
                    );
                }

                if(in_array('profile', $needsFetch)) {
                    $calls['profile'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GET_PROFILE&TX=',
                        [
                            'pid' => $pid_mask,
                            'TOKEN' => 'aa',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );

                    // Log the profile API call details
                    \Log::info('Profile API Call in Parallel', [
                        'pid' => $pid_mask,
                        'url' => config('minishop.base_url') . 'api_name=TRACK_GET_PROFILE&TX=',
                        'domain' => config('minishop.domain')
                    ]);
                }

                if(in_array('record', $needsFetch)) {
                    $calls['record'] = $pool->timeout(5)->asForm()->post(
                        'https://corrad.visionice.net/bizapp/apigenerator.php?api_name=TRACK_LIST_DONETRACKINGNO&TX==',
                        [
                            'pid' => $pid_mask,
                            'TOKEN' => 'aa',
                            'start' => '0',
                            'loadsemua' => 'NO',
                            'jenissort' => '0',
                            'sk' => '',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );
                }

                if(in_array('happyHour', $needsFetch)) {
                    $calls['happyHour'] = $pool->timeout(5)->asForm()->post(
                        config('minishop.base_url') . 'api_name=TRACK_GETPERSONALSHOP_HAPPYHOUR&TX=',
                        [
                            'cleanurl' => $cleanurl_mask,
                            'TOKEN' => 'aa',
                            'DOMAIN' => config('minishop.domain')
                        ]
                    );
                }

                return $calls;
            });

            // Cache the fresh data (2 minute cache)
            foreach($responses as $key => $response) {
                // Ensure the key exists in our cache keys mapping
                if(!isset($cacheKeys[$key])) {
                    continue; // Skip unknown keys
                }

                if($response && $response->successful()) {
                    $data = $response->json();
                    // Ensure data is valid before caching
                    if($data !== null && is_array($data)) {
                        Cache::put($cacheKeys[$key], $data, now()->addMinutes(2));
                        $cached[$key] = $data;
                    } else {
                        // Invalid response data, use empty array
                        $cached[$key] = [];
                    }
                } else {
                    // Fallback to empty array if API fails
                    $cached[$key] = [];
                }
            }
            } catch (\Exception $e) {
                // If parallel calls fail, set empty arrays for missing data
                foreach($needsFetch as $key) {
                    $cached[$key] = [];
                }
            }
        }

        // Debug log the cached data
        \Log::info('Parallel API Cache Results', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'cache_hits' => count($cached) - count($needsFetch),
            'api_calls_made' => count($needsFetch),
            'profile_data' => $cached['profile'] ?? 'not_set',
            'profile_count' => isset($cached['profile']) && is_array($cached['profile']) ? count($cached['profile']) : 0
        ]);

        PerformanceMonitor::end('parallel_api_calls', [
            'pid' => $pid_mask,
            'cleanurl' => $cleanurl_mask,
            'cache_hits' => count($cached) - count($needsFetch),
            'api_calls_made' => count($needsFetch)
        ]);

        return $cached;
    }
}
