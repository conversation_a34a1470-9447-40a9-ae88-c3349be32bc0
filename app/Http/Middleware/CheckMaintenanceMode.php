<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class CheckMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = Http::asForm()->post(config('minishop.base_url'). 'api_name=TRACK_GETPERSONALSHOP_ONLY_DETAILS&TX=', [
            'pid' =>  session('pid'),
            'cleanurl' => session('cleanurl'),
            'TOKEN' => 'aa',
            'DOMAIN' => session('DOMAIN'),
            'agentpid' => session('agentpid'),
        ])->throw()->json();
        // dd($response);
        if(isset($response[0]['profilvisible'])){
            if($response[0]['profilvisible'] == "1"){
                return response()->view('maintenance', [], 503);
            }
        }    
        return $next($request);
    }
}
