<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UrlMask extends Model
{
    use HasFactory;

    protected $table = 'url_masking';

    protected $guarded = [
        'id'
    ];

    protected $fillable = [
    	'url_param',
    	'cleanurl',
		'pid'
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];
}
