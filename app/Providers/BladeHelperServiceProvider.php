<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\BladeHelper;

class BladeHelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register Blade directives for safe array access
        Blade::directive('safeGet', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::safeArrayGet($expression); ?>";
        });

        Blade::directive('safeFirst', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::safeFirstElement($expression); ?>";
        });

        Blade::directive('profileProp', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::getProfileProperty($expression); ?>";
        });

        Blade::directive('shopProp', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::getShopProperty($expression); ?>";
        });

        Blade::directive('happyProp', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::getHappyHourProperty($expression); ?>";
        });

        Blade::directive('formatAddress', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::formatAddress($expression); ?>";
        });

        Blade::directive('stateName', function ($expression) {
            return "<?php echo App\Helpers\BladeHelper::getStateName($expression); ?>";
        });
    }
}
