<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\UrlMask;

class UrlMaskingTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        UrlMask::create([
            'url_param' => 'minishop_kami',
            'cleanurl' => 'wowskin',
            'pid' => '1'
        ]);

        UrlMask::create([
            'url_param' => 'selamat_datang',
            'cleanurl' => 'vsfaiz',
            'pid' => '375270'
        ]);

        UrlMask::create([
            'url_param' => 'user_bizapp',
            'cleanurl' => 'syariergps',
            'pid' => '2078114'
        ]);
    }
}
