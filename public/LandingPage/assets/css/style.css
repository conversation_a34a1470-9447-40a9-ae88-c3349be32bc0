@charset "UTF-8";

/*-----------------------------------------------------------------------------------

	Theme Name : Ebo
	Theme URI  : http://themeforest.net/user/zytheme
	Description: Ebo is a pixel perfect creative html5 ebook landing page based on designed with great attention to details, flexibility and performance. It is ultra professional, smooth and sleek, with a clean modern layout.
	Author     : zytheme
	Author URI : http://themeforest.net/user/zytheme
	Version    : 2.1

-----------------------------------------------------------------------------------*/
/*!
// Contents
// ------------------------------------------------>

1.  Global Styles
2.  Typography
3.	Color
4.	Align
5.	Grid
6.	Background
7.	Buttons
8.	Forms
9.	Heading
10. Loading
11. Contact
12. Header
13. Menu
14. Clients
15. Hero
16. Video
17. Services
18. Thank You
19. Testimonial
20. Feature
21. Action
22. Pricing
23. Carousel
24. Landing

/*------------------------------------*\
    #GLOBAL STYLES
\*------------------------------------*/
/*
WARNING! DO NOT EDIT THIS FILE!

To make it easy to update your theme, you should not edit the styles in this file. Instead use 
the custom.css file to add your styles. You can copy a style from this file and paste it in 
custom.css and it will override the style in this file. You have been warned! :)
*/
body,
html {
	overflow-x: hidden;
}

html {
	font-size: 13px;
}

body {
	background-color: #ffffff;
	font-family     : "Ubuntu", sans-serif;
	font-size       : 13px;
	font-weight     : 400;
	color           : #aaaaaa;
	line-height     : 1.5;
	margin          : 0;
}

.wrapper {
	background-color: #f8f8f8;
}

* {
	outline: none;
}

::-moz-selection {
	text-shadow: none;
}

::selection {
	background-color: #49b970;
	color           : #ffffff;
	text-shadow     : none;
}

a {
	-webkit-transition: all 0.3s ease;
	-o-transition     : all 0.3s ease;
	transition        : all 0.3s ease;
	color             : #49b970;
}

a:hover {
	color          : #3a965a;
	text-decoration: none;
}

a:focus {
	color          : #3a965a;
	outline        : none;
	text-decoration: none;
}

a:active {
	color          : #3a965a;
	outline        : none;
	text-decoration: none;
}

textarea {
	resize: none;
}

button.btn {
	margin-bottom: 0;
}

.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn.focus:active,
.btn.active.focus {
	outline: none;
}

.modal-backdrop {
	z-index         : 1020;
	background-color: rgba(34, 34, 34, 0.95);
}

.fullscreen {
	height: 100vh !important;
}

/* Medium Devices, Desktops */
@media only screen and (max-width: 992px) {
	.fullscreen {
		height: auto !important;
	}
}

/*------------------------------------*\
    #TYPOGRAPHY
\*------------------------------------*/
/* Heading Text */
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
	color      : #333333;
	font-family: "Ubuntu", sans-serif;
	font-weight: 700;
	margin     : 0 0 28px;
	line-height: 1;
}

.h1,
h1 {
	font-size: 52px;
}

.h2,
h2 {
	font-size: 42px;
}

.h3,
h3 {
	font-size: 38px;
}

.h4,
h4 {
	font-size: 32px;
}

.h5,
h5 {
	font-size: 24px;
}

.h6,
h6 {
	font-size: 18px;
}

p {
	color      : #aaaaaa;
	font-size  : 13px;
	font-weight: 400;
	line-height: 22px;
}

.lead {
	font-size  : 16px;
	line-height: 1.8;
}

/* Aligning Text */
.text--left {
	text-align: left !important;
}

.text--right {
	text-align: right !important;
}

.text--center {
	text-align: center !important;
}

.text--just {
	text-align: justify !important;
}

.align--top {
	vertical-align: top;
}

.align--bottom {
	vertical-align: bottom;
}

.align--middle {
	vertical-align: middle;
}

.align--baseline {
	vertical-align: baseline;
}

/* Weight Text */
.bold {
	font-weight: bold;
}

.regular {
	font-weight: normal;
}

.italic {
	font-style: italic;
}

.break-word {
	word-wrap: break-word;
}

.no-wrap {
	white-space: nowrap;
}

/* Text Color */
.text-white {
	color: #ffffff !important;
}

.text-gray {
	color: #f9f9f9 !important;
}

.text-black {
	color: #333333;
}

.text-theme {
	color: #49b970;
}

.text--capitalize {
	text-transform: capitalize !important;
}

.text--uppercase {
	text-transform: uppercase !important;
}

.font-heading {
	font-family: "Ubuntu", sans-serif;
}

.font-body {
	font-family: "Ubuntu", sans-serif;
}

.font-18 {
	font-size: 24px;
}

.font-16 {
	font-size: 16px;
}

.font-20 {
	font-size: 20px;
}

.font-40 {
	font-size: 40px;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	text-center-xs {
		text-align: center !important;
	}
}

/* Small Devices, Tablets */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	text-center-sm {
		text-align: center !important;
	}
}

.higlighted-style1 {
	background-color: #49b970;
	color           : #fff;
	padding         : 5px 0;
}

.higlighted-style2 {
	background-color: #333333;
	color           : #fff;
	padding         : 5px 0;
}

.higlighted-style3 {
	background-color: #f9f9f9;
	color           : #49b970;
	padding         : 5px 0;
}

/*------------------------------------*\
    #COLORS
\*------------------------------------*/
.color-heading {
	color: #333333 !important;
}

.color-theme {
	color: #49b970 !important;
}

.color-white {
	color: #ffffff !important;
}

.color-gray {
	color: #f9f9f9 !important;
}

/*------------------------------------*\
    #Align
\*------------------------------------*/
address,
blockquote,
dd,
dl,
fieldset,
form,
ol,
p,
pre,
table,
ul {
	margin-bottom: 20px;
}

section {
	padding-top   : 100px;
	padding-bottom: 100px;
	overflow      : hidden;
}

/* All margin */
.m-0 {
	margin: 0 !important;
}

.m-xs {
	margin: 10px;
}

.m-sm {
	margin: 20px;
}

.m-md {
	margin: 40px;
}

.m-lg {
	margin: 80px;
}

/* top margin */
.mt-0 {
	margin-top: 0;
}

.mt-xs {
	margin-top: 10px;
}

.mt-10 {
	margin-top: 10px !important;
}

.mt-20 {
	margin-top: 20px !important;
}

.mt-30 {
	margin-top: 30px !important;
}

.mt-40 {
	margin-top: 40px !important;
}

.mt-50 {
	margin-top: 50px !important;
}

.mt-60 {
	margin-top: 60px;
}

.mt-70 {
	margin-top: 70px !important;
}

.mt-80 {
	margin-top: 80px !important;
}

.mt-90 {
	margin-top: 90px !important;
}

.mt-100 {
	margin-top: 100px !important;
}

.mt-150 {
	margin-top: 150px;
}

/* bottom margin */
.mb-0 {
	margin-bottom: 0 !important;
}

.mb-15 {
	margin-bottom: 15px;
}

.mb-10 {
	margin-bottom: 10px !important;
}

.mb-20 {
	margin-bottom: 20px !important;
}

.mb-30 {
	margin-bottom: 30px !important;
}

.mb-50 {
	margin-bottom: 50px !important;
}

.mb-40 {
	margin-bottom: 40px !important;
}

.mb-60 {
	margin-bottom: 60px !important;
}

.mb-70 {
	margin-bottom: 70px !important;
}

.mb-80 {
	margin-bottom: 80px !important;
}

.mb-90 {
	margin-bottom: 90px !important;
}

.mb-100 {
	margin-bottom: 100px !important;
}

.mb-150 {
	margin-bottom: 150px !important;
}

/* right margin */
.mr-0 {
	margin-right: 0;
}

.mr-30 {
	margin-right: 30px !important;
}

.mr-50 {
	margin-right: 50px;
}

.mr-60 {
	margin-right: 60px;
}

.mr-150 {
	margin-right: 150px;
}

/* left margin */
.ml-0 {
	margin-left: 0;
}

.ml-xs {
	margin-left: 10px;
}

.ml-sm {
	margin-left: 20px;
}

.ml-md {
	margin-left: 40px;
}

.ml-lg {
	margin-left: 80px;
}

.ml-30 {
	margin-left: 30px !important;
}

.ml-50 {
	margin-left: 50px;
}

.ml-60 {
	margin-left: 60px;
}

.ml-150 {
	margin-left: 150px;
}

/* All padding */
.p-0 {
	padding: 0 !important;
}

.p-xs {
	padding: 10px;
}

.p-sm {
	padding: 20px;
}

.p-md {
	padding: 40px;
}

.p-lg {
	padding: 80px;
}

/* top padding */
.pt-0 {
	padding-top: 0 !important;
}

.pt-20 {
	padding-top: 20px !important;
}

.pt-30 {
	padding-top: 30px !important;
}

.pt-40 {
	padding-top: 40px !important;
}

.pt-50 {
	padding-top: 50px;
}

.pt-60 {
	padding-top: 60px;
}

.pt-70 {
	padding-top: 70px !important;
}

.pt-80 {
	padding-top: 80px;
}

.pt-90 {
	padding-top: 90px;
}

.pt-100 {
	padding-top: 100px !important;
}

.pt-150 {
	padding-top: 150px !important;
}

/* bottom padding */
.pb-0 {
	padding-bottom: 0 !important;
}

.pb-30 {
	padding-bottom: 30px;
}

.pb-50 {
	padding-bottom: 50px;
}

.pb-60 {
	padding-bottom: 60px;
}

.pb-70 {
	padding-bottom: 70px !important;
}

.pb-80 {
	padding-bottom: 80px;
}

.pb-90 {
	padding-bottom: 90px;
}

.pb-100 {
	padding-bottom: 100px !important;
}

/* right padding */
.pr-0 {
	padding-right: 0;
}

.pr-xs {
	padding-right: 10px;
}

.pr-sm {
	padding-right: 20px;
}

.pr-md {
	padding-right: 40px;
}

.pr-lg {
	padding-right: 80px;
}

.pr-15 {
	padding-right: 15px !important;
}

.pr-30 {
	padding-right: 30px !important;
}

.pr-50 {
	padding-right: 50px;
}

.pr-60 {
	padding-right: 60px;
}

.pr-100 {
	padding-right: 100px !important;
}

.pr-150 {
	padding-right: 150px;
}

/* left padding */
.pl-0 {
	padding-left: 0 !important;
}

.pl-30 {
	padding-left: 30px;
}

.pl-50 {
	padding-left: 50px;
}

.pl-60 {
	padding-left: 60px;
}

.pl-100 {
	padding-left: 100px !important;
}

.pl-150 {
	padding-left: 150px;
}

/* Postions */
.fixed {
	position: fixed;
}

.relative {
	position: relative;
}

.absolute {
	position: absolute;
}

.static {
	position: static;
}

/* Zindex*/
.zindex-1 {
	z-index: 1;
}

.zindex-2 {
	z-index: 2;
}

.zindex-3 {
	z-index: 3;
}

/* Borders */
.border-all {
	border: 1px solid #49b970;
}

.border-top {
	border-top: 1px solid #49b970;
}

.border-bottom {
	border-bottom: 1px solid #49b970;
}

.border-right {
	border-right: 1px solid #49b970;
}

.border-left {
	border-left: 1px solid #49b970;
}

/* Display */
.inline {
	display: inline;
}

.block {
	display: block;
}

.inline-block {
	display: inline-block;
}

.hide {
	display: none;
}

.flex {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

@media only screen and (max-width: 767px) {
	section {
		padding-top   : 60px;
		padding-bottom: 60px;
	}

	.text-center-xs {
		text-align: center !important;
	}

	.pull-none-xs {
		float     : none !important;
		text-align: center !important;
	}

	.mb-15-xs {
		margin-bottom: 15px;
	}

	.mb-30-xs {
		margin-bottom: 30px !important;
	}

	.mb-50-xs {
		margin-bottom: 50px;
	}

	.mb-60-xs {
		margin-bottom: 60px !important;
	}

	.p-none-xs {
		padding-right: 0;
		padding-left : 0;
	}
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
	.text-center-sm {
		text-align: center !important;
	}

	.mb-0-sm {
		margin-bottom: 0;
	}

	.mb-15-sm {
		margin-bottom: 15px;
	}

	.mb-30-sm {
		margin-bottom: 30px !important;
	}

	.mb-50-sm {
		margin-bottom: 50px;
	}

	.mb-60-sm {
		margin-bottom: 60px;
	}

	.pb-15-sm {
		padding-bottom: 15px;
	}

	.pb-30-sm {
		padding-bottom: 30px;
	}

	.pb-50-sm {
		padding-bottom: 50px;
	}

	.pb-60-sm {
		padding-bottom: 60px;
	}

	.p-none-sm {
		padding-right: 0;
		padding-left : 0;
	}

	.pull-none-sm {
		float     : none !important;
		text-align: center !important;
	}
}

.border-b {
	border-bottom: 1px solid #f9f9f9;
}

/*------------------------------------*\
    #Grid
\*------------------------------------*/
.row-no-padding [class*="col-"] {
	padding-left : 0 !important;
	padding-right: 0 !important;
}

.col-lg-5ths,
.col-md-5ths,
.col-sm-5ths,
.col-xs-5ths {
	position     : relative;
	min-height   : 1px;
	padding-right: 15px;
	padding-left : 15px;
}

.col-xs-5ths {
	width: 20%;
	float: left;
}

@media (min-width: 768px) {
	.col-sm-5ths {
		width: 20%;
		float: left;
	}
}

@media (min-width: 992px) {
	.col-md-5ths {
		width: 20%;
		float: left;
	}
}

@media (min-width: 1200px) {
	.col-lg-5ths {
		width: 20%;
		float: left;
	}
}

.col-content {
	padding: 120px 100px 90px 120px;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.col-content {
		padding: 40px !important;
	}
}

.col-img {
	height  : 600px !important;
	padding : 60px;
	overflow: hidden;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.col-img {
		height    : auto;
		min-height: 600px;
	}
}

/* Postion Helpers */
.pos-fixed {
	position: fixed;
}

.pos-relative {
	position: relative;
}

.pos-absolute {
	position: absolute;
}

.pos-static {
	position: static;
}

.pos-top {
	top: 0;
}

.pos-bottom {
	bottom: 0;
}

.pos-right {
	right: 0;
}

.pos-left {
	left: 0;
}

.pos-vertical-center {
	position         : relative;
	top              : 50%;
	-webkit-transform: perspective(1px) translateY(-50%);
	transform        : perspective(1px) translateY(-50%);
}

.height-700 {
	height: 700px !important;
}

.height-500 {
	height: 500px !important;
}

.height-800 {
	height: 800px !important;
}

@media only screen and (min-width: 992px) {

	.hidden-lg,
	.hidden-md {
		display: none;
	}
}

/*------------------------------------*\
    #BACKGROUNDS
\*------------------------------------*/
.bg-theme {
	background-color: #49b970 !important;
}

.bg-gray {
	background-color: #f9f9f9 !important;
}

.bg-white {
	background-color: #ffffff !important;
}

.bg-dark {
	background-color: #333333 !important;
}

/* Background Image */
.bg-section {
	position           : relative;
	overflow           : hidden;
	z-index            : 1;
	background-size    : cover;
	background-repeat  : no-repeat;
	background-position: center center;
	width              : 100%;
	height             : 100%;
}

.bg-overlay:before {
	content : "";
	display : inline-block;
	height  : 100%;
	left    : 0;
	position: absolute;
	top     : 0;
	width   : 100%;
	z-index : -1;
}

.bg-overlay-light:before {
	background-color: #ffffff;
	opacity         : 0.8;
}

.bg-overlay-dark:before {
	background-color: #222222;
	opacity         : 0.9;
}

.bg-overlay-theme:before {
	background-color: #49b970;
	opacity         : 0.7;
}

.bg-parallex {
	background-attachment: fixed;
}

/* Background Color Palettes */
.bg-purple {
	color           : #ffffff;
	background-color: #cc66cc !important;
}

.bg-sun-flower {
	color           : #ffffff;
	background-color: #f1c40f !important;
}

.bg-orange {
	color           : #ffffff;
	background-color: #f39c12 !important;
}

.bg-carrot {
	color           : #ffffff;
	background-color: #e67e22 !important;
}

.bg-pumpkin {
	color           : #ffffff;
	background-color: #d35400 !important;
}

.bg-alizarin {
	color           : #ffffff;
	background-color: #e74c3c !important;
}

.bg-pomegranate {
	color           : #ffffff;
	background-color: #c0392b !important;
}

.bg-turquoise {
	color           : #ffffff;
	background-color: #1abc9c !important;
}

.bg-green-sea {
	color           : #ffffff;
	background-color: #16a085 !important;
}

.bg-emerald {
	color           : #ffffff;
	background-color: #2ecc71 !important;
}

.bg-nephritis {
	color           : #ffffff;
	background-color: #27ae60 !important;
}

.bg-peter-river {
	color           : #ffffff;
	background-color: #3498db !important;
}

.bg-belize-hole {
	color           : #ffffff;
	background-color: #2980b9 !important;
}

.bg-amethyst {
	color           : #ffffff;
	background-color: #9b59b6 !important;
}

.bg-wisteria {
	color           : #ffffff;
	background-color: #8e44ad !important;
}

.bg-wet-asphalt {
	color           : #ffffff;
	background-color: #34495e !important;
}

.bg-wet-asphalt {
	color           : #ffffff;
	background-color: #34495e !important;
}

.bg-midnight-blue {
	color           : #ffffff;
	background-color: #2c3e50 !important;
}

.bg-clouds {
	color           : #454545;
	background-color: #ecf0f1 !important;
}

.bg-silver {
	color           : #ffffff;
	background-color: #bdc3c7 !important;
}

.bg-concrete {
	color           : #ffffff;
	background-color: #859596 !important;
}

.bg-asbestos {
	color           : #ffffff;
	background-color: #7f8c8d !important;
}

.bg-asbestos {
	color           : #ffffff;
	background-color: #7f8c8d !important;
}

.bg-graphite {
	color           : #ffffff;
	background-color: #454545 !important;
}

.bg-gray-0 {
	background-color: #EEEEEE !important;
	color           : #454545;
}

.bg-gray-1 {
	background-color: #ECECEC !important;
	color           : #454545;
}

.bg-gray-2 {
	background-color: #BDC3C7 !important;
	color           : #454545;
}

.bg-gray-3 {
	backgrond-color: #DADFE1 !important;
	color          : #454545;
}

.bg-gray-5 {
	background-color: #ECF0F1 !important;
	color           : #454545;
}

.bg-gray-4 {
	backgrouund-color: #F2F1EF !important;
	color            : #454545;
}

.bg-gray-6 {
	background-color: #D2D7D3 !important;
	color           : #454545;
}

.bg-gray-7 {
	background-color: #E6E6E6 !important;
	color           : #454545;
}

.bg-orange-1 {
	background-color: #F9690E !important;
	color           : #ffffff;
}

.bg-orange-2 {
	background-color: #D35400 !important;
	color           : #ffffff;
}

.bg-orange-3 {
	background-color: #F89406 !important;
	color           : #ffffff;
}

/* Background Video */
.bg-ytvideo {
	position: absolute !important;
	height  : inherit;
	width   : 100%;
	top     : 0;
	right   : 0;
	bottom  : 0;
	left    : 0;
	z-index : 0;
	overflow: hidden;
}

.bg-ytvideo.bg-overlay:before {
	z-index: 1;
	opacity: .7;
}

.bg-ytvideo iframe {
	position      : absolute;
	top           : 0;
	left          : 0;
	width         : 150%;
	height        : 900px;
	pointer-events: none;
}

.bg-video {
	width            : 100%;
	overflow         : hidden;
	behavior         : url(/PIE.htc);
	-webkit-transform: translateZ(0);
	transform        : translateZ(0);
	display          : block;
	border-radius    : 0;
}

.bg-video iframe {
	display : block;
	position: absolute;
	z-index : -2;
}

.bg-video .bg-player {
	bottom    : 0;
	left      : 0;
	right     : 0;
	position  : absolute;
	top       : 0;
	width     : 100%;
	background: url(polina.jpg) no-repeat;
	min-height: 500px;
}

.bg-video .vidbg {
	min-width : 100%;
	min-height: 100%;
}

.bg-vyoutube {
	z-index   : 550;
	text-align: center;
	height    : 100%;
	min-height: 100%;
	position  : relative;
	overflow  : hidden;
}

/*------------------------------------*\
    #BUTTONS
\*------------------------------------*/
.btn {
	font-family   : "Ubuntu", sans-serif;
	position      : relative;
	z-index       : 2;
	font-size     : 12px;
	font-weight   : 700;
	text-transform: uppercase;
	text-align    : center;
	border-radius : 0;
	padding       : 0;
	line-height   : 45px;
	width         : 170px;
	height        : 45px;
}

.btn.active,
.btn:active {
	-webkit-box-shadow: none;
	box-shadow        : none;
}

/* Button Primary */
.btn--primary {
	background-color: #49b970;
	color           : #ffffff;
	border          : none;
}

.btn--primary:active,
.btn--primary:focus,
.btn--primary:hover {
	background-color: #37b05d;
	color           : #ffffff;
}

.bg-dark .btn--primary:active,
.bg-dark .btn--primary:focus,
.bg-dark .btn--primary:hover {
	background-color: #37b05d;
	color           : #ffffff;
}

/* Button Secondary*/
.btn--secondary {
	background-color: #333333;
	color           : #ffffff;
	border          : 2px solid #333333;
}

.btn--secondary:active,
.btn--secondary:focus,
.btn--secondary:hover {
	background-color: #49b970;
	color           : #ffffff;
	border-color    : #49b970;
}

/* Button White */
.btn--white {
	background-color: #ffffff;
	color           : #333333;
	border          : 1px solid #ffffff;
	border-radius   : 4px;
}

.btn--white:active,
.btn--white:focus,
.btn--white:hover {
	background-color: #49b970;
	color           : #ffffff;
	border-color    : #49b970;
}

/* Button Block */
.btn--block {
	width: 100%;
}

.btn--auto {
	width: auto;
}

/* Button Rounded */
.btn--rounded {
	border-radius: 50px;
}

/* Button Bordered */
.btn--bordered {
	background-color: transparent;
}

.btn--bordered.btn--primary {
	color       : #49b970;
	border-color: #49b970;
}

.btn--bordered.btn--primary:active,
.btn--bordered.btn--primary:focus,
.btn--bordered.btn--primary:hover {
	color           : #ffffff;
	background-color: #49b970;
	border-color    : #49b970;
}

.btn--bordered.btn--secondary {
	color       : #333333;
	border-color: #333333;
}

.btn--bordered.btn--secondary:active,
.btn--bordered.btn--secondary:focus,
.btn--bordered.btn--secondary:hover {
	color           : #ffffff;
	background-color: #333333;
	border-color    : #333333;
}

.btn--bordered.btn--white {
	color       : #ffffff;
	border-color: #ffffff;
}

.btn--bordered.btn--white:active,
.btn--bordered.btn--white:focus,
.btn--bordered.btn--white:hover {
	color           : #49b970;
	background-color: #ffffff;
	border-color    : #ffffff;
}

.btn--white.btn--secondary {
	color           : #333333;
	border-color    : #ffffff;
	background-color: #ffffff;
}

.btn--white.btn--secondary:active,
.btn--white.btn--secondary:focus,
.btn--white.btn--secondary:hover {
	color           : #ffffff;
	background-color: #49b970;
	border-color    : #49b970;
}

.bg-theme .btn--primary,
.bg-overlay-theme .btn--primary {
	background-color: #2ecc71;
	color           : #ffffff;
	border          : none;
}

.portfolio-filter li a.active-filter,
.portfolio-filter li a:hover {
	color: #49b970;
}

.navbar-toggle {
	border-color : #49b970;
	border-radius: 0;
	margin-top   : 35px;
}

/*------------------------------------*\
    #FORMS
\*------------------------------------*/
.form-control {
	color             : #ffffff;
	font-family       : "Ubuntu", sans-serif;
	font-size         : 14px;
	font-weight       : 400;
	line-height       : 45px;
	height            : 45px;
	text-transform    : capitalize;
	border            : none;
	background        : transparent;
	padding-left      : 24px;
	margin-bottom     : 30px;
	position          : relative;
	border-radius     : 0;
	-webkit-box-shadow: none;
	box-shadow        : none;
}

.form-control:focus {
	border-color      : #49b970;
	-webkit-box-shadow: none;
	box-shadow        : none;
	background        : transparent;
}

.form-control::-webkit-input-placeholder {
	color: #ffffff;
}

.form-control:-moz-placeholder {
	color: #ffffff;
}

.form-control::-moz-placeholder {
	color: #ffffff;
}

.form-control:-ms-input-placeholder {
	color: #ffffff;
}

/* Placeholder */
.input-group {
	border-radius   : 25px;
	border          : 1px solid #ffffff;
	background-color: transparent;
	overflow        : hidden;
	padding         : 3px;
}

.input-group .btn {
	width: 160px;
}

.input-group .input-group-btn {
	border-radius: 50px;
	overflow     : hidden;
}

.subscribe-alert {
	margin-top: 1.5rem;
}

.subscribe-alert .alert {
	font-size  : 18px;
	line-height: 22px;
}

/*------------------------------------*\
    #Heading
\*------------------------------------*/
.heading .heading--title {
	font-family   : "Ubuntu", sans-serif;
	color         : #222222;
	font-size     : 36px;
	font-weight   : 400;
	line-height   : 1;
	margin-bottom : 30px;
	text-transform: capitalize;
}

.heading .heading--desc {
	font-size    : 18px;
	font-weight  : 400;
	line-height  : 24px;
	margin-bottom: 0;
	padding      : 0 30px;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.heading .heading--title {
		font-size: 30px;
	}
}

/*------------------------------------*\
    #LOADING-SECTION
\*------------------------------------*/
.preloader {
	width           : 100%;
	height          : 100%;
	left            : 0;
	top             : 0;
	position        : fixed;
	z-index         : 99999;
	background-color: #ffffff;
	overflow        : hidden;
}

/*------------------------------------*\
    #HEADER
\*------------------------------------*/
.header.header-transparent {
	background-color: transparent;
	position        : absolute;
	top             : 0;
	left            : 0;
	width           : 100%;
	z-index         : 1001;
}

/*------------------------------------*\
    #Menu
\*------------------------------------*/
.header .navbar {
	margin-bottom: 0;
	border-radius: 0;
	min-height   : 100px;
	border       : none;
	padding      : 0;
}

.header .navbar.navbar-sticky {
	position: fixed;
	top     : 0;
	right   : 0;
	left    : 0;
	z-index : 1030;
}

.header .navbar.navbar-hidden {
	top: -200px;
}

.header .navbar .navbar-brand {
	margin-right: 0;
}

.header .navbar .navbar-brand .logo-dark {
	display: none;
}

.header .navbar .navbar-brand .logo-light {
	display: inline-block;
}

.header .navbar .navbar-nav>li {
	margin-right: 30px;
	position    : relative;
}

.header .navbar .navbar-nav>li:last-child {
	margin-right: 0;
}

.header .navbar .navbar-nav>li>a {
	font-size         : 13px;
	text-transform    : uppercase;
	font-weight       : 700;
	line-height       : 100px;
	color             : #333333;
	padding           : 0;
	position          : relative;
	display           : block;
	-webkit-transition: 0.3s ease-in-out;
	-o-transition     : 0.3s ease-in-out;
	transition        : 0.3s ease-in-out;
}

.header .navbar .navbar-nav>li>a:before {
	position          : absolute;
	bottom            : -1px;
	left              : 0;
	right             : 0;
	margin            : auto;
	width             : 0;
	height            : 3px;
	background        : transparent;
	content           : '';
	-webkit-transition: all 0.4s;
	-o-transition     : all 0.4s;
	transition        : all 0.4s;
}

.header .navbar .navbar-nav>li.active>a,
.header .navbar .navbar-nav>li>a:focus,
.header .navbar .navbar-nav>li>a:hover {
	color: #49b970;
}

.header .navbar .navbar-nav>li.active>a:before,
.header .navbar .navbar-nav>li>a:focus:before,
.header .navbar .navbar-nav>li>a:hover:before {
	background-color: #49b970;
	width           : 100%;
}

@media screen and (max-width: 991px) {
	.header .navbar .navbar-nav>li {
		margin-right: 0;
	}

	.header .navbar .navbar-nav>li a {
		margin-right: 0;
		padding     : 15px 10px;
		color       : #333333;
		line-height : 1;
	}

	.header .navbar .navbar-nav>li.active {
		background-color: #49b970;
	}

	.header .navbar .navbar-nav>li.active a {
		color: #ffffff;
	}
}

.header .navbar.navbar-fixed {
	top               : 0;
	background        : #ffffff;
	-webkit-box-shadow: 0 2px 4px rgba(3, 27, 78, 0.1);
	box-shadow        : 0 2px 4px rgba(3, 27, 78, 0.1);
	-webkit-animation : navbarFixed 0.8s;
	animation         : navbarFixed 0.8s;
}

.header .navbar.navbar-fixed .navbar-brand .logo-dark {
	display: inline-block;
}

.header .navbar.navbar-fixed .navbar-brand .logo-light {
	display: none;
}

.header .navbar.navbar-fixed .navbar-nav>li a {
	color: #333333;
}

@media screen and (max-width: 991px) {
	.header .navbar.navbar-fixed .navbar-nav>li a:hover {
		background-color: #49b970;
		color           : #ffffff;
	}
}

.header .navbar.navbar-fixed .navbar-nav>li.active a {
	color: #49b970;
}

@media screen and (max-width: 991px) {
	.header .navbar.navbar-fixed .navbar-nav>li.active {
		background-color: #49b970;
	}

	.header .navbar.navbar-fixed .navbar-nav>li.active a {
		color: #ffffff;
	}
}

@-webkit-keyframes navbarFixed {
	0% {
		opacity          : 0;
		-webkit-transform: translateY(-100%);
		transform        : translateY(-100%);
	}

	100% {
		opacity          : 1;
		-webkit-transform: translateY(0);
		transform        : translateY(0);
	}
}

@keyframes navbarFixed {
	0% {
		opacity          : 0;
		-webkit-transform: translateY(-100%);
		transform        : translateY(-100%);
	}

	100% {
		opacity          : 1;
		-webkit-transform: translateY(0);
		transform        : translateY(0);
	}
}

/* Small Devices, Tablets */
@media only screen and (max-width: 991px) {
	.navbar .navbar-brand {
		margin-left: 15px;
		height     : 100px;
		line-height: 100px;
	}

	.navbar .navbar-toggler {
		width             : 30px;
		height            : 30px;
		display           : -webkit-box;
		display           : -ms-flexbox;
		display           : flex;
		-webkit-box-pack  : center;
		-ms-flex-pack     : center;
		justify-content   : center;
		-webkit-box-align : center;
		-ms-flex-align    : center;
		align-items       : center;
		-webkit-transition: 0.3s ease-in-out;
		-o-transition     : 0.3s ease-in-out;
		transition        : 0.3s ease-in-out;
		cursor            : pointer;
		outline           : none;
		margin-right      : 30px;
		padding           : 0;
	}
}

@media only screen and (max-width: 991px) and (min-width: 992px) {
	.navbar .navbar-toggler {
		display: none;
	}
}

@media only screen and (max-width: 991px) {
	.navbar .navbar-toggler:not(.collapsed) .navbar-toggler-icon {
		background-color: transparent;
	}

	.navbar .navbar-toggler:not(.collapsed) .navbar-toggler-icon:before,
	.navbar .navbar-toggler:not(.collapsed) .navbar-toggler-icon:after {
		top: 0;
	}

	.navbar .navbar-toggler:not(.collapsed) .navbar-toggler-icon::before {
		-webkit-transform: rotate(45deg);
		-ms-transform    : rotate(45deg);
		transform        : rotate(45deg);
	}

	.navbar .navbar-toggler:not(.collapsed) .navbar-toggler-icon::after {
		-webkit-transform: rotate(-45deg);
		-ms-transform    : rotate(-45deg);
		transform        : rotate(-45deg);
	}

	.navbar .navbar-toggler .navbar-toggler-icon {
		width             : 100%;
		height            : 2px;
		background-color  : #49b970;
		position          : relative;
		-webkit-transition: 0.3s ease-in-out;
		-o-transition     : 0.3s ease-in-out;
		transition        : 0.3s ease-in-out;
	}

	.navbar .navbar-toggler .navbar-toggler-icon:before,
	.navbar .navbar-toggler .navbar-toggler-icon:after {
		content           : '';
		position          : absolute;
		left              : 0;
		height            : 2px;
		width             : 100%;
		background-color  : #49b970;
		-webkit-transition: 0.3s ease-in-out;
		-o-transition     : 0.3s ease-in-out;
		transition        : 0.3s ease-in-out;
	}

	.navbar .navbar-toggler .navbar-toggler-icon:before {
		top: -10px;
	}

	.navbar .navbar-toggler .navbar-toggler-icon:after {
		top: 10px;
	}

	.navbar .navbar-collapse {
		background-color: #ffffff;
		padding         : 15px;
	}

	.navbar.navbar-fixed .navbar-toggler:not(.collapsed) .navbar-toggler-icon {
		background-color: transparent;
	}

	.navbar.navbar-fixed .navbar-toggler .navbar-toggler-icon {
		background-color: #49b970;
	}

	.navbar.navbar-fixed .navbar-toggler .navbar-toggler-icon:before,
	.navbar.navbar-fixed .navbar-toggler .navbar-toggler-icon:after {
		background-color: #49b970;
	}
}

/* Large Devices, Wide Screens */
@media only screen and (min-width: 992px) and (max-width: 1200px) {
	.navbar-nav>li {
		margin-right: 16px;
	}

	.module .module-icon,
	.module-consultation .btn {
		margin-right: 0;
	}
}

@media only screen and (min-width: 992px) {
	.navbar-collapse {
		padding-right: 0;
		padding-left : 0;
	}
}

/*------------------------------------*\
    #Hero
\*------------------------------------*/
.hero {
	height        : 800px;
	padding-top   : 0;
	padding-bottom: 0;
}

.hero .hero-content {
	height: 800px;
}

.hero .row-content {
	padding-top: 80px;
}

.hero .hero-headline {
	color         : #ffffff;
	font-family   : "Ubuntu", sans-serif;
	font-size     : 48px;
	font-weight   : 400;
	line-height   : 60px;
	margin-bottom : 20px;
	text-transform: capitalize;
}

.hero .hero-bio {
	color        : #ffffff;
	opacity      : 0.6;
	font-family  : "Ubuntu", sans-serif;
	font-size    : 20px;
	font-weight  : 400;
	line-height  : 32px;
	margin-bottom: 47px;
}

.hero .hero-holder {
	position: relative;
}

.hero .hero-holder img {
	position: absolute;
	left    : 0;
	bottom  : -700px;
}

.hero-2 {
	overflow: visible;
}

.hero-2 .bg-section {
	overflow: visible;
}

.hero-2+.clearfix {
	padding-top: 200px;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.hero .row-content {
		padding-top: 120px;
	}

	.hero .hero-headline {
		margin-top : 50px;
		font-size  : 30px;
		line-height: 1.2;
		text-align : center;
		margin-top : 0;
	}

	.hero .hero-bio {
		font-size  : 14px;
		line-height: 1.5;
		padding    : 0 10px;
		text-align : center;
	}

	.hero .btn {
		width: 130px;
	}

	.hero form {
		margin-bottom: 30px !important;
	}

	.hero .hero-action {
		text-align   : center;
		margin-bottom: 30px;
	}

	.hero .hero-holder img {
		position: relative;
		right   : 0;
		bottom  : 0;
	}

	.hero,
	.hero .hero-content {
		height          : auto;
		background-color: #333333;
	}

	.hero-2+.clearfix {
		padding-top: 0;
	}
}

/* Small Devices, Tablets */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	.hero .hero-headline {
		font-size  : 30px;
		line-height: 1.2;
	}

	.hero .hero-bio {
		font-size  : 14px;
		line-height: 1.5;
	}

	.hero .hero-holder img {
		max-width: 350px;
		position : relative;
		right    : 0;
		bottom   : 0;
	}

	.hero-2+.clearfix {
		padding-top: 0;
	}
}

/*------------------------------------*\
    #Testimonial
\*------------------------------------*/
.testimonial-panel {
	padding           : 0 40px;
	text-align        : center;
	-webkit-transition: all 0.3s linear;
	-o-transition     : all 0.3s linear;
	transition        : all 0.3s linear;
}

.testimonial-panel .testimonial--body {
	margin-bottom: 40px;
	position     : relative;
	padding-top  : 80px;
}

.testimonial-panel .testimonial--body:before {
	position   : absolute;
	top        : 0;
	left       : 50%;
	margin-left: -16px;
	content    : "“";
	color      : #333333;
	font-family: "Merriweather", serif;
	font-size  : 70px;
	font-weight: 400;
	line-height: 1;
}

.testimonial-panel .testimonial--body p {
	color        : #999999;
	font-family  : "Merriweather", serif;
	font-size    : 20px;
	font-style   : italic;
	line-height  : 38px;
	margin-bottom: 0;
	font-weight  : 400;
}

.testimonial-panel .testimonial--meta h5 {
	color         : #333333;
	font-size     : 16px;
	font-weight   : 400;
	line-height   : 18px;
	margin-bottom : 9px;
	text-transform: capitalize;
}

.testimonial-panel .testimonial--meta a p {
	color        : #999999;
	font-size    : 12px;
	font-weight  : 400;
	line-height  : 1;
	margin-bottom: 0;
}

.testimonial-panel .testimonial--meta img {
	max-width: 100%;
	height   : auto;
	width    : auto;
	margin   : 0 auto 25px auto;
}

/*------------------------------------*\
    #Features
\*------------------------------------*/
.feature-panel {
	position: relative;
}

.feature-panel .feature--icon {
	color        : #49b970;
	font-size    : 47px;
	line-height  : 1;
	margin-bottom: 35px;
}

.feature-panel .feature--icon i {
	-webkit-transition: all 0.3s linear;
	-o-transition     : all 0.3s linear;
	transition        : all 0.3s linear;
	display           : inline-block;
}

.feature-panel .feature--icon:hover i {
	-webkit-animation: pulse 0.9s infinite;
	animation        : pulse 0.9s infinite;
}

.feature-panel .feature--content h3 {
	font-family   : "Ubuntu", sans-serif;
	color         : #333333;
	font-size     : 18px;
	font-weight   : 400;
	line-height   : 1;
	margin-bottom : 24px;
	text-transform: capitalize;
}

.feature-panel .feature--content p {
	font-size    : 14px;
	font-weight  : 400;
	line-height  : 24px;
	margin-bottom: 0;
	padding-right: 10px;
	padding-left : 10px;
}

.feature-left .feature-panel {
	-webkit-transition: all 0.3s;
	-o-transition     : all 0.3s;
	transition        : all 0.3s;
	margin-bottom     : 18px;
}

.feature-left .feature-panel:hover {
	-webkit-transform: translateY(-4px);
	-ms-transform    : translateY(-4px);
	transform        : translateY(-4px);
}

.feature-left .feature-panel .feature--icon {
	position     : absolute;
	left         : 0;
	top          : 0;
	margin-bottom: 0;
}

.feature-left .feature-panel .feature--content {
	padding-left: 68px;
}

/* feature left circle  */
.feature-left-circle .feature--content h3 {
	font-size    : 22px;
	font-family  : "Ubuntu", sans-serif;
	font-weight  : 400;
	line-height  : 29px;
	margin-bottom: 15px;
}

.feature-left-circle .feature--content p {
	font-size  : 14px;
	font-weight: 400;
	line-height: 23px;
	padding    : 0;
}

.feature-left-circle .feature--icon {
	width             : 45px;
	height            : 45px;
	line-height       : 42px;
	text-align        : center;
	border-radius     : 50%;
	color             : #49b970;
	font-family       : "Ubuntu", sans-serif;
	font-size         : 24px;
	border            : 2px solid #49b970;
	-webkit-transition: all 0.2s linear;
	-o-transition     : all 0.2s linear;
	transition        : all 0.2s linear;
}

.feature-left-circle:hover .feature--icon {
	background  : #49b970;
	border-color: #49b970;
	color       : #ffffff;
}

.feature-divider {
	margin-top   : 0;
	margin-bottom: 0;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.feature-panel {
		margin-bottom: 20px;
	}

	.feature-panel .feature--icon {
		margin-bottom: 10px;
	}

	.feature img {
		max-width: 100%;
		height   : auto;
	}
}

/* Small Devices, Tablets */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	.feature-panel {
		margin-bottom: 20px;
	}
}

.banner h3 {
	color         : #363636;
	font-size     : 36px;
	font-weight   : 400;
	line-height   : 1;
	margin-bottom : 45px;
	text-transform: capitalize;
}

.banner p {
	color        : #999999;
	font-family  : Ubuntu;
	font-size    : 18px;
	font-weight  : 400;
	line-height  : 31px;
	margin-bottom: 49px;
}

/*------------------------------------*\
    #Call To Action
\*------------------------------------*/
.cta h3 {
	color        : #fffefe;
	font-family  : Ubuntu;
	font-size    : 40px;
	font-weight  : 400;
	line-height  : 58px;
	margin-bottom: 25px;
}

.cta p {
	color        : #ffffff;
	font-family  : Ubuntu;
	font-size    : 18px;
	font-weight  : 400;
	line-height  : 28px;
	opacity      : 0.6;
	margin-bottom: 48px;
}

/*------------------------------------*\
    #PRICING-SECTION
\*------------------------------------*/
.pricing-panel {
	border            : 1px solid #eeeeee;
	background-color  : #ffffff;
	padding           : 50px 65px;
	-webkit-transition: all 0.35s;
	-o-transition     : all 0.35s;
	transition        : all 0.35s;
	position          : relative;
}

.pricing-panel:hover {
	-webkit-transform: translateY(-4px);
	-ms-transform    : translateY(-4px);
	transform        : translateY(-4px);
}

.pricing-panel .pricing--heading .pricing--icon {
	color         : #49b970;
	font-family   : "Ubuntu", sans-serif;
	font-size     : 33px;
	font-weight   : 400;
	line-height   : 1;
	margin-bottom : 30px;
	text-transform: uppercase;
}

.pricing-panel .pricing--heading p {
	color         : #333333;
	font-size     : 70px;
	font-weight   : 700;
	line-height   : 1;
	margin-bottom : 15px;
	text-transform: capitalize;
}

.pricing-panel .pricing--heading span.currency {
	font-size     : 18px;
	font-weight   : 400;
	vertical-align: top;
	top           : 10px;
	position      : relative;
}

.pricing-panel .pricing--heading .pricing--desc {
	font-size     : 12px;
	font-weight   : 400;
	line-height   : 24px;
	margin-bottom : 26px;
	text-transform: uppercase;
}

.pricing-panel .pricing--body {
	text-align : center;
	font-size  : 16px;
	font-weight: 400;
}

.pricing-panel .pricing--body ul {
	margin-bottom: 40px;
}

.pricing-panel .btn--primary.btn--primary:hover {
	background-color: #37b05d;
	border-color    : #37b05d;
	color           : #ffffff;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.price-table {
		margin-bottom: 30px;
	}
}

/* Small Devices, Tablets */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	.pricing-panel {
		padding: 20px;
	}
}

/*------------------------------------*\
    #Footer
\*------------------------------------*/
.footer {
	background-color: #ffffff;
	padding-top     : 33px;
	padding-bottom  : 33px;
}

.footer--copyright {
	color         : #999999;
	font-family   : "Ubuntu", sans-serif;
	font-size     : 12px;
	font-weight   : 400;
	line-height   : 1;
	text-transform: capitalize;
}

.footer--copyright i {
	color: red;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.footer--copyright {
		font-size: 12px;
	}
}

/*------------------------------------*\
    #Carousel
\*------------------------------------*/
.carousel-dots .owl-controls {
	margin-top: 50px;
}

.carousel-dots .owl-controls .owl-dots .owl-dot span {
	height          : 11px;
	width           : 11px;
	border-radius   : 50%;
	background-color: transparent;
	border          : 2px solid rgba(100, 100, 100, 0.5);
	margin          : 0 4px;
}

.carousel-dots .owl-controls .owl-dots .owl-dot.active span {
	background-color: #646464;
	border-color    : #646464;
}

.carousel-dots.carousel-white .owl-controls .owl-dots .owl-dot span {
	border-color: rgba(255, 255, 255, 0.5);
}

.carousel-dots.carousel-white .owl-controls .owl-dots .owl-dot.active span {
	background-color: #ffffff;
	border-color    : #ffffff;
}

.carousel-navs .owl-controls .owl-nav {
	position: relative;
}

.carousel-navs .owl-controls .owl-nav [class*="owl-"] {
	position          : absolute;
	top               : 50%;
	height            : 60px;
	line-height       : 60px;
	margin-top        : -30px;
	color             : #333333;
	font-size         : 0;
	font-weight       : 400;
	text-align        : center;
	background-color  : transparent;
	padding           : 0;
	border-radius     : 0;
	-webkit-transition: all 0.35s;
	-o-transition     : all 0.35s;
	transition        : all 0.35s;
	margin-top        : -250px;
}

.carousel-navs .owl-controls .owl-nav [class*="owl-"]:hover {
	color           : #49b970;
	background-color: transparent;
}

.carousel-navs .owl-controls .owl-nav .owl-prev {
	left: 0;
}

.carousel-navs .owl-controls .owl-nav .owl-prev:before {
	font-family: "Linearicons-Free";
	content    : "\e875";
	font-size  : 40px;
}

.carousel-navs .owl-controls .owl-nav .owl-next {
	right: 0;
}

.carousel-navs .owl-controls .owl-nav .owl-next:before {
	font-family: "Linearicons-Free";
	content    : "\e876";
	font-size  : 40px;
}

.carousel-navs .owl-controls .owl-dots {
	position: absolute;
	bottom  : 20px;
	width   : 100%;
}

.carousel-navs .owl-controls .owl-dots span {
	background-color: transparent;
	border          : 1px solid #fff;
}

.carousel-navs .owl-controls .owl-dots .owl-dot.active span,
.carousel-navs .owl-controls .owl-dots .owl-dot:hover span {
	background: #ffffff;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.carousel-dots .owl-controls {
		margin-top: 20px;
	}
}

/*------------------------------------*\
    #Landing Page / Promo Page
\*------------------------------------*/
.landing-hero {
	height     : 100vh;
	padding-top: 200px;
}

.landing-hero h1 {
	font-size     : 65px;
	font-weight   : 700;
	line-height   : 70px;
	text-transform: uppercase;
}

.landing-hero p {
	font-family: "Ubuntu", sans-serif;
	font-size  : 18px;
	line-height: 27px;
}

.landing-demos .portfolio-item {
	margin-bottom: 50px;
}

.landing-demos .portfolio-item .portfolio-title h4 {
	color         : #282828;
	text-align    : center;
	font-size     : 16px;
	font-weight   : 600;
	text-transform: capitalize;
	margin-bottom : 0;
}

.landing-demos .portfolio-item .portfolio-title:hover h4 {
	color: #49b970;
}

.landing-demos .portfolio-item .portfolio-img {
	-webkit-box-shadow: 0 0 36px 4px rgba(0, 0, 0, 0.1);
	box-shadow        : 0 0 36px 4px rgba(0, 0, 0, 0.1);
	background-color  : #c0c0c0;
	-webkit-transition: all 0.35s;
	-o-transition     : all 0.35s;
	transition        : all 0.35s;
}

.landing-demos .portfolio-item .portfolio-img img {
	max-width         : 100%;
	height            : auto;
	-webkit-transition: all 300ms ease-in-out;
	-o-transition     : all 300ms ease-in-out;
	transition        : all 300ms ease-in-out;
}

.landing-demos .portfolio-item:hover .portfolio-img {
	-webkit-transform: translateY(-6px);
	-ms-transform    : translateY(-6px);
	transform        : translateY(-6px);
}

.landing-demos .portfolio-item:hover .portfolio-img img {
	-webkit-transform: scale(1);
	-ms-transform    : scale(1);
	transform        : scale(1);
}

.landing-demos .portfolio-item .portfolio-bio {
	padding-top: 23px;
}

.landing-action {
	padding-top: 200px;
}

.landing-action h2 {
	font-size    : 50px;
	line-height  : 60px;
	margin-bottom: 50px;
}

.landing-action img {
	margin-bottom: 50px;
}

.landing-featues {
	padding-bottom: 50px;
}

.landing-featues .feature-box {
	margin-bottom: 50px;
}

/* Custom, iPhone Retina */
@media only screen and (min-width: 320px) and (max-width: 767px) {
	.landing-hero {
		height     : auto;
		padding-top: 60px;
	}

	.landing-hero h1 {
		font-size  : 34px;
		line-height: 50px;
	}
}

/* Small Devices, Tablets */
@media only screen and (min-width: 768px) and (max-width: 991px) {
	.landing-hero {
		height     : auto;
		padding-top: 100px;
	}

	.landing-hero h1 {
		font-size  : 50px;
		line-height: 55px;
	}
}