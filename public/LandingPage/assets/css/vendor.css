@charset "UTF-8";

/*!
 * animate.css -https://daneden.github.io/animate.css/
 * Version - 3.7.2
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2019 <PERSON>
 */

@-webkit-keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }

  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes bounce {
  from,
  20%,
  53%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }

  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}

.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}

@-webkit-keyframes flash {
  from,
  50%,
  to {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }
}

@keyframes flash {
  from,
  50%,
  to {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }
}

.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
}

@-webkit-keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }

  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }

  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }

  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }

  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }

  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }

  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
}

@-webkit-keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

@keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }

  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }

  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }

  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }

  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes headShake {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  6.5% {
    -webkit-transform: translateX(-6px) rotateY(-9deg);
    transform: translateX(-6px) rotateY(-9deg);
  }

  18.5% {
    -webkit-transform: translateX(5px) rotateY(7deg);
    transform: translateX(5px) rotateY(7deg);
  }

  31.5% {
    -webkit-transform: translateX(-3px) rotateY(-5deg);
    transform: translateX(-3px) rotateY(-5deg);
  }

  43.5% {
    -webkit-transform: translateX(2px) rotateY(3deg);
    transform: translateX(2px) rotateY(3deg);
  }

  50% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.headShake {
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -webkit-animation-name: headShake;
  animation-name: headShake;
}

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }

  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }

  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }

  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }

  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }

  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }

  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }

  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }

  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

.swing {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-animation-name: swing;
  animation-name: swing;
}

@-webkit-keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  10%,
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%,
  60%,
  80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.tada {
  -webkit-animation-name: tada;
  animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }

  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }

  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }

  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes wobble {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }

  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }

  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }

  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble;
}

@-webkit-keyframes jello {
  from,
  11.1%,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }

  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }

  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }

  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }

  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }

  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }

  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}

@keyframes jello {
  from,
  11.1%,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }

  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }

  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }

  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }

  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }

  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }

  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}

.jello {
  -webkit-animation-name: jello;
  animation-name: jello;
  -webkit-transform-origin: center;
  transform-origin: center;
}

@-webkit-keyframes heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }

  28% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }

  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes heartBeat {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  14% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }

  28% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  42% {
    -webkit-transform: scale(1.3);
    transform: scale(1.3);
  }

  70% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.heartBeat {
  -webkit-animation-name: heartBeat;
  animation-name: heartBeat;
  -webkit-animation-duration: 1.3s;
  animation-duration: 1.3s;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
}

@-webkit-keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }

  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }

  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.bounceIn {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}

@-webkit-keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceInLeft {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceInRight {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }

  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }

  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight;
}

@-webkit-keyframes bounceInUp {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes bounceInUp {
  from,
  60%,
  75%,
  90%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }

  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }

  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }

  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp;
}

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  50%,
  55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}

@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  50%,
  55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}

.bounceOut {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut;
}

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }

  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}

@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }

  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}

.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown;
}

@-webkit-keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}

@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}

.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft;
}

@-webkit-keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}

@keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}

.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight;
}

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}

@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }

  40%,
  45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}

.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig;
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@-webkit-keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}

.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}

@keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}

.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig;
}

@-webkit-keyframes fadeOutLeft {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}

@keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}

.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig;
}

@-webkit-keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight;
}

@-webkit-keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}

@keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}

.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig;
}

@-webkit-keyframes fadeOutUp {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes fadeOutUp {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}

.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}

@keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}

.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig;
}

@-webkit-keyframes flip {
  from {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  40% {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  50% {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  to {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}

@keyframes flip {
  from {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  40% {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  50% {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  to {
    -webkit-transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}

.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  -webkit-animation-name: flip;
  animation-name: flip;
}

@-webkit-keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

@keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

.flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
}

@-webkit-keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

@keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

.flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
}

@-webkit-keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }

  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }

  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}

@keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }

  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }

  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}

.flipOutX {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-name: flipOutX;
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }

  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }

  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}

@keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }

  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }

  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}

.flipOutY {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipOutY;
  animation-name: flipOutY;
}

@-webkit-keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }

  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }

  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }

  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
}

@-webkit-keyframes lightSpeedOut {
  from {
    opacity: 1;
  }

  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}

@keyframes lightSpeedOut {
  from {
    opacity: 1;
  }

  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}

.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-timing-function: ease-in;
}

@-webkit-keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn;
}

@-webkit-keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight;
}

@-webkit-keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}

@keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}

.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut;
}

@-webkit-keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}

@keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}

.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft;
}

@-webkit-keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

@keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight;
}

@-webkit-keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

@keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}

.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft;
}

@-webkit-keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}

@keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }

  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}

.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight;
}

@-webkit-keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  20%,
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  40%,
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }

  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}

@keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  20%,
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }

  40%,
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }

  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}

.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-name: hinge;
  animation-name: hinge;
}

@-webkit-keyframes jackInTheBox {
  from {
    opacity: 0;
    -webkit-transform: scale(0.1) rotate(30deg);
    transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
  }

  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }

  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
  }

  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes jackInTheBox {
  from {
    opacity: 0;
    -webkit-transform: scale(0.1) rotate(30deg);
    transform: scale(0.1) rotate(30deg);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
  }

  50% {
    -webkit-transform: rotate(-10deg);
    transform: rotate(-10deg);
  }

  70% {
    -webkit-transform: rotate(3deg);
    transform: rotate(3deg);
  }

  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.jackInTheBox {
  -webkit-animation-name: jackInTheBox;
  animation-name: jackInTheBox;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */

@-webkit-keyframes rollOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}

@keyframes rollOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}

.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown;
}

@-webkit-keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft;
}

@-webkit-keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight;
}

@-webkit-keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp;
}

@-webkit-keyframes zoomOut {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 0;
  }
}

@keyframes zoomOut {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 0;
  }
}

.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

@-webkit-keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown;
}

@-webkit-keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}

@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}

.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft;
}

@-webkit-keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}

@keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }

  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}

.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight;
}

@-webkit-keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

@keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp;
}

@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown;
}

@-webkit-keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft;
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

@-webkit-keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp;
}

@-webkit-keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}

.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown;
}

@-webkit-keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft;
}

@-webkit-keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

@-webkit-keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}

.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp;
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animated.delay-1s {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

.animated.delay-2s {
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}

.animated.delay-3s {
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}

.animated.delay-4s {
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}

.animated.delay-5s {
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}

.animated.fast {
  -webkit-animation-duration: 800ms;
  animation-duration: 800ms;
}

.animated.faster {
  -webkit-animation-duration: 500ms;
  animation-duration: 500ms;
}

.animated.slow {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animated.slower {
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
}

@media (print), (prefers-reduced-motion: reduce) {
  .animated {
    -webkit-animation-duration: 1ms !important;
    animation-duration: 1ms !important;
    -webkit-transition-duration: 1ms !important;
    transition-duration: 1ms !important;
    -webkit-animation-iteration-count: 1 !important;
    animation-iteration-count: 1 !important;
  }
}

/*!
 * Bootstrap v4.3.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */:root{--blue:#007bff;--indigo:#6610f2;--purple:#6f42c1;--pink:#e83e8c;--red:#dc3545;--orange:#fd7e14;--yellow:#ffc107;--green:#28a745;--teal:#20c997;--cyan:#17a2b8;--white:#fff;--gray:#6c757d;--gray-dark:#343a40;--primary:#007bff;--secondary:#6c757d;--success:#28a745;--info:#17a2b8;--warning:#ffc107;--danger:#dc3545;--light:#f8f9fa;--dark:#343a40;--breakpoint-xs:0;--breakpoint-sm:576px;--breakpoint-md:768px;--breakpoint-lg:992px;--breakpoint-xl:1200px;--font-family-sans-serif:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-family-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace}*,::after,::before{box-sizing:border-box}html{font-family:sans-serif;line-height:1.15;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}article,aside,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}body{margin:0;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;text-align:left;background-color:#fff}[tabindex="-1"]:focus{outline:0!important}hr{box-sizing:content-box;height:0;overflow:visible}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:.5rem}p{margin-top:0;margin-bottom:1rem}abbr[data-original-title],abbr[title]{text-decoration:underline;-webkit-text-decoration:underline dotted;text-decoration:underline dotted;cursor:help;border-bottom:0;-webkit-text-decoration-skip-ink:none;text-decoration-skip-ink:none}address{margin-bottom:1rem;font-style:normal;line-height:inherit}dl,ol,ul{margin-top:0;margin-bottom:1rem}ol ol,ol ul,ul ol,ul ul{margin-bottom:0}dt{font-weight:700}dd{margin-bottom:.5rem;margin-left:0}blockquote{margin:0 0 1rem}b,strong{font-weight:bolder}small{font-size:80%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}a{color:#007bff;text-decoration:none;background-color:transparent}a:hover{color:#0056b3;text-decoration:underline}a:not([href]):not([tabindex]){color:inherit;text-decoration:none}a:not([href]):not([tabindex]):focus,a:not([href]):not([tabindex]):hover{color:inherit;text-decoration:none}a:not([href]):not([tabindex]):focus{outline:0}code,kbd,pre,samp{font-family:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;font-size:1em}pre{margin-top:0;margin-bottom:1rem;overflow:auto}figure{margin:0 0 1rem}img{vertical-align:middle;border-style:none}svg{overflow:hidden;vertical-align:middle}table{border-collapse:collapse}caption{padding-top:.75rem;padding-bottom:.75rem;color:#6c757d;text-align:left;caption-side:bottom}th{text-align:inherit}label{display:inline-block;margin-bottom:.5rem}button{border-radius:0}button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}button,input,optgroup,select,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,input{overflow:visible}button,select{text-transform:none}select{word-wrap:normal}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}[type=button]:not(:disabled),[type=reset]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled){cursor:pointer}[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner{padding:0;border-style:none}input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}input[type=date],input[type=datetime-local],input[type=month],input[type=time]{-webkit-appearance:listbox}textarea{overflow:auto;resize:vertical}fieldset{min-width:0;padding:0;margin:0;border:0}legend{display:block;width:100%;max-width:100%;padding:0;margin-bottom:.5rem;font-size:1.5rem;line-height:inherit;color:inherit;white-space:normal}progress{vertical-align:baseline}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{outline-offset:-2px;-webkit-appearance:none}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}output{display:inline-block}summary{display:list-item;cursor:pointer}template{display:none}[hidden]{display:none!important}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{margin-bottom:.5rem;font-weight:500;line-height:1.2}.h1,h1{font-size:2.5rem}.h2,h2{font-size:2rem}.h3,h3{font-size:1.75rem}.h4,h4{font-size:1.5rem}.h5,h5{font-size:1.25rem}.h6,h6{font-size:1rem}.lead{font-size:1.25rem;font-weight:300}.display-1{font-size:6rem;font-weight:300;line-height:1.2}.display-2{font-size:5.5rem;font-weight:300;line-height:1.2}.display-3{font-size:4.5rem;font-weight:300;line-height:1.2}.display-4{font-size:3.5rem;font-weight:300;line-height:1.2}hr{margin-top:1rem;margin-bottom:1rem;border:0;border-top:1px solid rgba(0,0,0,.1)}.small,small{font-size:80%;font-weight:400}.mark,mark{padding:.2em;background-color:#fcf8e3}.list-unstyled{padding-left:0;list-style:none}.list-inline{padding-left:0;list-style:none}.list-inline-item{display:inline-block}.list-inline-item:not(:last-child){margin-right:.5rem}.initialism{font-size:90%;text-transform:uppercase}.blockquote{margin-bottom:1rem;font-size:1.25rem}.blockquote-footer{display:block;font-size:80%;color:#6c757d}.blockquote-footer::before{content:"\2014\00A0"}.img-fluid{max-width:100%;height:auto}.img-thumbnail{padding:.25rem;background-color:#fff;border:1px solid #dee2e6;border-radius:.25rem;max-width:100%;height:auto}.figure{display:inline-block}.figure-img{margin-bottom:.5rem;line-height:1}.figure-caption{font-size:90%;color:#6c757d}code{font-size:87.5%;color:#e83e8c;word-break:break-word}a>code{color:inherit}kbd{padding:.2rem .4rem;font-size:87.5%;color:#fff;background-color:#212529;border-radius:.2rem}kbd kbd{padding:0;font-size:100%;font-weight:700}pre{display:block;font-size:87.5%;color:#212529}pre code{font-size:inherit;color:inherit;word-break:normal}.pre-scrollable{max-height:340px;overflow-y:scroll}.container{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}@media (min-width:576px){.container{max-width:540px}}@media (min-width:768px){.container{max-width:720px}}@media (min-width:992px){.container{max-width:960px}}@media (min-width:1200px){.container{max-width:1140px}}.container-fluid{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto}.row{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-right:-15px;margin-left:-15px}.no-gutters{margin-right:0;margin-left:0}.no-gutters>.col,.no-gutters>[class*=col-]{padding-right:0;padding-left:0}.col,.col-1,.col-10,.col-11,.col-12,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-auto,.col-lg,.col-lg-1,.col-lg-10,.col-lg-11,.col-lg-12,.col-lg-2,.col-lg-3,.col-lg-4,.col-lg-5,.col-lg-6,.col-lg-7,.col-lg-8,.col-lg-9,.col-lg-auto,.col-md,.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9,.col-md-auto,.col-sm,.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9,.col-sm-auto,.col-xl,.col-xl-1,.col-xl-10,.col-xl-11,.col-xl-12,.col-xl-2,.col-xl-3,.col-xl-4,.col-xl-5,.col-xl-6,.col-xl-7,.col-xl-8,.col-xl-9,.col-xl-auto{position:relative;width:100%;padding-right:15px;padding-left:15px}.col{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;max-width:100%}.col-auto{-ms-flex:0 0 auto;flex:0 0 auto;width:auto;max-width:100%}.col-1{-ms-flex:0 0 8.333333%;flex:0 0 8.333333%;max-width:8.333333%}.col-2{-ms-flex:0 0 16.666667%;flex:0 0 16.666667%;max-width:16.666667%}.col-3{-ms-flex:0 0 25%;flex:0 0 25%;max-width:25%}.col-4{-ms-flex:0 0 33.333333%;flex:0 0 33.333333%;max-width:33.333333%}.col-5{-ms-flex:0 0 41.666667%;flex:0 0 41.666667%;max-width:41.666667%}.col-6{-ms-flex:0 0 50%;flex:0 0 50%;max-width:50%}.col-7{-ms-flex:0 0 58.333333%;flex:0 0 58.333333%;max-width:58.333333%}.col-8{-ms-flex:0 0 66.666667%;flex:0 0 66.666667%;max-width:66.666667%}.col-9{-ms-flex:0 0 75%;flex:0 0 75%;max-width:75%}.col-10{-ms-flex:0 0 83.333333%;flex:0 0 83.333333%;max-width:83.333333%}.col-11{-ms-flex:0 0 91.666667%;flex:0 0 91.666667%;max-width:91.666667%}.col-12{-ms-flex:0 0 100%;flex:0 0 100%;max-width:100%}.order-first{-ms-flex-order:-1;order:-1}.order-last{-ms-flex-order:13;order:13}.order-0{-ms-flex-order:0;order:0}.order-1{-ms-flex-order:1;order:1}.order-2{-ms-flex-order:2;order:2}.order-3{-ms-flex-order:3;order:3}.order-4{-ms-flex-order:4;order:4}.order-5{-ms-flex-order:5;order:5}.order-6{-ms-flex-order:6;order:6}.order-7{-ms-flex-order:7;order:7}.order-8{-ms-flex-order:8;order:8}.order-9{-ms-flex-order:9;order:9}.order-10{-ms-flex-order:10;order:10}.order-11{-ms-flex-order:11;order:11}.order-12{-ms-flex-order:12;order:12}.offset-1{margin-left:8.333333%}.offset-2{margin-left:16.666667%}.offset-3{margin-left:25%}.offset-4{margin-left:33.333333%}.offset-5{margin-left:41.666667%}.offset-6{margin-left:50%}.offset-7{margin-left:58.333333%}.offset-8{margin-left:66.666667%}.offset-9{margin-left:75%}.offset-10{margin-left:83.333333%}.offset-11{margin-left:91.666667%}@media (min-width:576px){.col-sm{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;max-width:100%}.col-sm-auto{-ms-flex:0 0 auto;flex:0 0 auto;width:auto;max-width:100%}.col-sm-1{-ms-flex:0 0 8.333333%;flex:0 0 8.333333%;max-width:8.333333%}.col-sm-2{-ms-flex:0 0 16.666667%;flex:0 0 16.666667%;max-width:16.666667%}.col-sm-3{-ms-flex:0 0 25%;flex:0 0 25%;max-width:25%}.col-sm-4{-ms-flex:0 0 33.333333%;flex:0 0 33.333333%;max-width:33.333333%}.col-sm-5{-ms-flex:0 0 41.666667%;flex:0 0 41.666667%;max-width:41.666667%}.col-sm-6{-ms-flex:0 0 50%;flex:0 0 50%;max-width:50%}.col-sm-7{-ms-flex:0 0 58.333333%;flex:0 0 58.333333%;max-width:58.333333%}.col-sm-8{-ms-flex:0 0 66.666667%;flex:0 0 66.666667%;max-width:66.666667%}.col-sm-9{-ms-flex:0 0 75%;flex:0 0 75%;max-width:75%}.col-sm-10{-ms-flex:0 0 83.333333%;flex:0 0 83.333333%;max-width:83.333333%}.col-sm-11{-ms-flex:0 0 91.666667%;flex:0 0 91.666667%;max-width:91.666667%}.col-sm-12{-ms-flex:0 0 100%;flex:0 0 100%;max-width:100%}.order-sm-first{-ms-flex-order:-1;order:-1}.order-sm-last{-ms-flex-order:13;order:13}.order-sm-0{-ms-flex-order:0;order:0}.order-sm-1{-ms-flex-order:1;order:1}.order-sm-2{-ms-flex-order:2;order:2}.order-sm-3{-ms-flex-order:3;order:3}.order-sm-4{-ms-flex-order:4;order:4}.order-sm-5{-ms-flex-order:5;order:5}.order-sm-6{-ms-flex-order:6;order:6}.order-sm-7{-ms-flex-order:7;order:7}.order-sm-8{-ms-flex-order:8;order:8}.order-sm-9{-ms-flex-order:9;order:9}.order-sm-10{-ms-flex-order:10;order:10}.order-sm-11{-ms-flex-order:11;order:11}.order-sm-12{-ms-flex-order:12;order:12}.offset-sm-0{margin-left:0}.offset-sm-1{margin-left:8.333333%}.offset-sm-2{margin-left:16.666667%}.offset-sm-3{margin-left:25%}.offset-sm-4{margin-left:33.333333%}.offset-sm-5{margin-left:41.666667%}.offset-sm-6{margin-left:50%}.offset-sm-7{margin-left:58.333333%}.offset-sm-8{margin-left:66.666667%}.offset-sm-9{margin-left:75%}.offset-sm-10{margin-left:83.333333%}.offset-sm-11{margin-left:91.666667%}}@media (min-width:768px){.col-md{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;max-width:100%}.col-md-auto{-ms-flex:0 0 auto;flex:0 0 auto;width:auto;max-width:100%}.col-md-1{-ms-flex:0 0 8.333333%;flex:0 0 8.333333%;max-width:8.333333%}.col-md-2{-ms-flex:0 0 16.666667%;flex:0 0 16.666667%;max-width:16.666667%}.col-md-3{-ms-flex:0 0 25%;flex:0 0 25%;max-width:25%}.col-md-4{-ms-flex:0 0 33.333333%;flex:0 0 33.333333%;max-width:33.333333%}.col-md-5{-ms-flex:0 0 41.666667%;flex:0 0 41.666667%;max-width:41.666667%}.col-md-6{-ms-flex:0 0 50%;flex:0 0 50%;max-width:50%}.col-md-7{-ms-flex:0 0 58.333333%;flex:0 0 58.333333%;max-width:58.333333%}.col-md-8{-ms-flex:0 0 66.666667%;flex:0 0 66.666667%;max-width:66.666667%}.col-md-9{-ms-flex:0 0 75%;flex:0 0 75%;max-width:75%}.col-md-10{-ms-flex:0 0 83.333333%;flex:0 0 83.333333%;max-width:83.333333%}.col-md-11{-ms-flex:0 0 91.666667%;flex:0 0 91.666667%;max-width:91.666667%}.col-md-12{-ms-flex:0 0 100%;flex:0 0 100%;max-width:100%}.order-md-first{-ms-flex-order:-1;order:-1}.order-md-last{-ms-flex-order:13;order:13}.order-md-0{-ms-flex-order:0;order:0}.order-md-1{-ms-flex-order:1;order:1}.order-md-2{-ms-flex-order:2;order:2}.order-md-3{-ms-flex-order:3;order:3}.order-md-4{-ms-flex-order:4;order:4}.order-md-5{-ms-flex-order:5;order:5}.order-md-6{-ms-flex-order:6;order:6}.order-md-7{-ms-flex-order:7;order:7}.order-md-8{-ms-flex-order:8;order:8}.order-md-9{-ms-flex-order:9;order:9}.order-md-10{-ms-flex-order:10;order:10}.order-md-11{-ms-flex-order:11;order:11}.order-md-12{-ms-flex-order:12;order:12}.offset-md-0{margin-left:0}.offset-md-1{margin-left:8.333333%}.offset-md-2{margin-left:16.666667%}.offset-md-3{margin-left:25%}.offset-md-4{margin-left:33.333333%}.offset-md-5{margin-left:41.666667%}.offset-md-6{margin-left:50%}.offset-md-7{margin-left:58.333333%}.offset-md-8{margin-left:66.666667%}.offset-md-9{margin-left:75%}.offset-md-10{margin-left:83.333333%}.offset-md-11{margin-left:91.666667%}}@media (min-width:992px){.col-lg{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;max-width:100%}.col-lg-auto{-ms-flex:0 0 auto;flex:0 0 auto;width:auto;max-width:100%}.col-lg-1{-ms-flex:0 0 8.333333%;flex:0 0 8.333333%;max-width:8.333333%}.col-lg-2{-ms-flex:0 0 16.666667%;flex:0 0 16.666667%;max-width:16.666667%}.col-lg-3{-ms-flex:0 0 25%;flex:0 0 25%;max-width:25%}.col-lg-4{-ms-flex:0 0 33.333333%;flex:0 0 33.333333%;max-width:33.333333%}.col-lg-5{-ms-flex:0 0 41.666667%;flex:0 0 41.666667%;max-width:41.666667%}.col-lg-6{-ms-flex:0 0 50%;flex:0 0 50%;max-width:50%}.col-lg-7{-ms-flex:0 0 58.333333%;flex:0 0 58.333333%;max-width:58.333333%}.col-lg-8{-ms-flex:0 0 66.666667%;flex:0 0 66.666667%;max-width:66.666667%}.col-lg-9{-ms-flex:0 0 75%;flex:0 0 75%;max-width:75%}.col-lg-10{-ms-flex:0 0 83.333333%;flex:0 0 83.333333%;max-width:83.333333%}.col-lg-11{-ms-flex:0 0 91.666667%;flex:0 0 91.666667%;max-width:91.666667%}.col-lg-12{-ms-flex:0 0 100%;flex:0 0 100%;max-width:100%}.order-lg-first{-ms-flex-order:-1;order:-1}.order-lg-last{-ms-flex-order:13;order:13}.order-lg-0{-ms-flex-order:0;order:0}.order-lg-1{-ms-flex-order:1;order:1}.order-lg-2{-ms-flex-order:2;order:2}.order-lg-3{-ms-flex-order:3;order:3}.order-lg-4{-ms-flex-order:4;order:4}.order-lg-5{-ms-flex-order:5;order:5}.order-lg-6{-ms-flex-order:6;order:6}.order-lg-7{-ms-flex-order:7;order:7}.order-lg-8{-ms-flex-order:8;order:8}.order-lg-9{-ms-flex-order:9;order:9}.order-lg-10{-ms-flex-order:10;order:10}.order-lg-11{-ms-flex-order:11;order:11}.order-lg-12{-ms-flex-order:12;order:12}.offset-lg-0{margin-left:0}.offset-lg-1{margin-left:8.333333%}.offset-lg-2{margin-left:16.666667%}.offset-lg-3{margin-left:25%}.offset-lg-4{margin-left:33.333333%}.offset-lg-5{margin-left:41.666667%}.offset-lg-6{margin-left:50%}.offset-lg-7{margin-left:58.333333%}.offset-lg-8{margin-left:66.666667%}.offset-lg-9{margin-left:75%}.offset-lg-10{margin-left:83.333333%}.offset-lg-11{margin-left:91.666667%}}@media (min-width:1200px){.col-xl{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;max-width:100%}.col-xl-auto{-ms-flex:0 0 auto;flex:0 0 auto;width:auto;max-width:100%}.col-xl-1{-ms-flex:0 0 8.333333%;flex:0 0 8.333333%;max-width:8.333333%}.col-xl-2{-ms-flex:0 0 16.666667%;flex:0 0 16.666667%;max-width:16.666667%}.col-xl-3{-ms-flex:0 0 25%;flex:0 0 25%;max-width:25%}.col-xl-4{-ms-flex:0 0 33.333333%;flex:0 0 33.333333%;max-width:33.333333%}.col-xl-5{-ms-flex:0 0 41.666667%;flex:0 0 41.666667%;max-width:41.666667%}.col-xl-6{-ms-flex:0 0 50%;flex:0 0 50%;max-width:50%}.col-xl-7{-ms-flex:0 0 58.333333%;flex:0 0 58.333333%;max-width:58.333333%}.col-xl-8{-ms-flex:0 0 66.666667%;flex:0 0 66.666667%;max-width:66.666667%}.col-xl-9{-ms-flex:0 0 75%;flex:0 0 75%;max-width:75%}.col-xl-10{-ms-flex:0 0 83.333333%;flex:0 0 83.333333%;max-width:83.333333%}.col-xl-11{-ms-flex:0 0 91.666667%;flex:0 0 91.666667%;max-width:91.666667%}.col-xl-12{-ms-flex:0 0 100%;flex:0 0 100%;max-width:100%}.order-xl-first{-ms-flex-order:-1;order:-1}.order-xl-last{-ms-flex-order:13;order:13}.order-xl-0{-ms-flex-order:0;order:0}.order-xl-1{-ms-flex-order:1;order:1}.order-xl-2{-ms-flex-order:2;order:2}.order-xl-3{-ms-flex-order:3;order:3}.order-xl-4{-ms-flex-order:4;order:4}.order-xl-5{-ms-flex-order:5;order:5}.order-xl-6{-ms-flex-order:6;order:6}.order-xl-7{-ms-flex-order:7;order:7}.order-xl-8{-ms-flex-order:8;order:8}.order-xl-9{-ms-flex-order:9;order:9}.order-xl-10{-ms-flex-order:10;order:10}.order-xl-11{-ms-flex-order:11;order:11}.order-xl-12{-ms-flex-order:12;order:12}.offset-xl-0{margin-left:0}.offset-xl-1{margin-left:8.333333%}.offset-xl-2{margin-left:16.666667%}.offset-xl-3{margin-left:25%}.offset-xl-4{margin-left:33.333333%}.offset-xl-5{margin-left:41.666667%}.offset-xl-6{margin-left:50%}.offset-xl-7{margin-left:58.333333%}.offset-xl-8{margin-left:66.666667%}.offset-xl-9{margin-left:75%}.offset-xl-10{margin-left:83.333333%}.offset-xl-11{margin-left:91.666667%}}.table{width:100%;margin-bottom:1rem;color:#212529}.table td,.table th{padding:.75rem;vertical-align:top;border-top:1px solid #dee2e6}.table thead th{vertical-align:bottom;border-bottom:2px solid #dee2e6}.table tbody+tbody{border-top:2px solid #dee2e6}.table-sm td,.table-sm th{padding:.3rem}.table-bordered{border:1px solid #dee2e6}.table-bordered td,.table-bordered th{border:1px solid #dee2e6}.table-bordered thead td,.table-bordered thead th{border-bottom-width:2px}.table-borderless tbody+tbody,.table-borderless td,.table-borderless th,.table-borderless thead th{border:0}.table-striped tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,.05)}.table-hover tbody tr:hover{color:#212529;background-color:rgba(0,0,0,.075)}.table-primary,.table-primary>td,.table-primary>th{background-color:#b8daff}.table-primary tbody+tbody,.table-primary td,.table-primary th,.table-primary thead th{border-color:#7abaff}.table-hover .table-primary:hover{background-color:#9fcdff}.table-hover .table-primary:hover>td,.table-hover .table-primary:hover>th{background-color:#9fcdff}.table-secondary,.table-secondary>td,.table-secondary>th{background-color:#d6d8db}.table-secondary tbody+tbody,.table-secondary td,.table-secondary th,.table-secondary thead th{border-color:#b3b7bb}.table-hover .table-secondary:hover{background-color:#c8cbcf}.table-hover .table-secondary:hover>td,.table-hover .table-secondary:hover>th{background-color:#c8cbcf}.table-success,.table-success>td,.table-success>th{background-color:#c3e6cb}.table-success tbody+tbody,.table-success td,.table-success th,.table-success thead th{border-color:#8fd19e}.table-hover .table-success:hover{background-color:#b1dfbb}.table-hover .table-success:hover>td,.table-hover .table-success:hover>th{background-color:#b1dfbb}.table-info,.table-info>td,.table-info>th{background-color:#bee5eb}.table-info tbody+tbody,.table-info td,.table-info th,.table-info thead th{border-color:#86cfda}.table-hover .table-info:hover{background-color:#abdde5}.table-hover .table-info:hover>td,.table-hover .table-info:hover>th{background-color:#abdde5}.table-warning,.table-warning>td,.table-warning>th{background-color:#ffeeba}.table-warning tbody+tbody,.table-warning td,.table-warning th,.table-warning thead th{border-color:#ffdf7e}.table-hover .table-warning:hover{background-color:#ffe8a1}.table-hover .table-warning:hover>td,.table-hover .table-warning:hover>th{background-color:#ffe8a1}.table-danger,.table-danger>td,.table-danger>th{background-color:#f5c6cb}.table-danger tbody+tbody,.table-danger td,.table-danger th,.table-danger thead th{border-color:#ed969e}.table-hover .table-danger:hover{background-color:#f1b0b7}.table-hover .table-danger:hover>td,.table-hover .table-danger:hover>th{background-color:#f1b0b7}.table-light,.table-light>td,.table-light>th{background-color:#fdfdfe}.table-light tbody+tbody,.table-light td,.table-light th,.table-light thead th{border-color:#fbfcfc}.table-hover .table-light:hover{background-color:#ececf6}.table-hover .table-light:hover>td,.table-hover .table-light:hover>th{background-color:#ececf6}.table-dark,.table-dark>td,.table-dark>th{background-color:#c6c8ca}.table-dark tbody+tbody,.table-dark td,.table-dark th,.table-dark thead th{border-color:#95999c}.table-hover .table-dark:hover{background-color:#b9bbbe}.table-hover .table-dark:hover>td,.table-hover .table-dark:hover>th{background-color:#b9bbbe}.table-active,.table-active>td,.table-active>th{background-color:rgba(0,0,0,.075)}.table-hover .table-active:hover{background-color:rgba(0,0,0,.075)}.table-hover .table-active:hover>td,.table-hover .table-active:hover>th{background-color:rgba(0,0,0,.075)}.table .thead-dark th{color:#fff;background-color:#343a40;border-color:#454d55}.table .thead-light th{color:#495057;background-color:#e9ecef;border-color:#dee2e6}.table-dark{color:#fff;background-color:#343a40}.table-dark td,.table-dark th,.table-dark thead th{border-color:#454d55}.table-dark.table-bordered{border:0}.table-dark.table-striped tbody tr:nth-of-type(odd){background-color:rgba(255,255,255,.05)}.table-dark.table-hover tbody tr:hover{color:#fff;background-color:rgba(255,255,255,.075)}@media (max-width:575.98px){.table-responsive-sm{display:block;width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}.table-responsive-sm>.table-bordered{border:0}}@media (max-width:767.98px){.table-responsive-md{display:block;width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}.table-responsive-md>.table-bordered{border:0}}@media (max-width:991.98px){.table-responsive-lg{display:block;width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}.table-responsive-lg>.table-bordered{border:0}}@media (max-width:1199.98px){.table-responsive-xl{display:block;width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}.table-responsive-xl>.table-bordered{border:0}}.table-responsive{display:block;width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch}.table-responsive>.table-bordered{border:0}.form-control{display:block;width:100%;height:calc(1.5em + .75rem + 2px);padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:.25rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}@media (prefers-reduced-motion:reduce){.form-control{transition:none}}.form-control::-ms-expand{background-color:transparent;border:0}.form-control:focus{color:#ffffff;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.form-control::-webkit-input-placeholder{color:#6c757d;opacity:1}.form-control::-moz-placeholder{color:#6c757d;opacity:1}.form-control:-ms-input-placeholder{color:#6c757d;opacity:1}.form-control::-ms-input-placeholder{color:#6c757d;opacity:1}.form-control::placeholder{color:#ffffff;opacity:0.2; font-style: italic;}.form-control:disabled,.form-control[readonly]{background-color:#e9ecef;opacity:1}select.form-control:focus::-ms-value{color:#495057;background-color:#fff}.form-control-file,.form-control-range{display:block;width:100%}.col-form-label{padding-top:calc(.375rem + 1px);padding-bottom:calc(.375rem + 1px);margin-bottom:0;font-size:inherit;line-height:1.5}.col-form-label-lg{padding-top:calc(.5rem + 1px);padding-bottom:calc(.5rem + 1px);font-size:1.25rem;line-height:1.5}.col-form-label-sm{padding-top:calc(.25rem + 1px);padding-bottom:calc(.25rem + 1px);font-size:.875rem;line-height:1.5}.form-control-plaintext{display:block;width:100%;padding-top:.375rem;padding-bottom:.375rem;margin-bottom:0;line-height:1.5;color:#212529;background-color:transparent;border:solid transparent;border-width:1px 0}.form-control-plaintext.form-control-lg,.form-control-plaintext.form-control-sm{padding-right:0;padding-left:0}.form-control-sm{height:calc(1.5em + .5rem + 2px);padding:.25rem .5rem;font-size:.875rem;line-height:1.5;border-radius:.2rem}.form-control-lg{height:calc(1.5em + 1rem + 2px);padding:.5rem 1rem;font-size:1.25rem;line-height:1.5;border-radius:.3rem}select.form-control[multiple],select.form-control[size]{height:auto}textarea.form-control{height:auto}.form-group{margin-bottom:1rem}.form-text{display:block;margin-top:.25rem}.form-row{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-right:-5px;margin-left:-5px}.form-row>.col,.form-row>[class*=col-]{padding-right:5px;padding-left:5px}.form-check{position:relative;display:block;padding-left:1.25rem}.form-check-input{position:absolute;margin-top:.3rem;margin-left:-1.25rem}.form-check-input:disabled~.form-check-label{color:#6c757d}.form-check-label{margin-bottom:0}.form-check-inline{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center;padding-left:0;margin-right:.75rem}.form-check-inline .form-check-input{position:static;margin-top:0;margin-right:.3125rem;margin-left:0}.valid-feedback{display:none;width:100%;margin-top:.25rem;font-size:80%;color:#28a745}.valid-tooltip{position:absolute;top:100%;z-index:5;display:none;max-width:100%;padding:.25rem .5rem;margin-top:.1rem;font-size:.875rem;line-height:1.5;color:#fff;background-color:rgba(40,167,69,.9);border-radius:.25rem}.form-control.is-valid,.was-validated .form-control:valid{border-color:#28a745;padding-right:calc(1.5em + .75rem);background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");background-repeat:no-repeat;background-position:center right calc(.375em + .1875rem);background-size:calc(.75em + .375rem) calc(.75em + .375rem)}.form-control.is-valid:focus,.was-validated .form-control:valid:focus{border-color:#28a745;box-shadow:0 0 0 .2rem rgba(40,167,69,.25)}.form-control.is-valid~.valid-feedback,.form-control.is-valid~.valid-tooltip,.was-validated .form-control:valid~.valid-feedback,.was-validated .form-control:valid~.valid-tooltip{display:block}.was-validated textarea.form-control:valid,textarea.form-control.is-valid{padding-right:calc(1.5em + .75rem);background-position:top calc(.375em + .1875rem) right calc(.375em + .1875rem)}.custom-select.is-valid,.was-validated .custom-select:valid{border-color:#28a745;padding-right:calc((1em + .75rem) * 3 / 4 + 1.75rem);background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px,url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)}.custom-select.is-valid:focus,.was-validated .custom-select:valid:focus{border-color:#28a745;box-shadow:0 0 0 .2rem rgba(40,167,69,.25)}.custom-select.is-valid~.valid-feedback,.custom-select.is-valid~.valid-tooltip,.was-validated .custom-select:valid~.valid-feedback,.was-validated .custom-select:valid~.valid-tooltip{display:block}.form-control-file.is-valid~.valid-feedback,.form-control-file.is-valid~.valid-tooltip,.was-validated .form-control-file:valid~.valid-feedback,.was-validated .form-control-file:valid~.valid-tooltip{display:block}.form-check-input.is-valid~.form-check-label,.was-validated .form-check-input:valid~.form-check-label{color:#28a745}.form-check-input.is-valid~.valid-feedback,.form-check-input.is-valid~.valid-tooltip,.was-validated .form-check-input:valid~.valid-feedback,.was-validated .form-check-input:valid~.valid-tooltip{display:block}.custom-control-input.is-valid~.custom-control-label,.was-validated .custom-control-input:valid~.custom-control-label{color:#28a745}.custom-control-input.is-valid~.custom-control-label::before,.was-validated .custom-control-input:valid~.custom-control-label::before{border-color:#28a745}.custom-control-input.is-valid~.valid-feedback,.custom-control-input.is-valid~.valid-tooltip,.was-validated .custom-control-input:valid~.valid-feedback,.was-validated .custom-control-input:valid~.valid-tooltip{display:block}.custom-control-input.is-valid:checked~.custom-control-label::before,.was-validated .custom-control-input:valid:checked~.custom-control-label::before{border-color:#34ce57;background-color:#34ce57}.custom-control-input.is-valid:focus~.custom-control-label::before,.was-validated .custom-control-input:valid:focus~.custom-control-label::before{box-shadow:0 0 0 .2rem rgba(40,167,69,.25)}.custom-control-input.is-valid:focus:not(:checked)~.custom-control-label::before,.was-validated .custom-control-input:valid:focus:not(:checked)~.custom-control-label::before{border-color:#28a745}.custom-file-input.is-valid~.custom-file-label,.was-validated .custom-file-input:valid~.custom-file-label{border-color:#28a745}.custom-file-input.is-valid~.valid-feedback,.custom-file-input.is-valid~.valid-tooltip,.was-validated .custom-file-input:valid~.valid-feedback,.was-validated .custom-file-input:valid~.valid-tooltip{display:block}.custom-file-input.is-valid:focus~.custom-file-label,.was-validated .custom-file-input:valid:focus~.custom-file-label{border-color:#28a745;box-shadow:0 0 0 .2rem rgba(40,167,69,.25)}.invalid-feedback{display:none;width:100%;margin-top:.25rem;font-size:80%;color:#dc3545}.invalid-tooltip{position:absolute;top:100%;z-index:5;display:none;max-width:100%;padding:.25rem .5rem;margin-top:.1rem;font-size:.875rem;line-height:1.5;color:#fff;background-color:rgba(220,53,69,.9);border-radius:.25rem}.form-control.is-invalid,.was-validated .form-control:invalid{border-color:#dc3545;padding-right:calc(1.5em + .75rem);background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");background-repeat:no-repeat;background-position:center right calc(.375em + .1875rem);background-size:calc(.75em + .375rem) calc(.75em + .375rem)}.form-control.is-invalid:focus,.was-validated .form-control:invalid:focus{border-color:#dc3545;box-shadow:0 0 0 .2rem rgba(220,53,69,.25)}.form-control.is-invalid~.invalid-feedback,.form-control.is-invalid~.invalid-tooltip,.was-validated .form-control:invalid~.invalid-feedback,.was-validated .form-control:invalid~.invalid-tooltip{display:block}.was-validated textarea.form-control:invalid,textarea.form-control.is-invalid{padding-right:calc(1.5em + .75rem);background-position:top calc(.375em + .1875rem) right calc(.375em + .1875rem)}.custom-select.is-invalid,.was-validated .custom-select:invalid{border-color:#dc3545;padding-right:calc((1em + .75rem) * 3 / 4 + 1.75rem);background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px,url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)}.custom-select.is-invalid:focus,.was-validated .custom-select:invalid:focus{border-color:#dc3545;box-shadow:0 0 0 .2rem rgba(220,53,69,.25)}.custom-select.is-invalid~.invalid-feedback,.custom-select.is-invalid~.invalid-tooltip,.was-validated .custom-select:invalid~.invalid-feedback,.was-validated .custom-select:invalid~.invalid-tooltip{display:block}.form-control-file.is-invalid~.invalid-feedback,.form-control-file.is-invalid~.invalid-tooltip,.was-validated .form-control-file:invalid~.invalid-feedback,.was-validated .form-control-file:invalid~.invalid-tooltip{display:block}.form-check-input.is-invalid~.form-check-label,.was-validated .form-check-input:invalid~.form-check-label{color:#dc3545}.form-check-input.is-invalid~.invalid-feedback,.form-check-input.is-invalid~.invalid-tooltip,.was-validated .form-check-input:invalid~.invalid-feedback,.was-validated .form-check-input:invalid~.invalid-tooltip{display:block}.custom-control-input.is-invalid~.custom-control-label,.was-validated .custom-control-input:invalid~.custom-control-label{color:#dc3545}.custom-control-input.is-invalid~.custom-control-label::before,.was-validated .custom-control-input:invalid~.custom-control-label::before{border-color:#dc3545}.custom-control-input.is-invalid~.invalid-feedback,.custom-control-input.is-invalid~.invalid-tooltip,.was-validated .custom-control-input:invalid~.invalid-feedback,.was-validated .custom-control-input:invalid~.invalid-tooltip{display:block}.custom-control-input.is-invalid:checked~.custom-control-label::before,.was-validated .custom-control-input:invalid:checked~.custom-control-label::before{border-color:#e4606d;background-color:#e4606d}.custom-control-input.is-invalid:focus~.custom-control-label::before,.was-validated .custom-control-input:invalid:focus~.custom-control-label::before{box-shadow:0 0 0 .2rem rgba(220,53,69,.25)}.custom-control-input.is-invalid:focus:not(:checked)~.custom-control-label::before,.was-validated .custom-control-input:invalid:focus:not(:checked)~.custom-control-label::before{border-color:#dc3545}.custom-file-input.is-invalid~.custom-file-label,.was-validated .custom-file-input:invalid~.custom-file-label{border-color:#dc3545}.custom-file-input.is-invalid~.invalid-feedback,.custom-file-input.is-invalid~.invalid-tooltip,.was-validated .custom-file-input:invalid~.invalid-feedback,.was-validated .custom-file-input:invalid~.invalid-tooltip{display:block}.custom-file-input.is-invalid:focus~.custom-file-label,.was-validated .custom-file-input:invalid:focus~.custom-file-label{border-color:#dc3545;box-shadow:0 0 0 .2rem rgba(220,53,69,.25)}.form-inline{display:-ms-flexbox;display:flex;-ms-flex-flow:row wrap;flex-flow:row wrap;-ms-flex-align:center;align-items:center}.form-inline .form-check{width:100%}@media (min-width:576px){.form-inline label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;margin-bottom:0}.form-inline .form-group{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-flow:row wrap;flex-flow:row wrap;-ms-flex-align:center;align-items:center;margin-bottom:0}.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}.form-inline .form-control-plaintext{display:inline-block}.form-inline .custom-select,.form-inline .input-group{width:auto}.form-inline .form-check{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-left:0}.form-inline .form-check-input{position:relative;-ms-flex-negative:0;flex-shrink:0;margin-top:0;margin-right:.25rem;margin-left:0}.form-inline .custom-control{-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.form-inline .custom-control-label{margin-bottom:0}}.btn{display:inline-block;font-weight:400;color:#212529;text-align:center;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}@media (prefers-reduced-motion:reduce){.btn{transition:none}}.btn:hover{color:#212529;text-decoration:none}.btn.focus,.btn:focus{outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.btn.disabled,.btn:disabled{opacity:.65}a.btn.disabled,fieldset:disabled a.btn{pointer-events:none}.btn-primary{color:#fff;background-color:#007bff;border-color:#007bff}.btn-primary:hover{color:#fff;background-color:#0069d9;border-color:#0062cc}.btn-primary.focus,.btn-primary:focus{box-shadow:0 0 0 .2rem rgba(38,143,255,.5)}.btn-primary.disabled,.btn-primary:disabled{color:#fff;background-color:#007bff;border-color:#007bff}.btn-primary:not(:disabled):not(.disabled).active,.btn-primary:not(:disabled):not(.disabled):active,.show>.btn-primary.dropdown-toggle{color:#fff;background-color:#0062cc;border-color:#005cbf}.btn-primary:not(:disabled):not(.disabled).active:focus,.btn-primary:not(:disabled):not(.disabled):active:focus,.show>.btn-primary.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(38,143,255,.5)}.btn-secondary{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-secondary:hover{color:#fff;background-color:#5a6268;border-color:#545b62}.btn-secondary.focus,.btn-secondary:focus{box-shadow:0 0 0 .2rem rgba(130,138,145,.5)}.btn-secondary.disabled,.btn-secondary:disabled{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-secondary:not(:disabled):not(.disabled).active,.btn-secondary:not(:disabled):not(.disabled):active,.show>.btn-secondary.dropdown-toggle{color:#fff;background-color:#545b62;border-color:#4e555b}.btn-secondary:not(:disabled):not(.disabled).active:focus,.btn-secondary:not(:disabled):not(.disabled):active:focus,.show>.btn-secondary.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(130,138,145,.5)}.btn-success{color:#fff;background-color:#28a745;border-color:#28a745}.btn-success:hover{color:#fff;background-color:#218838;border-color:#1e7e34}.btn-success.focus,.btn-success:focus{box-shadow:0 0 0 .2rem rgba(72,180,97,.5)}.btn-success.disabled,.btn-success:disabled{color:#fff;background-color:#28a745;border-color:#28a745}.btn-success:not(:disabled):not(.disabled).active,.btn-success:not(:disabled):not(.disabled):active,.show>.btn-success.dropdown-toggle{color:#fff;background-color:#1e7e34;border-color:#1c7430}.btn-success:not(:disabled):not(.disabled).active:focus,.btn-success:not(:disabled):not(.disabled):active:focus,.show>.btn-success.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(72,180,97,.5)}.btn-info{color:#fff;background-color:#17a2b8;border-color:#17a2b8}.btn-info:hover{color:#fff;background-color:#138496;border-color:#117a8b}.btn-info.focus,.btn-info:focus{box-shadow:0 0 0 .2rem rgba(58,176,195,.5)}.btn-info.disabled,.btn-info:disabled{color:#fff;background-color:#17a2b8;border-color:#17a2b8}.btn-info:not(:disabled):not(.disabled).active,.btn-info:not(:disabled):not(.disabled):active,.show>.btn-info.dropdown-toggle{color:#fff;background-color:#117a8b;border-color:#10707f}.btn-info:not(:disabled):not(.disabled).active:focus,.btn-info:not(:disabled):not(.disabled):active:focus,.show>.btn-info.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(58,176,195,.5)}.btn-warning{color:#212529;background-color:#ffc107;border-color:#ffc107}.btn-warning:hover{color:#212529;background-color:#e0a800;border-color:#d39e00}.btn-warning.focus,.btn-warning:focus{box-shadow:0 0 0 .2rem rgba(222,170,12,.5)}.btn-warning.disabled,.btn-warning:disabled{color:#212529;background-color:#ffc107;border-color:#ffc107}.btn-warning:not(:disabled):not(.disabled).active,.btn-warning:not(:disabled):not(.disabled):active,.show>.btn-warning.dropdown-toggle{color:#212529;background-color:#d39e00;border-color:#c69500}.btn-warning:not(:disabled):not(.disabled).active:focus,.btn-warning:not(:disabled):not(.disabled):active:focus,.show>.btn-warning.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(222,170,12,.5)}.btn-danger{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-danger:hover{color:#fff;background-color:#c82333;border-color:#bd2130}.btn-danger.focus,.btn-danger:focus{box-shadow:0 0 0 .2rem rgba(225,83,97,.5)}.btn-danger.disabled,.btn-danger:disabled{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-danger:not(:disabled):not(.disabled).active,.btn-danger:not(:disabled):not(.disabled):active,.show>.btn-danger.dropdown-toggle{color:#fff;background-color:#bd2130;border-color:#b21f2d}.btn-danger:not(:disabled):not(.disabled).active:focus,.btn-danger:not(:disabled):not(.disabled):active:focus,.show>.btn-danger.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(225,83,97,.5)}.btn-light{color:#212529;background-color:#f8f9fa;border-color:#f8f9fa}.btn-light:hover{color:#212529;background-color:#e2e6ea;border-color:#dae0e5}.btn-light.focus,.btn-light:focus{box-shadow:0 0 0 .2rem rgba(216,217,219,.5)}.btn-light.disabled,.btn-light:disabled{color:#212529;background-color:#f8f9fa;border-color:#f8f9fa}.btn-light:not(:disabled):not(.disabled).active,.btn-light:not(:disabled):not(.disabled):active,.show>.btn-light.dropdown-toggle{color:#212529;background-color:#dae0e5;border-color:#d3d9df}.btn-light:not(:disabled):not(.disabled).active:focus,.btn-light:not(:disabled):not(.disabled):active:focus,.show>.btn-light.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(216,217,219,.5)}.btn-dark{color:#fff;background-color:#343a40;border-color:#343a40}.btn-dark:hover{color:#fff;background-color:#23272b;border-color:#1d2124}.btn-dark.focus,.btn-dark:focus{box-shadow:0 0 0 .2rem rgba(82,88,93,.5)}.btn-dark.disabled,.btn-dark:disabled{color:#fff;background-color:#343a40;border-color:#343a40}.btn-dark:not(:disabled):not(.disabled).active,.btn-dark:not(:disabled):not(.disabled):active,.show>.btn-dark.dropdown-toggle{color:#fff;background-color:#1d2124;border-color:#171a1d}.btn-dark:not(:disabled):not(.disabled).active:focus,.btn-dark:not(:disabled):not(.disabled):active:focus,.show>.btn-dark.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(82,88,93,.5)}.btn-outline-primary{color:#007bff;border-color:#007bff}.btn-outline-primary:hover{color:#fff;background-color:#007bff;border-color:#007bff}.btn-outline-primary.focus,.btn-outline-primary:focus{box-shadow:0 0 0 .2rem rgba(0,123,255,.5)}.btn-outline-primary.disabled,.btn-outline-primary:disabled{color:#007bff;background-color:transparent}.btn-outline-primary:not(:disabled):not(.disabled).active,.btn-outline-primary:not(:disabled):not(.disabled):active,.show>.btn-outline-primary.dropdown-toggle{color:#fff;background-color:#007bff;border-color:#007bff}.btn-outline-primary:not(:disabled):not(.disabled).active:focus,.btn-outline-primary:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-primary.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(0,123,255,.5)}.btn-outline-secondary{color:#6c757d;border-color:#6c757d}.btn-outline-secondary:hover{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-outline-secondary.focus,.btn-outline-secondary:focus{box-shadow:0 0 0 .2rem rgba(108,117,125,.5)}.btn-outline-secondary.disabled,.btn-outline-secondary:disabled{color:#6c757d;background-color:transparent}.btn-outline-secondary:not(:disabled):not(.disabled).active,.btn-outline-secondary:not(:disabled):not(.disabled):active,.show>.btn-outline-secondary.dropdown-toggle{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-secondary.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(108,117,125,.5)}.btn-outline-success{color:#28a745;border-color:#28a745}.btn-outline-success:hover{color:#fff;background-color:#28a745;border-color:#28a745}.btn-outline-success.focus,.btn-outline-success:focus{box-shadow:0 0 0 .2rem rgba(40,167,69,.5)}.btn-outline-success.disabled,.btn-outline-success:disabled{color:#28a745;background-color:transparent}.btn-outline-success:not(:disabled):not(.disabled).active,.btn-outline-success:not(:disabled):not(.disabled):active,.show>.btn-outline-success.dropdown-toggle{color:#fff;background-color:#28a745;border-color:#28a745}.btn-outline-success:not(:disabled):not(.disabled).active:focus,.btn-outline-success:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-success.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(40,167,69,.5)}.btn-outline-info{color:#17a2b8;border-color:#17a2b8}.btn-outline-info:hover{color:#fff;background-color:#17a2b8;border-color:#17a2b8}.btn-outline-info.focus,.btn-outline-info:focus{box-shadow:0 0 0 .2rem rgba(23,162,184,.5)}.btn-outline-info.disabled,.btn-outline-info:disabled{color:#17a2b8;background-color:transparent}.btn-outline-info:not(:disabled):not(.disabled).active,.btn-outline-info:not(:disabled):not(.disabled):active,.show>.btn-outline-info.dropdown-toggle{color:#fff;background-color:#17a2b8;border-color:#17a2b8}.btn-outline-info:not(:disabled):not(.disabled).active:focus,.btn-outline-info:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-info.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(23,162,184,.5)}.btn-outline-warning{color:#ffc107;border-color:#ffc107}.btn-outline-warning:hover{color:#212529;background-color:#ffc107;border-color:#ffc107}.btn-outline-warning.focus,.btn-outline-warning:focus{box-shadow:0 0 0 .2rem rgba(255,193,7,.5)}.btn-outline-warning.disabled,.btn-outline-warning:disabled{color:#ffc107;background-color:transparent}.btn-outline-warning:not(:disabled):not(.disabled).active,.btn-outline-warning:not(:disabled):not(.disabled):active,.show>.btn-outline-warning.dropdown-toggle{color:#212529;background-color:#ffc107;border-color:#ffc107}.btn-outline-warning:not(:disabled):not(.disabled).active:focus,.btn-outline-warning:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-warning.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(255,193,7,.5)}.btn-outline-danger{color:#dc3545;border-color:#dc3545}.btn-outline-danger:hover{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-outline-danger.focus,.btn-outline-danger:focus{box-shadow:0 0 0 .2rem rgba(220,53,69,.5)}.btn-outline-danger.disabled,.btn-outline-danger:disabled{color:#dc3545;background-color:transparent}.btn-outline-danger:not(:disabled):not(.disabled).active,.btn-outline-danger:not(:disabled):not(.disabled):active,.show>.btn-outline-danger.dropdown-toggle{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-outline-danger:not(:disabled):not(.disabled).active:focus,.btn-outline-danger:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-danger.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(220,53,69,.5)}.btn-outline-light{color:#f8f9fa;border-color:#f8f9fa}.btn-outline-light:hover{color:#212529;background-color:#f8f9fa;border-color:#f8f9fa}.btn-outline-light.focus,.btn-outline-light:focus{box-shadow:0 0 0 .2rem rgba(248,249,250,.5)}.btn-outline-light.disabled,.btn-outline-light:disabled{color:#f8f9fa;background-color:transparent}.btn-outline-light:not(:disabled):not(.disabled).active,.btn-outline-light:not(:disabled):not(.disabled):active,.show>.btn-outline-light.dropdown-toggle{color:#212529;background-color:#f8f9fa;border-color:#f8f9fa}.btn-outline-light:not(:disabled):not(.disabled).active:focus,.btn-outline-light:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-light.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(248,249,250,.5)}.btn-outline-dark{color:#343a40;border-color:#343a40}.btn-outline-dark:hover{color:#fff;background-color:#343a40;border-color:#343a40}.btn-outline-dark.focus,.btn-outline-dark:focus{box-shadow:0 0 0 .2rem rgba(52,58,64,.5)}.btn-outline-dark.disabled,.btn-outline-dark:disabled{color:#343a40;background-color:transparent}.btn-outline-dark:not(:disabled):not(.disabled).active,.btn-outline-dark:not(:disabled):not(.disabled):active,.show>.btn-outline-dark.dropdown-toggle{color:#fff;background-color:#343a40;border-color:#343a40}.btn-outline-dark:not(:disabled):not(.disabled).active:focus,.btn-outline-dark:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-dark.dropdown-toggle:focus{box-shadow:0 0 0 .2rem rgba(52,58,64,.5)}.btn-link{font-weight:400;color:#007bff;text-decoration:none}.btn-link:hover{color:#0056b3;text-decoration:underline}.btn-link.focus,.btn-link:focus{text-decoration:underline;box-shadow:none}.btn-link.disabled,.btn-link:disabled{color:#6c757d;pointer-events:none}.btn-group-lg>.btn,.btn-lg{padding:.5rem 1rem;font-size:1.25rem;line-height:1.5;border-radius:.3rem}.btn-group-sm>.btn,.btn-sm{padding:.25rem .5rem;font-size:.875rem;line-height:1.5;border-radius:.2rem}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:.5rem}input[type=button].btn-block,input[type=reset].btn-block,input[type=submit].btn-block{width:100%}.fade{transition:opacity .15s linear}@media (prefers-reduced-motion:reduce){.fade{transition:none}}.fade:not(.show){opacity:0}.collapse:not(.show){display:none}.collapsing{position:relative;height:0;overflow:hidden;transition:height .35s ease}@media (prefers-reduced-motion:reduce){.collapsing{transition:none}}.dropdown,.dropleft,.dropright,.dropup{position:relative}.dropdown-toggle{white-space:nowrap}.dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:"";border-top:.3em solid;border-right:.3em solid transparent;border-bottom:0;border-left:.3em solid transparent}.dropdown-toggle:empty::after{margin-left:0}.dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;min-width:10rem;padding:.5rem 0;margin:.125rem 0 0;font-size:1rem;color:#212529;text-align:left;list-style:none;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,.15);border-radius:.25rem}.dropdown-menu-left{right:auto;left:0}.dropdown-menu-right{right:0;left:auto}@media (min-width:576px){.dropdown-menu-sm-left{right:auto;left:0}.dropdown-menu-sm-right{right:0;left:auto}}@media (min-width:768px){.dropdown-menu-md-left{right:auto;left:0}.dropdown-menu-md-right{right:0;left:auto}}@media (min-width:992px){.dropdown-menu-lg-left{right:auto;left:0}.dropdown-menu-lg-right{right:0;left:auto}}@media (min-width:1200px){.dropdown-menu-xl-left{right:auto;left:0}.dropdown-menu-xl-right{right:0;left:auto}}.dropup .dropdown-menu{top:auto;bottom:100%;margin-top:0;margin-bottom:.125rem}.dropup .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:"";border-top:0;border-right:.3em solid transparent;border-bottom:.3em solid;border-left:.3em solid transparent}.dropup .dropdown-toggle:empty::after{margin-left:0}.dropright .dropdown-menu{top:0;right:auto;left:100%;margin-top:0;margin-left:.125rem}.dropright .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:"";border-top:.3em solid transparent;border-right:0;border-bottom:.3em solid transparent;border-left:.3em solid}.dropright .dropdown-toggle:empty::after{margin-left:0}.dropright .dropdown-toggle::after{vertical-align:0}.dropleft .dropdown-menu{top:0;right:100%;left:auto;margin-top:0;margin-right:.125rem}.dropleft .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:""}.dropleft .dropdown-toggle::after{display:none}.dropleft .dropdown-toggle::before{display:inline-block;margin-right:.255em;vertical-align:.255em;content:"";border-top:.3em solid transparent;border-right:.3em solid;border-bottom:.3em solid transparent}.dropleft .dropdown-toggle:empty::after{margin-left:0}.dropleft .dropdown-toggle::before{vertical-align:0}.dropdown-menu[x-placement^=bottom],.dropdown-menu[x-placement^=left],.dropdown-menu[x-placement^=right],.dropdown-menu[x-placement^=top]{right:auto;bottom:auto}.dropdown-divider{height:0;margin:.5rem 0;overflow:hidden;border-top:1px solid #e9ecef}.dropdown-item{display:block;width:100%;padding:.25rem 1.5rem;clear:both;font-weight:400;color:#212529;text-align:inherit;white-space:nowrap;background-color:transparent;border:0}.dropdown-item:focus,.dropdown-item:hover{color:#16181b;text-decoration:none;background-color:#f8f9fa}.dropdown-item.active,.dropdown-item:active{color:#fff;text-decoration:none;background-color:#007bff}.dropdown-item.disabled,.dropdown-item:disabled{color:#6c757d;pointer-events:none;background-color:transparent}.dropdown-menu.show{display:block}.dropdown-header{display:block;padding:.5rem 1.5rem;margin-bottom:0;font-size:.875rem;color:#6c757d;white-space:nowrap}.dropdown-item-text{display:block;padding:.25rem 1.5rem;color:#212529}.btn-group,.btn-group-vertical{position:relative;display:-ms-inline-flexbox;display:inline-flex;vertical-align:middle}.btn-group-vertical>.btn,.btn-group>.btn{position:relative;-ms-flex:1 1 auto;flex:1 1 auto}.btn-group-vertical>.btn:hover,.btn-group>.btn:hover{z-index:1}.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus{z-index:1}.btn-toolbar{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start}.btn-toolbar .input-group{width:auto}.btn-group>.btn-group:not(:first-child),.btn-group>.btn:not(:first-child){margin-left:-1px}.btn-group>.btn-group:not(:last-child)>.btn,.btn-group>.btn:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn-group:not(:first-child)>.btn,.btn-group>.btn:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.dropdown-toggle-split{padding-right:.5625rem;padding-left:.5625rem}.dropdown-toggle-split::after,.dropright .dropdown-toggle-split::after,.dropup .dropdown-toggle-split::after{margin-left:0}.dropleft .dropdown-toggle-split::before{margin-right:0}.btn-group-sm>.btn+.dropdown-toggle-split,.btn-sm+.dropdown-toggle-split{padding-right:.375rem;padding-left:.375rem}.btn-group-lg>.btn+.dropdown-toggle-split,.btn-lg+.dropdown-toggle-split{padding-right:.75rem;padding-left:.75rem}.btn-group-vertical{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:flex-start;-ms-flex-pack:center;justify-content:center}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group{width:100%}.btn-group-vertical>.btn-group:not(:first-child),.btn-group-vertical>.btn:not(:first-child){margin-top:-1px}.btn-group-vertical>.btn-group:not(:last-child)>.btn,.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle){border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn-group:not(:first-child)>.btn,.btn-group-vertical>.btn:not(:first-child){border-top-left-radius:0;border-top-right-radius:0}.btn-group-toggle>.btn,.btn-group-toggle>.btn-group>.btn{margin-bottom:0}.btn-group-toggle>.btn input[type=checkbox],.btn-group-toggle>.btn input[type=radio],.btn-group-toggle>.btn-group>.btn input[type=checkbox],.btn-group-toggle>.btn-group>.btn input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.input-group{position:relative;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:stretch;align-items:stretch;width:100%}.input-group>.custom-file,.input-group>.custom-select,.input-group>.form-control,.input-group>.form-control-plaintext{position:relative;-ms-flex:1 1 auto;flex:1 1 auto;width:1%;margin-bottom:0}.input-group>.custom-file+.custom-file,.input-group>.custom-file+.custom-select,.input-group>.custom-file+.form-control,.input-group>.custom-select+.custom-file,.input-group>.custom-select+.custom-select,.input-group>.custom-select+.form-control,.input-group>.form-control+.custom-file,.input-group>.form-control+.custom-select,.input-group>.form-control+.form-control,.input-group>.form-control-plaintext+.custom-file,.input-group>.form-control-plaintext+.custom-select,.input-group>.form-control-plaintext+.form-control{margin-left:-1px}.input-group>.custom-file .custom-file-input:focus~.custom-file-label,.input-group>.custom-select:focus,.input-group>.form-control:focus{z-index:3}.input-group>.custom-file .custom-file-input:focus{z-index:4}.input-group>.custom-select:not(:last-child),.input-group>.form-control:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0}.input-group>.custom-select:not(:first-child),.input-group>.form-control:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.input-group>.custom-file{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.input-group>.custom-file:not(:last-child) .custom-file-label,.input-group>.custom-file:not(:last-child) .custom-file-label::after{border-top-right-radius:0;border-bottom-right-radius:0}.input-group>.custom-file:not(:first-child) .custom-file-label{border-top-left-radius:0;border-bottom-left-radius:0}.input-group-append,.input-group-prepend{display:-ms-flexbox;display:flex}.input-group-append .btn,.input-group-prepend .btn{position:relative;z-index:2}.input-group-append .btn:focus,.input-group-prepend .btn:focus{z-index:3}.input-group-append .btn+.btn,.input-group-append .btn+.input-group-text,.input-group-append .input-group-text+.btn,.input-group-append .input-group-text+.input-group-text,.input-group-prepend .btn+.btn,.input-group-prepend .btn+.input-group-text,.input-group-prepend .input-group-text+.btn,.input-group-prepend .input-group-text+.input-group-text{margin-left:-1px}.input-group-prepend{margin-right:-1px}.input-group-append{margin-left:-1px}.input-group-text{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:.375rem .75rem;margin-bottom:0;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;text-align:center;white-space:nowrap;background-color:#e9ecef;border:1px solid #ced4da;border-radius:.25rem}.input-group-text input[type=checkbox],.input-group-text input[type=radio]{margin-top:0}.input-group-lg>.custom-select,.input-group-lg>.form-control:not(textarea){height:calc(1.5em + 1rem + 2px)}.input-group-lg>.custom-select,.input-group-lg>.form-control,.input-group-lg>.input-group-append>.btn,.input-group-lg>.input-group-append>.input-group-text,.input-group-lg>.input-group-prepend>.btn,.input-group-lg>.input-group-prepend>.input-group-text{padding:.5rem 1rem;font-size:1.25rem;line-height:1.5;border-radius:.3rem}.input-group-sm>.custom-select,.input-group-sm>.form-control:not(textarea){height:calc(1.5em + .5rem + 2px)}.input-group-sm>.custom-select,.input-group-sm>.form-control,.input-group-sm>.input-group-append>.btn,.input-group-sm>.input-group-append>.input-group-text,.input-group-sm>.input-group-prepend>.btn,.input-group-sm>.input-group-prepend>.input-group-text{padding:.25rem .5rem;font-size:.875rem;line-height:1.5;border-radius:.2rem}.input-group-lg>.custom-select,.input-group-sm>.custom-select{padding-right:1.75rem}.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),.input-group>.input-group-append:not(:last-child)>.btn,.input-group>.input-group-append:not(:last-child)>.input-group-text,.input-group>.input-group-prepend>.btn,.input-group>.input-group-prepend>.input-group-text{border-top-right-radius:0;border-bottom-right-radius:0}.input-group>.input-group-append>.btn,.input-group>.input-group-append>.input-group-text,.input-group>.input-group-prepend:first-child>.btn:not(:first-child),.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),.input-group>.input-group-prepend:not(:first-child)>.btn,.input-group>.input-group-prepend:not(:first-child)>.input-group-text{border-top-left-radius:0;border-bottom-left-radius:0}.custom-control{position:relative;display:block;min-height:1.5rem;padding-left:1.5rem}.custom-control-inline{display:-ms-inline-flexbox;display:inline-flex;margin-right:1rem}.custom-control-input{position:absolute;z-index:-1;opacity:0}.custom-control-input:checked~.custom-control-label::before{color:#fff;border-color:#007bff;background-color:#007bff}.custom-control-input:focus~.custom-control-label::before{box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.custom-control-input:focus:not(:checked)~.custom-control-label::before{border-color:#80bdff}.custom-control-input:not(:disabled):active~.custom-control-label::before{color:#fff;background-color:#b3d7ff;border-color:#b3d7ff}.custom-control-input:disabled~.custom-control-label{color:#6c757d}.custom-control-input:disabled~.custom-control-label::before{background-color:#e9ecef}.custom-control-label{position:relative;margin-bottom:0;vertical-align:top}.custom-control-label::before{position:absolute;top:.25rem;left:-1.5rem;display:block;width:1rem;height:1rem;pointer-events:none;content:"";background-color:#fff;border:#adb5bd solid 1px}.custom-control-label::after{position:absolute;top:.25rem;left:-1.5rem;display:block;width:1rem;height:1rem;content:"";background:no-repeat 50%/50% 50%}.custom-checkbox .custom-control-label::before{border-radius:.25rem}.custom-checkbox .custom-control-input:checked~.custom-control-label::after{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e")}.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before{border-color:#007bff;background-color:#007bff}.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::after{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e")}.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before{background-color:rgba(0,123,255,.5)}.custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label::before{background-color:rgba(0,123,255,.5)}.custom-radio .custom-control-label::before{border-radius:50%}.custom-radio .custom-control-input:checked~.custom-control-label::after{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e")}.custom-radio .custom-control-input:disabled:checked~.custom-control-label::before{background-color:rgba(0,123,255,.5)}.custom-switch{padding-left:2.25rem}.custom-switch .custom-control-label::before{left:-2.25rem;width:1.75rem;pointer-events:all;border-radius:.5rem}.custom-switch .custom-control-label::after{top:calc(.25rem + 2px);left:calc(-2.25rem + 2px);width:calc(1rem - 4px);height:calc(1rem - 4px);background-color:#adb5bd;border-radius:.5rem;transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-transform .15s ease-in-out;transition:transform .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;transition:transform .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-transform .15s ease-in-out}@media (prefers-reduced-motion:reduce){.custom-switch .custom-control-label::after{transition:none}}.custom-switch .custom-control-input:checked~.custom-control-label::after{background-color:#fff;-webkit-transform:translateX(.75rem);transform:translateX(.75rem)}.custom-switch .custom-control-input:disabled:checked~.custom-control-label::before{background-color:rgba(0,123,255,.5)}.custom-select{display:inline-block;width:100%;height:calc(1.5em + .75rem + 2px);padding:.375rem 1.75rem .375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;vertical-align:middle;background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px;background-color:#fff;border:1px solid #ced4da;border-radius:.25rem;-webkit-appearance:none;-moz-appearance:none;appearance:none}.custom-select:focus{border-color:#80bdff;outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.custom-select:focus::-ms-value{color:#495057;background-color:#fff}.custom-select[multiple],.custom-select[size]:not([size="1"]){height:auto;padding-right:.75rem;background-image:none}.custom-select:disabled{color:#6c757d;background-color:#e9ecef}.custom-select::-ms-expand{display:none}.custom-select-sm{height:calc(1.5em + .5rem + 2px);padding-top:.25rem;padding-bottom:.25rem;padding-left:.5rem;font-size:.875rem}.custom-select-lg{height:calc(1.5em + 1rem + 2px);padding-top:.5rem;padding-bottom:.5rem;padding-left:1rem;font-size:1.25rem}.custom-file{position:relative;display:inline-block;width:100%;height:calc(1.5em + .75rem + 2px);margin-bottom:0}.custom-file-input{position:relative;z-index:2;width:100%;height:calc(1.5em + .75rem + 2px);margin:0;opacity:0}.custom-file-input:focus~.custom-file-label{border-color:#80bdff;box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.custom-file-input:disabled~.custom-file-label{background-color:#e9ecef}.custom-file-input:lang(en)~.custom-file-label::after{content:"Browse"}.custom-file-input~.custom-file-label[data-browse]::after{content:attr(data-browse)}.custom-file-label{position:absolute;top:0;right:0;left:0;z-index:1;height:calc(1.5em + .75rem + 2px);padding:.375rem .75rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;border:1px solid #ced4da;border-radius:.25rem}.custom-file-label::after{position:absolute;top:0;right:0;bottom:0;z-index:3;display:block;height:calc(1.5em + .75rem);padding:.375rem .75rem;line-height:1.5;color:#495057;content:"Browse";background-color:#e9ecef;border-left:inherit;border-radius:0 .25rem .25rem 0}.custom-range{width:100%;height:calc(1rem + .4rem);padding:0;background-color:transparent;-webkit-appearance:none;-moz-appearance:none;appearance:none}.custom-range:focus{outline:0}.custom-range:focus::-webkit-hero-thumb{box-shadow:0 0 0 1px #fff,0 0 0 .2rem rgba(0,123,255,.25)}.custom-range:focus::-moz-range-thumb{box-shadow:0 0 0 1px #fff,0 0 0 .2rem rgba(0,123,255,.25)}.custom-range:focus::-ms-thumb{box-shadow:0 0 0 1px #fff,0 0 0 .2rem rgba(0,123,255,.25)}.custom-range::-moz-focus-outer{border:0}.custom-range::-webkit-hero-thumb{width:1rem;height:1rem;margin-top:-.25rem;background-color:#007bff;border:0;border-radius:1rem;transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;-webkit-appearance:none;appearance:none}@media (prefers-reduced-motion:reduce){.custom-range::-webkit-hero-thumb{transition:none}}.custom-range::-webkit-hero-thumb:active{background-color:#b3d7ff}.custom-range::-webkit-hero-runnable-track{width:100%;height:.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}.custom-range::-moz-range-thumb{width:1rem;height:1rem;background-color:#007bff;border:0;border-radius:1rem;transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;-moz-appearance:none;appearance:none}@media (prefers-reduced-motion:reduce){.custom-range::-moz-range-thumb{transition:none}}.custom-range::-moz-range-thumb:active{background-color:#b3d7ff}.custom-range::-moz-range-track{width:100%;height:.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}.custom-range::-ms-thumb{width:1rem;height:1rem;margin-top:0;margin-right:.2rem;margin-left:.2rem;background-color:#007bff;border:0;border-radius:1rem;transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;appearance:none}@media (prefers-reduced-motion:reduce){.custom-range::-ms-thumb{transition:none}}.custom-range::-ms-thumb:active{background-color:#b3d7ff}.custom-range::-ms-track{width:100%;height:.5rem;color:transparent;cursor:pointer;background-color:transparent;border-color:transparent;border-width:.5rem}.custom-range::-ms-fill-lower{background-color:#dee2e6;border-radius:1rem}.custom-range::-ms-fill-upper{margin-right:15px;background-color:#dee2e6;border-radius:1rem}.custom-range:disabled::-webkit-hero-thumb{background-color:#adb5bd}.custom-range:disabled::-webkit-hero-runnable-track{cursor:default}.custom-range:disabled::-moz-range-thumb{background-color:#adb5bd}.custom-range:disabled::-moz-range-track{cursor:default}.custom-range:disabled::-ms-thumb{background-color:#adb5bd}.custom-control-label::before,.custom-file-label,.custom-select{transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}@media (prefers-reduced-motion:reduce){.custom-control-label::before,.custom-file-label,.custom-select{transition:none}}.nav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;padding-left:0;margin-bottom:0;list-style:none}.nav-link{display:block;padding:.5rem 1rem}.nav-link:focus,.nav-link:hover{text-decoration:none}.nav-link.disabled{color:#6c757d;pointer-events:none;cursor:default}.nav-tabs{border-bottom:1px solid #dee2e6}.nav-tabs .nav-item{margin-bottom:-1px}.nav-tabs .nav-link{border:1px solid transparent;border-top-left-radius:.25rem;border-top-right-radius:.25rem}.nav-tabs .nav-link:focus,.nav-tabs .nav-link:hover{border-color:#e9ecef #e9ecef #dee2e6}.nav-tabs .nav-link.disabled{color:#6c757d;background-color:transparent;border-color:transparent}.nav-tabs .nav-item.show .nav-link,.nav-tabs .nav-link.active{color:#495057;background-color:#fff;border-color:#dee2e6 #dee2e6 #fff}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.nav-pills .nav-link{border-radius:.25rem}.nav-pills .nav-link.active,.nav-pills .show>.nav-link{color:#fff;background-color:#007bff}.nav-fill .nav-item{-ms-flex:1 1 auto;flex:1 1 auto;text-align:center}.nav-justified .nav-item{-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;text-align:center}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.navbar{position:relative;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;padding:.5rem 1rem}.navbar>.container,.navbar>.container-fluid{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}.navbar-brand{display:inline-block;padding-top:.3125rem;padding-bottom:.3125rem;margin-right:1rem;font-size:1.25rem;line-height:inherit;white-space:nowrap}.navbar-brand:focus,.navbar-brand:hover{text-decoration:none}.navbar-nav{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;padding-left:0;margin-bottom:0;list-style:none}.navbar-nav .nav-link{padding-right:0;padding-left:0}.navbar-nav .dropdown-menu{position:static;float:none}.navbar-text{display:inline-block;padding-top:.5rem;padding-bottom:.5rem}.navbar-collapse{-ms-flex-preferred-size:100%;flex-basis:100%;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}.navbar-toggler{padding:.25rem .75rem;font-size:1.25rem;line-height:1;background-color:transparent;border:1px solid transparent;border-radius:.25rem}.navbar-toggler:focus,.navbar-toggler:hover{text-decoration:none}.navbar-toggler-icon{display:inline-block;width:1.5em;height:1.5em;vertical-align:middle;content:"";background:no-repeat center center;background-size:100% 100%}@media (max-width:575.98px){.navbar-expand-sm>.container,.navbar-expand-sm>.container-fluid{padding-right:0;padding-left:0}}@media (min-width:576px){.navbar-expand-sm{-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-pack:start;justify-content:flex-start}.navbar-expand-sm .navbar-nav{-ms-flex-direction:row;flex-direction:row}.navbar-expand-sm .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-sm .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-sm>.container,.navbar-expand-sm>.container-fluid{-ms-flex-wrap:nowrap;flex-wrap:nowrap}.navbar-expand-sm .navbar-collapse{display:-ms-flexbox!important;display:flex!important;-ms-flex-preferred-size:auto;flex-basis:auto}.navbar-expand-sm .navbar-toggler{display:none}}@media (max-width:767.98px){.navbar-expand-md>.container,.navbar-expand-md>.container-fluid{padding-right:0;padding-left:0}}@media (min-width:768px){.navbar-expand-md{-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-pack:start;justify-content:flex-start}.navbar-expand-md .navbar-nav{-ms-flex-direction:row;flex-direction:row}.navbar-expand-md .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-md .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-md>.container,.navbar-expand-md>.container-fluid{-ms-flex-wrap:nowrap;flex-wrap:nowrap}.navbar-expand-md .navbar-collapse{display:-ms-flexbox!important;display:flex!important;-ms-flex-preferred-size:auto;flex-basis:auto}.navbar-expand-md .navbar-toggler{display:none}}@media (max-width:991.98px){.navbar-expand-lg>.container,.navbar-expand-lg>.container-fluid{padding-right:0;padding-left:0}}@media (min-width:992px){.navbar-expand-lg{-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-pack:start;justify-content:flex-start}.navbar-expand-lg .navbar-nav{-ms-flex-direction:row;flex-direction:row}.navbar-expand-lg .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-lg .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-lg>.container,.navbar-expand-lg>.container-fluid{-ms-flex-wrap:nowrap;flex-wrap:nowrap}.navbar-expand-lg .navbar-collapse{display:-ms-flexbox!important;display:flex!important;-ms-flex-preferred-size:auto;flex-basis:auto}.navbar-expand-lg .navbar-toggler{display:none}}@media (max-width:1199.98px){.navbar-expand-xl>.container,.navbar-expand-xl>.container-fluid{padding-right:0;padding-left:0}}@media (min-width:1200px){.navbar-expand-xl{-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-pack:start;justify-content:flex-start}.navbar-expand-xl .navbar-nav{-ms-flex-direction:row;flex-direction:row}.navbar-expand-xl .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-xl .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-xl>.container,.navbar-expand-xl>.container-fluid{-ms-flex-wrap:nowrap;flex-wrap:nowrap}.navbar-expand-xl .navbar-collapse{display:-ms-flexbox!important;display:flex!important;-ms-flex-preferred-size:auto;flex-basis:auto}.navbar-expand-xl .navbar-toggler{display:none}}.navbar-expand{-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-pack:start;justify-content:flex-start}.navbar-expand>.container,.navbar-expand>.container-fluid{padding-right:0;padding-left:0}.navbar-expand .navbar-nav{-ms-flex-direction:row;flex-direction:row}.navbar-expand .navbar-nav .dropdown-menu{position:absolute}.navbar-expand .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand>.container,.navbar-expand>.container-fluid{-ms-flex-wrap:nowrap;flex-wrap:nowrap}.navbar-expand .navbar-collapse{display:-ms-flexbox!important;display:flex!important;-ms-flex-preferred-size:auto;flex-basis:auto}.navbar-expand .navbar-toggler{display:none}.navbar-light .navbar-brand{color:rgba(0,0,0,.9)}.navbar-light .navbar-brand:focus,.navbar-light .navbar-brand:hover{color:rgba(0,0,0,.9)}.navbar-light .navbar-nav .nav-link{color:rgba(0,0,0,.5)}.navbar-light .navbar-nav .nav-link:focus,.navbar-light .navbar-nav .nav-link:hover{color:rgba(0,0,0,.7)}.navbar-light .navbar-nav .nav-link.disabled{color:rgba(0,0,0,.3)}.navbar-light .navbar-nav .active>.nav-link,.navbar-light .navbar-nav .nav-link.active,.navbar-light .navbar-nav .nav-link.show,.navbar-light .navbar-nav .show>.nav-link{color:rgba(0,0,0,.9)}.navbar-light .navbar-toggler{color:rgba(0,0,0,.5);border-color:rgba(0,0,0,.1)}.navbar-light .navbar-toggler-icon{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e")}.navbar-light .navbar-text{color:rgba(0,0,0,.5)}.navbar-light .navbar-text a{color:rgba(0,0,0,.9)}.navbar-light .navbar-text a:focus,.navbar-light .navbar-text a:hover{color:rgba(0,0,0,.9)}.navbar-dark .navbar-brand{color:#fff}.navbar-dark .navbar-brand:focus,.navbar-dark .navbar-brand:hover{color:#fff}.navbar-dark .navbar-nav .nav-link{color:rgba(255,255,255,.5)}.navbar-dark .navbar-nav .nav-link:focus,.navbar-dark .navbar-nav .nav-link:hover{color:rgba(255,255,255,.75)}.navbar-dark .navbar-nav .nav-link.disabled{color:rgba(255,255,255,.25)}.navbar-dark .navbar-nav .active>.nav-link,.navbar-dark .navbar-nav .nav-link.active,.navbar-dark .navbar-nav .nav-link.show,.navbar-dark .navbar-nav .show>.nav-link{color:#fff}.navbar-dark .navbar-toggler{color:rgba(255,255,255,.5);border-color:rgba(255,255,255,.1)}.navbar-dark .navbar-toggler-icon{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e")}.navbar-dark .navbar-text{color:rgba(255,255,255,.5)}.navbar-dark .navbar-text a{color:#fff}.navbar-dark .navbar-text a:focus,.navbar-dark .navbar-text a:hover{color:#fff}.card{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(0,0,0,.125);border-radius:.25rem}.card>hr{margin-right:0;margin-left:0}.card>.list-group:first-child .list-group-item:first-child{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.card>.list-group:last-child .list-group-item:last-child{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.card-body{-ms-flex:1 1 auto;flex:1 1 auto;padding:1.25rem}.card-title{margin-bottom:.75rem}.card-subtitle{margin-top:-.375rem;margin-bottom:0}.card-text:last-child{margin-bottom:0}.card-link:hover{text-decoration:none}.card-link+.card-link{margin-left:1.25rem}.card-header{padding:.75rem 1.25rem;margin-bottom:0;background-color:rgba(0,0,0,.03);border-bottom:1px solid rgba(0,0,0,.125)}.card-header:first-child{border-radius:calc(.25rem - 1px) calc(.25rem - 1px) 0 0}.card-header+.list-group .list-group-item:first-child{border-top:0}.card-footer{padding:.75rem 1.25rem;background-color:rgba(0,0,0,.03);border-top:1px solid rgba(0,0,0,.125)}.card-footer:last-child{border-radius:0 0 calc(.25rem - 1px) calc(.25rem - 1px)}.card-header-tabs{margin-right:-.625rem;margin-bottom:-.75rem;margin-left:-.625rem;border-bottom:0}.card-header-pills{margin-right:-.625rem;margin-left:-.625rem}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:1.25rem}.card-img{width:100%;border-radius:calc(.25rem - 1px)}.card-img-top{width:100%;border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.card-img-bottom{width:100%;border-bottom-right-radius:calc(.25rem - 1px);border-bottom-left-radius:calc(.25rem - 1px)}.card-deck{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.card-deck .card{margin-bottom:15px}@media (min-width:576px){.card-deck{-ms-flex-flow:row wrap;flex-flow:row wrap;margin-right:-15px;margin-left:-15px}.card-deck .card{display:-ms-flexbox;display:flex;-ms-flex:1 0 0%;flex:1 0 0%;-ms-flex-direction:column;flex-direction:column;margin-right:15px;margin-bottom:0;margin-left:15px}}.card-group{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.card-group>.card{margin-bottom:15px}@media (min-width:576px){.card-group{-ms-flex-flow:row wrap;flex-flow:row wrap}.card-group>.card{-ms-flex:1 0 0%;flex:1 0 0%;margin-bottom:0}.card-group>.card+.card{margin-left:0;border-left:0}.card-group>.card:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0}.card-group>.card:not(:last-child) .card-header,.card-group>.card:not(:last-child) .card-img-top{border-top-right-radius:0}.card-group>.card:not(:last-child) .card-footer,.card-group>.card:not(:last-child) .card-img-bottom{border-bottom-right-radius:0}.card-group>.card:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.card-group>.card:not(:first-child) .card-header,.card-group>.card:not(:first-child) .card-img-top{border-top-left-radius:0}.card-group>.card:not(:first-child) .card-footer,.card-group>.card:not(:first-child) .card-img-bottom{border-bottom-left-radius:0}}.card-columns .card{margin-bottom:.75rem}@media (min-width:576px){.card-columns{-webkit-column-count:3;-moz-column-count:3;column-count:3;-webkit-column-gap:1.25rem;-moz-column-gap:1.25rem;column-gap:1.25rem;orphans:1;widows:1}.card-columns .card{display:inline-block;width:100%}}.accordion>.card{overflow:hidden}.accordion>.card:not(:first-of-type) .card-header:first-child{border-radius:0}.accordion>.card:not(:first-of-type):not(:last-of-type){border-bottom:0;border-radius:0}.accordion>.card:first-of-type{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.accordion>.card:last-of-type{border-top-left-radius:0;border-top-right-radius:0}.accordion>.card .card-header{margin-bottom:-1px}.breadcrumb{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;padding:.75rem 1rem;margin-bottom:1rem;list-style:none;background-color:#e9ecef;border-radius:.25rem}.breadcrumb-item+.breadcrumb-item{padding-left:.5rem}.breadcrumb-item+.breadcrumb-item::before{display:inline-block;padding-right:.5rem;color:#6c757d;content:"/"}.breadcrumb-item+.breadcrumb-item:hover::before{text-decoration:underline}.breadcrumb-item+.breadcrumb-item:hover::before{text-decoration:none}.breadcrumb-item.active{color:#6c757d}.pagination{display:-ms-flexbox;display:flex;padding-left:0;list-style:none;border-radius:.25rem}.page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#007bff;background-color:#fff;border:1px solid #dee2e6}.page-link:hover{z-index:2;color:#0056b3;text-decoration:none;background-color:#e9ecef;border-color:#dee2e6}.page-link:focus{z-index:2;outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.25)}.page-item:first-child .page-link{margin-left:0;border-top-left-radius:.25rem;border-bottom-left-radius:.25rem}.page-item:last-child .page-link{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.page-item.active .page-link{z-index:1;color:#fff;background-color:#007bff;border-color:#007bff}.page-item.disabled .page-link{color:#6c757d;pointer-events:none;cursor:auto;background-color:#fff;border-color:#dee2e6}.pagination-lg .page-link{padding:.75rem 1.5rem;font-size:1.25rem;line-height:1.5}.pagination-lg .page-item:first-child .page-link{border-top-left-radius:.3rem;border-bottom-left-radius:.3rem}.pagination-lg .page-item:last-child .page-link{border-top-right-radius:.3rem;border-bottom-right-radius:.3rem}.pagination-sm .page-link{padding:.25rem .5rem;font-size:.875rem;line-height:1.5}.pagination-sm .page-item:first-child .page-link{border-top-left-radius:.2rem;border-bottom-left-radius:.2rem}.pagination-sm .page-item:last-child .page-link{border-top-right-radius:.2rem;border-bottom-right-radius:.2rem}.badge{display:inline-block;padding:.25em .4em;font-size:75%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}@media (prefers-reduced-motion:reduce){.badge{transition:none}}a.badge:focus,a.badge:hover{text-decoration:none}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.badge-pill{padding-right:.6em;padding-left:.6em;border-radius:10rem}.badge-primary{color:#fff;background-color:#007bff}a.badge-primary:focus,a.badge-primary:hover{color:#fff;background-color:#0062cc}a.badge-primary.focus,a.badge-primary:focus{outline:0;box-shadow:0 0 0 .2rem rgba(0,123,255,.5)}.badge-secondary{color:#fff;background-color:#6c757d}a.badge-secondary:focus,a.badge-secondary:hover{color:#fff;background-color:#545b62}a.badge-secondary.focus,a.badge-secondary:focus{outline:0;box-shadow:0 0 0 .2rem rgba(108,117,125,.5)}.badge-success{color:#fff;background-color:#28a745}a.badge-success:focus,a.badge-success:hover{color:#fff;background-color:#1e7e34}a.badge-success.focus,a.badge-success:focus{outline:0;box-shadow:0 0 0 .2rem rgba(40,167,69,.5)}.badge-info{color:#fff;background-color:#17a2b8}a.badge-info:focus,a.badge-info:hover{color:#fff;background-color:#117a8b}a.badge-info.focus,a.badge-info:focus{outline:0;box-shadow:0 0 0 .2rem rgba(23,162,184,.5)}.badge-warning{color:#212529;background-color:#ffc107}a.badge-warning:focus,a.badge-warning:hover{color:#212529;background-color:#d39e00}a.badge-warning.focus,a.badge-warning:focus{outline:0;box-shadow:0 0 0 .2rem rgba(255,193,7,.5)}.badge-danger{color:#fff;background-color:#dc3545}a.badge-danger:focus,a.badge-danger:hover{color:#fff;background-color:#bd2130}a.badge-danger.focus,a.badge-danger:focus{outline:0;box-shadow:0 0 0 .2rem rgba(220,53,69,.5)}.badge-light{color:#212529;background-color:#f8f9fa}a.badge-light:focus,a.badge-light:hover{color:#212529;background-color:#dae0e5}a.badge-light.focus,a.badge-light:focus{outline:0;box-shadow:0 0 0 .2rem rgba(248,249,250,.5)}.badge-dark{color:#fff;background-color:#343a40}a.badge-dark:focus,a.badge-dark:hover{color:#fff;background-color:#1d2124}a.badge-dark.focus,a.badge-dark:focus{outline:0;box-shadow:0 0 0 .2rem rgba(52,58,64,.5)}.jumbotron{padding:2rem 1rem;margin-bottom:2rem;background-color:#e9ecef;border-radius:.3rem}@media (min-width:576px){.jumbotron{padding:4rem 2rem}}.jumbotron-fluid{padding-right:0;padding-left:0;border-radius:0}.alert{position:relative;padding:.75rem 1.25rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.25rem}.alert-heading{color:inherit}.alert-link{font-weight:700}.alert-dismissible{padding-right:4rem}.alert-dismissible .close{position:absolute;top:0;right:0;padding:.75rem 1.25rem;color:inherit}.alert-primary{color:#004085;background-color:#cce5ff;border-color:#b8daff}.alert-primary hr{border-top-color:#9fcdff}.alert-primary .alert-link{color:#002752}.alert-secondary{color:#383d41;background-color:#e2e3e5;border-color:#d6d8db}.alert-secondary hr{border-top-color:#c8cbcf}.alert-secondary .alert-link{color:#202326}.alert-success{color:#155724;background-color:#d4edda;border-color:#c3e6cb}.alert-success hr{border-top-color:#b1dfbb}.alert-success .alert-link{color:#0b2e13}.alert-info{color:#0c5460;background-color:#d1ecf1;border-color:#bee5eb}.alert-info hr{border-top-color:#abdde5}.alert-info .alert-link{color:#062c33}.alert-warning{color:#856404;background-color:#fff3cd;border-color:#ffeeba}.alert-warning hr{border-top-color:#ffe8a1}.alert-warning .alert-link{color:#533f03}.alert-danger{color:#721c24;background-color:#f8d7da;border-color:#f5c6cb}.alert-danger hr{border-top-color:#f1b0b7}.alert-danger .alert-link{color:#491217}.alert-light{color:#818182;background-color:#fefefe;border-color:#fdfdfe}.alert-light hr{border-top-color:#ececf6}.alert-light .alert-link{color:#686868}.alert-dark{color:#1b1e21;background-color:#d6d8d9;border-color:#c6c8ca}.alert-dark hr{border-top-color:#b9bbbe}.alert-dark .alert-link{color:#040505}@-webkit-keyframes progress-bar-stripes{from{background-position:1rem 0}to{background-position:0 0}}@keyframes progress-bar-stripes{from{background-position:1rem 0}to{background-position:0 0}}.progress{display:-ms-flexbox;display:flex;height:1rem;overflow:hidden;font-size:.75rem;background-color:#e9ecef;border-radius:.25rem}.progress-bar{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;color:#fff;text-align:center;white-space:nowrap;background-color:#007bff;transition:width .6s ease}@media (prefers-reduced-motion:reduce){.progress-bar{transition:none}}.progress-bar-striped{background-image:linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);background-size:1rem 1rem}.progress-bar-animated{-webkit-animation:progress-bar-stripes 1s linear infinite;animation:progress-bar-stripes 1s linear infinite}@media (prefers-reduced-motion:reduce){.progress-bar-animated{-webkit-animation:none;animation:none}}.media{display:-ms-flexbox;display:flex;-ms-flex-align:start;align-items:flex-start}.media-body{-ms-flex:1;flex:1}.list-group{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;padding-left:0;margin-bottom:0}.list-group-item-action{width:100%;color:#495057;text-align:inherit}.list-group-item-action:focus,.list-group-item-action:hover{z-index:1;color:#495057;text-decoration:none;background-color:#f8f9fa}.list-group-item-action:active{color:#212529;background-color:#e9ecef}.list-group-item{position:relative;display:block;padding:.75rem 1.25rem;margin-bottom:-1px;background-color:#fff;border:1px solid rgba(0,0,0,.125)}.list-group-item:first-child{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.list-group-item:last-child{margin-bottom:0;border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.list-group-item.disabled,.list-group-item:disabled{color:#6c757d;pointer-events:none;background-color:#fff}.list-group-item.active{z-index:2;color:#fff;background-color:#007bff;border-color:#007bff}.list-group-horizontal{-ms-flex-direction:row;flex-direction:row}.list-group-horizontal .list-group-item{margin-right:-1px;margin-bottom:0}.list-group-horizontal .list-group-item:first-child{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal .list-group-item:last-child{margin-right:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem;border-bottom-left-radius:0}@media (min-width:576px){.list-group-horizontal-sm{-ms-flex-direction:row;flex-direction:row}.list-group-horizontal-sm .list-group-item{margin-right:-1px;margin-bottom:0}.list-group-horizontal-sm .list-group-item:first-child{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-sm .list-group-item:last-child{margin-right:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem;border-bottom-left-radius:0}}@media (min-width:768px){.list-group-horizontal-md{-ms-flex-direction:row;flex-direction:row}.list-group-horizontal-md .list-group-item{margin-right:-1px;margin-bottom:0}.list-group-horizontal-md .list-group-item:first-child{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-md .list-group-item:last-child{margin-right:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem;border-bottom-left-radius:0}}@media (min-width:992px){.list-group-horizontal-lg{-ms-flex-direction:row;flex-direction:row}.list-group-horizontal-lg .list-group-item{margin-right:-1px;margin-bottom:0}.list-group-horizontal-lg .list-group-item:first-child{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-lg .list-group-item:last-child{margin-right:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem;border-bottom-left-radius:0}}@media (min-width:1200px){.list-group-horizontal-xl{-ms-flex-direction:row;flex-direction:row}.list-group-horizontal-xl .list-group-item{margin-right:-1px;margin-bottom:0}.list-group-horizontal-xl .list-group-item:first-child{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-xl .list-group-item:last-child{margin-right:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem;border-bottom-left-radius:0}}.list-group-flush .list-group-item{border-right:0;border-left:0;border-radius:0}.list-group-flush .list-group-item:last-child{margin-bottom:-1px}.list-group-flush:first-child .list-group-item:first-child{border-top:0}.list-group-flush:last-child .list-group-item:last-child{margin-bottom:0;border-bottom:0}.list-group-item-primary{color:#004085;background-color:#b8daff}.list-group-item-primary.list-group-item-action:focus,.list-group-item-primary.list-group-item-action:hover{color:#004085;background-color:#9fcdff}.list-group-item-primary.list-group-item-action.active{color:#fff;background-color:#004085;border-color:#004085}.list-group-item-secondary{color:#383d41;background-color:#d6d8db}.list-group-item-secondary.list-group-item-action:focus,.list-group-item-secondary.list-group-item-action:hover{color:#383d41;background-color:#c8cbcf}.list-group-item-secondary.list-group-item-action.active{color:#fff;background-color:#383d41;border-color:#383d41}.list-group-item-success{color:#155724;background-color:#c3e6cb}.list-group-item-success.list-group-item-action:focus,.list-group-item-success.list-group-item-action:hover{color:#155724;background-color:#b1dfbb}.list-group-item-success.list-group-item-action.active{color:#fff;background-color:#155724;border-color:#155724}.list-group-item-info{color:#0c5460;background-color:#bee5eb}.list-group-item-info.list-group-item-action:focus,.list-group-item-info.list-group-item-action:hover{color:#0c5460;background-color:#abdde5}.list-group-item-info.list-group-item-action.active{color:#fff;background-color:#0c5460;border-color:#0c5460}.list-group-item-warning{color:#856404;background-color:#ffeeba}.list-group-item-warning.list-group-item-action:focus,.list-group-item-warning.list-group-item-action:hover{color:#856404;background-color:#ffe8a1}.list-group-item-warning.list-group-item-action.active{color:#fff;background-color:#856404;border-color:#856404}.list-group-item-danger{color:#721c24;background-color:#f5c6cb}.list-group-item-danger.list-group-item-action:focus,.list-group-item-danger.list-group-item-action:hover{color:#721c24;background-color:#f1b0b7}.list-group-item-danger.list-group-item-action.active{color:#fff;background-color:#721c24;border-color:#721c24}.list-group-item-light{color:#818182;background-color:#fdfdfe}.list-group-item-light.list-group-item-action:focus,.list-group-item-light.list-group-item-action:hover{color:#818182;background-color:#ececf6}.list-group-item-light.list-group-item-action.active{color:#fff;background-color:#818182;border-color:#818182}.list-group-item-dark{color:#1b1e21;background-color:#c6c8ca}.list-group-item-dark.list-group-item-action:focus,.list-group-item-dark.list-group-item-action:hover{color:#1b1e21;background-color:#b9bbbe}.list-group-item-dark.list-group-item-action.active{color:#fff;background-color:#1b1e21;border-color:#1b1e21}.close{float:right;font-size:1.5rem;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;opacity:.5}.close:hover{color:#000;text-decoration:none}.close:not(:disabled):not(.disabled):focus,.close:not(:disabled):not(.disabled):hover{opacity:.75}button.close{padding:0;background-color:transparent;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}a.close.disabled{pointer-events:none}.toast{max-width:350px;overflow:hidden;font-size:.875rem;background-color:rgba(255,255,255,.85);background-clip:padding-box;border:1px solid rgba(0,0,0,.1);box-shadow:0 .25rem .75rem rgba(0,0,0,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);opacity:0;border-radius:.25rem}.toast:not(:last-child){margin-bottom:.75rem}.toast.showing{opacity:1}.toast.show{display:block;opacity:1}.toast.hide{display:none}.toast-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;padding:.25rem .75rem;color:#6c757d;background-color:rgba(255,255,255,.85);background-clip:padding-box;border-bottom:1px solid rgba(0,0,0,.05)}.toast-body{padding:.75rem}.modal-open{overflow:hidden}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal{position:fixed;top:0;left:0;z-index:1050;display:none;width:100%;height:100%;overflow:hidden;outline:0}.modal-dialog{position:relative;width:auto;margin:.5rem;pointer-events:none}.modal.fade .modal-dialog{transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out,-webkit-transform .3s ease-out;-webkit-transform:translate(0,-50px);transform:translate(0,-50px)}@media (prefers-reduced-motion:reduce){.modal.fade .modal-dialog{transition:none}}.modal.show .modal-dialog{-webkit-transform:none;transform:none}.modal-dialog-scrollable{display:-ms-flexbox;display:flex;max-height:calc(100% - 1rem)}.modal-dialog-scrollable .modal-content{max-height:calc(100vh - 1rem);overflow:hidden}.modal-dialog-scrollable .modal-footer,.modal-dialog-scrollable .modal-header{-ms-flex-negative:0;flex-shrink:0}.modal-dialog-scrollable .modal-body{overflow-y:auto}.modal-dialog-centered{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;min-height:calc(100% - 1rem)}.modal-dialog-centered::before{display:block;height:calc(100vh - 1rem);content:""}.modal-dialog-centered.modal-dialog-scrollable{-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.modal-dialog-centered.modal-dialog-scrollable .modal-content{max-height:none}.modal-dialog-centered.modal-dialog-scrollable::before{content:none}.modal-content{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:100%;pointer-events:auto;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,.2);border-radius:.3rem;outline:0}.modal-backdrop{position:fixed;top:0;left:0;z-index:1040;width:100vw;height:100vh;background-color:#000}.modal-backdrop.fade{opacity:0}.modal-backdrop.show{opacity:.5}.modal-header{display:-ms-flexbox;display:flex;-ms-flex-align:start;align-items:flex-start;-ms-flex-pack:justify;justify-content:space-between;padding:1rem 1rem;border-bottom:1px solid #dee2e6;border-top-left-radius:.3rem;border-top-right-radius:.3rem}.modal-header .close{padding:1rem 1rem;margin:-1rem -1rem -1rem auto}.modal-title{margin-bottom:0;line-height:1.5}.modal-body{position:relative;-ms-flex:1 1 auto;flex:1 1 auto;padding:1rem}.modal-footer{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end;padding:1rem;border-top:1px solid #dee2e6;border-bottom-right-radius:.3rem;border-bottom-left-radius:.3rem}.modal-footer>:not(:first-child){margin-left:.25rem}.modal-footer>:not(:last-child){margin-right:.25rem}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:576px){.modal-dialog{max-width:500px;margin:1.75rem auto}.modal-dialog-scrollable{max-height:calc(100% - 3.5rem)}.modal-dialog-scrollable .modal-content{max-height:calc(100vh - 3.5rem)}.modal-dialog-centered{min-height:calc(100% - 3.5rem)}.modal-dialog-centered::before{height:calc(100vh - 3.5rem)}.modal-sm{max-width:300px}}@media (min-width:992px){.modal-lg,.modal-xl{max-width:800px}}@media (min-width:1200px){.modal-xl{max-width:1140px}}.tooltip{position:absolute;z-index:1070;display:block;margin:0;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.875rem;word-wrap:break-word;opacity:0}.tooltip.show{opacity:.9}.tooltip .arrow{position:absolute;display:block;width:.8rem;height:.4rem}.tooltip .arrow::before{position:absolute;content:"";border-color:transparent;border-style:solid}.bs-tooltip-auto[x-placement^=top],.bs-tooltip-top{padding:.4rem 0}.bs-tooltip-auto[x-placement^=top] .arrow,.bs-tooltip-top .arrow{bottom:0}.bs-tooltip-auto[x-placement^=top] .arrow::before,.bs-tooltip-top .arrow::before{top:0;border-width:.4rem .4rem 0;border-top-color:#000}.bs-tooltip-auto[x-placement^=right],.bs-tooltip-right{padding:0 .4rem}.bs-tooltip-auto[x-placement^=right] .arrow,.bs-tooltip-right .arrow{left:0;width:.4rem;height:.8rem}.bs-tooltip-auto[x-placement^=right] .arrow::before,.bs-tooltip-right .arrow::before{right:0;border-width:.4rem .4rem .4rem 0;border-right-color:#000}.bs-tooltip-auto[x-placement^=bottom],.bs-tooltip-bottom{padding:.4rem 0}.bs-tooltip-auto[x-placement^=bottom] .arrow,.bs-tooltip-bottom .arrow{top:0}.bs-tooltip-auto[x-placement^=bottom] .arrow::before,.bs-tooltip-bottom .arrow::before{bottom:0;border-width:0 .4rem .4rem;border-bottom-color:#000}.bs-tooltip-auto[x-placement^=left],.bs-tooltip-left{padding:0 .4rem}.bs-tooltip-auto[x-placement^=left] .arrow,.bs-tooltip-left .arrow{right:0;width:.4rem;height:.8rem}.bs-tooltip-auto[x-placement^=left] .arrow::before,.bs-tooltip-left .arrow::before{left:0;border-width:.4rem 0 .4rem .4rem;border-left-color:#000}.tooltip-inner{max-width:200px;padding:.25rem .5rem;color:#fff;text-align:center;background-color:#000;border-radius:.25rem}.popover{position:absolute;top:0;left:0;z-index:1060;display:block;max-width:276px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.875rem;word-wrap:break-word;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,.2);border-radius:.3rem}.popover .arrow{position:absolute;display:block;width:1rem;height:.5rem;margin:0 .3rem}.popover .arrow::after,.popover .arrow::before{position:absolute;display:block;content:"";border-color:transparent;border-style:solid}.bs-popover-auto[x-placement^=top],.bs-popover-top{margin-bottom:.5rem}.bs-popover-auto[x-placement^=top]>.arrow,.bs-popover-top>.arrow{bottom:calc((.5rem + 1px) * -1)}.bs-popover-auto[x-placement^=top]>.arrow::before,.bs-popover-top>.arrow::before{bottom:0;border-width:.5rem .5rem 0;border-top-color:rgba(0,0,0,.25)}.bs-popover-auto[x-placement^=top]>.arrow::after,.bs-popover-top>.arrow::after{bottom:1px;border-width:.5rem .5rem 0;border-top-color:#fff}.bs-popover-auto[x-placement^=right],.bs-popover-right{margin-left:.5rem}.bs-popover-auto[x-placement^=right]>.arrow,.bs-popover-right>.arrow{left:calc((.5rem + 1px) * -1);width:.5rem;height:1rem;margin:.3rem 0}.bs-popover-auto[x-placement^=right]>.arrow::before,.bs-popover-right>.arrow::before{left:0;border-width:.5rem .5rem .5rem 0;border-right-color:rgba(0,0,0,.25)}.bs-popover-auto[x-placement^=right]>.arrow::after,.bs-popover-right>.arrow::after{left:1px;border-width:.5rem .5rem .5rem 0;border-right-color:#fff}.bs-popover-auto[x-placement^=bottom],.bs-popover-bottom{margin-top:.5rem}.bs-popover-auto[x-placement^=bottom]>.arrow,.bs-popover-bottom>.arrow{top:calc((.5rem + 1px) * -1)}.bs-popover-auto[x-placement^=bottom]>.arrow::before,.bs-popover-bottom>.arrow::before{top:0;border-width:0 .5rem .5rem .5rem;border-bottom-color:rgba(0,0,0,.25)}.bs-popover-auto[x-placement^=bottom]>.arrow::after,.bs-popover-bottom>.arrow::after{top:1px;border-width:0 .5rem .5rem .5rem;border-bottom-color:#fff}.bs-popover-auto[x-placement^=bottom] .popover-header::before,.bs-popover-bottom .popover-header::before{position:absolute;top:0;left:50%;display:block;width:1rem;margin-left:-.5rem;content:"";border-bottom:1px solid #f7f7f7}.bs-popover-auto[x-placement^=left],.bs-popover-left{margin-right:.5rem}.bs-popover-auto[x-placement^=left]>.arrow,.bs-popover-left>.arrow{right:calc((.5rem + 1px) * -1);width:.5rem;height:1rem;margin:.3rem 0}.bs-popover-auto[x-placement^=left]>.arrow::before,.bs-popover-left>.arrow::before{right:0;border-width:.5rem 0 .5rem .5rem;border-left-color:rgba(0,0,0,.25)}.bs-popover-auto[x-placement^=left]>.arrow::after,.bs-popover-left>.arrow::after{right:1px;border-width:.5rem 0 .5rem .5rem;border-left-color:#fff}.popover-header{padding:.5rem .75rem;margin-bottom:0;font-size:1rem;background-color:#f7f7f7;border-bottom:1px solid #ebebeb;border-top-left-radius:calc(.3rem - 1px);border-top-right-radius:calc(.3rem - 1px)}.popover-header:empty{display:none}.popover-body{padding:.5rem .75rem;color:#212529}.carousel{position:relative}.carousel.pointer-event{-ms-touch-action:pan-y;touch-action:pan-y}.carousel-inner{position:relative;width:100%;overflow:hidden}.carousel-inner::after{display:block;clear:both;content:""}.carousel-item{position:relative;display:none;float:left;width:100%;margin-right:-100%;-webkit-backface-visibility:hidden;backface-visibility:hidden;transition:-webkit-transform .6s ease-in-out;transition:transform .6s ease-in-out;transition:transform .6s ease-in-out,-webkit-transform .6s ease-in-out}@media (prefers-reduced-motion:reduce){.carousel-item{transition:none}}.carousel-item-next,.carousel-item-prev,.carousel-item.active{display:block}.active.carousel-item-right,.carousel-item-next:not(.carousel-item-left){-webkit-transform:translateX(100%);transform:translateX(100%)}.active.carousel-item-left,.carousel-item-prev:not(.carousel-item-right){-webkit-transform:translateX(-100%);transform:translateX(-100%)}.carousel-fade .carousel-item{opacity:0;transition-property:opacity;-webkit-transform:none;transform:none}.carousel-fade .carousel-item-next.carousel-item-left,.carousel-fade .carousel-item-prev.carousel-item-right,.carousel-fade .carousel-item.active{z-index:1;opacity:1}.carousel-fade .active.carousel-item-left,.carousel-fade .active.carousel-item-right{z-index:0;opacity:0;transition:0s .6s opacity}@media (prefers-reduced-motion:reduce){.carousel-fade .active.carousel-item-left,.carousel-fade .active.carousel-item-right{transition:none}}.carousel-control-next,.carousel-control-prev{position:absolute;top:0;bottom:0;z-index:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:15%;color:#fff;text-align:center;opacity:.5;transition:opacity .15s ease}@media (prefers-reduced-motion:reduce){.carousel-control-next,.carousel-control-prev{transition:none}}.carousel-control-next:focus,.carousel-control-next:hover,.carousel-control-prev:focus,.carousel-control-prev:hover{color:#fff;text-decoration:none;outline:0;opacity:.9}.carousel-control-prev{left:0}.carousel-control-next{right:0}.carousel-control-next-icon,.carousel-control-prev-icon{display:inline-block;width:20px;height:20px;background:no-repeat 50%/100% 100%}.carousel-control-prev-icon{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e")}.carousel-control-next-icon{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e")}.carousel-indicators{position:absolute;right:0;bottom:0;left:0;z-index:15;display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;padding-left:0;margin-right:15%;margin-left:15%;list-style:none}.carousel-indicators li{box-sizing:content-box;-ms-flex:0 1 auto;flex:0 1 auto;width:30px;height:3px;margin-right:3px;margin-left:3px;text-indent:-999px;cursor:pointer;background-color:#fff;background-clip:padding-box;border-top:10px solid transparent;border-bottom:10px solid transparent;opacity:.5;transition:opacity .6s ease}@media (prefers-reduced-motion:reduce){.carousel-indicators li{transition:none}}.carousel-indicators .active{opacity:1}.carousel-caption{position:absolute;right:15%;bottom:20px;left:15%;z-index:10;padding-top:20px;padding-bottom:20px;color:#fff;text-align:center}@-webkit-keyframes spinner-border{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-border{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.spinner-border{display:inline-block;width:2rem;height:2rem;vertical-align:text-bottom;border:.25em solid currentColor;border-right-color:transparent;border-radius:50%;-webkit-animation:spinner-border .75s linear infinite;animation:spinner-border .75s linear infinite}.spinner-border-sm{width:1rem;height:1rem;border-width:.2em}@-webkit-keyframes spinner-grow{0%{-webkit-transform:scale(0);transform:scale(0)}50%{opacity:1}}@keyframes spinner-grow{0%{-webkit-transform:scale(0);transform:scale(0)}50%{opacity:1}}.spinner-grow{display:inline-block;width:2rem;height:2rem;vertical-align:text-bottom;background-color:currentColor;border-radius:50%;opacity:0;-webkit-animation:spinner-grow .75s linear infinite;animation:spinner-grow .75s linear infinite}.spinner-grow-sm{width:1rem;height:1rem}.align-baseline{vertical-align:baseline!important}.align-top{vertical-align:top!important}.align-middle{vertical-align:middle!important}.align-bottom{vertical-align:bottom!important}.align-text-bottom{vertical-align:text-bottom!important}.align-text-top{vertical-align:text-top!important}.bg-primary{background-color:#007bff!important}a.bg-primary:focus,a.bg-primary:hover,button.bg-primary:focus,button.bg-primary:hover{background-color:#0062cc!important}.bg-secondary{background-color:#6c757d!important}a.bg-secondary:focus,a.bg-secondary:hover,button.bg-secondary:focus,button.bg-secondary:hover{background-color:#545b62!important}.bg-success{background-color:#28a745!important}a.bg-success:focus,a.bg-success:hover,button.bg-success:focus,button.bg-success:hover{background-color:#1e7e34!important}.bg-info{background-color:#17a2b8!important}a.bg-info:focus,a.bg-info:hover,button.bg-info:focus,button.bg-info:hover{background-color:#117a8b!important}.bg-warning{background-color:#ffc107!important}a.bg-warning:focus,a.bg-warning:hover,button.bg-warning:focus,button.bg-warning:hover{background-color:#d39e00!important}.bg-danger{background-color:#dc3545!important}a.bg-danger:focus,a.bg-danger:hover,button.bg-danger:focus,button.bg-danger:hover{background-color:#bd2130!important}.bg-light{background-color:#f8f9fa!important}a.bg-light:focus,a.bg-light:hover,button.bg-light:focus,button.bg-light:hover{background-color:#dae0e5!important}.bg-dark{background-color:#343a40!important}a.bg-dark:focus,a.bg-dark:hover,button.bg-dark:focus,button.bg-dark:hover{background-color:#1d2124!important}.bg-white{background-color:#fff!important}.bg-transparent{background-color:transparent!important}.border{border:1px solid #dee2e6!important}.border-top{border-top:1px solid #dee2e6!important}.border-right{border-right:1px solid #dee2e6!important}.border-bottom{border-bottom:1px solid #dee2e6!important}.border-left{border-left:1px solid #dee2e6!important}.border-0{border:0!important}.border-top-0{border-top:0!important}.border-right-0{border-right:0!important}.border-bottom-0{border-bottom:0!important}.border-left-0{border-left:0!important}.border-primary{border-color:#007bff!important}.border-secondary{border-color:#6c757d!important}.border-success{border-color:#28a745!important}.border-info{border-color:#17a2b8!important}.border-warning{border-color:#ffc107!important}.border-danger{border-color:#dc3545!important}.border-light{border-color:#f8f9fa!important}.border-dark{border-color:#343a40!important}.border-white{border-color:#fff!important}.rounded-sm{border-radius:.2rem!important}.rounded{border-radius:.25rem!important}.rounded-top{border-top-left-radius:.25rem!important;border-top-right-radius:.25rem!important}.rounded-right{border-top-right-radius:.25rem!important;border-bottom-right-radius:.25rem!important}.rounded-bottom{border-bottom-right-radius:.25rem!important;border-bottom-left-radius:.25rem!important}.rounded-left{border-top-left-radius:.25rem!important;border-bottom-left-radius:.25rem!important}.rounded-lg{border-radius:.3rem!important}.rounded-circle{border-radius:50%!important}.rounded-pill{border-radius:50rem!important}.rounded-0{border-radius:0!important}.clearfix::after{display:block;clear:both;content:""}.d-none{display:none!important}.d-inline{display:inline!important}.d-inline-block{display:inline-block!important}.d-block{display:block!important}.d-table{display:table!important}.d-table-row{display:table-row!important}.d-table-cell{display:table-cell!important}.d-flex{display:-ms-flexbox!important;display:flex!important}.d-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}@media (min-width:576px){.d-sm-none{display:none!important}.d-sm-inline{display:inline!important}.d-sm-inline-block{display:inline-block!important}.d-sm-block{display:block!important}.d-sm-table{display:table!important}.d-sm-table-row{display:table-row!important}.d-sm-table-cell{display:table-cell!important}.d-sm-flex{display:-ms-flexbox!important;display:flex!important}.d-sm-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}}@media (min-width:768px){.d-md-none{display:none!important}.d-md-inline{display:inline!important}.d-md-inline-block{display:inline-block!important}.d-md-block{display:block!important}.d-md-table{display:table!important}.d-md-table-row{display:table-row!important}.d-md-table-cell{display:table-cell!important}.d-md-flex{display:-ms-flexbox!important;display:flex!important}.d-md-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}}@media (min-width:992px){.d-lg-none{display:none!important}.d-lg-inline{display:inline!important}.d-lg-inline-block{display:inline-block!important}.d-lg-block{display:block!important}.d-lg-table{display:table!important}.d-lg-table-row{display:table-row!important}.d-lg-table-cell{display:table-cell!important}.d-lg-flex{display:-ms-flexbox!important;display:flex!important}.d-lg-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}}@media (min-width:1200px){.d-xl-none{display:none!important}.d-xl-inline{display:inline!important}.d-xl-inline-block{display:inline-block!important}.d-xl-block{display:block!important}.d-xl-table{display:table!important}.d-xl-table-row{display:table-row!important}.d-xl-table-cell{display:table-cell!important}.d-xl-flex{display:-ms-flexbox!important;display:flex!important}.d-xl-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}}@media print{.d-print-none{display:none!important}.d-print-inline{display:inline!important}.d-print-inline-block{display:inline-block!important}.d-print-block{display:block!important}.d-print-table{display:table!important}.d-print-table-row{display:table-row!important}.d-print-table-cell{display:table-cell!important}.d-print-flex{display:-ms-flexbox!important;display:flex!important}.d-print-inline-flex{display:-ms-inline-flexbox!important;display:inline-flex!important}}.embed-responsive{position:relative;display:block;width:100%;padding:0;overflow:hidden}.embed-responsive::before{display:block;content:""}.embed-responsive .embed-responsive-item,.embed-responsive embed,.embed-responsive iframe,.embed-responsive object,.embed-responsive video{position:absolute;top:0;bottom:0;left:0;width:100%;height:100%;border:0}.embed-responsive-21by9::before{padding-top:42.857143%}.embed-responsive-16by9::before{padding-top:56.25%}.embed-responsive-4by3::before{padding-top:75%}.embed-responsive-1by1::before{padding-top:100%}.flex-row{-ms-flex-direction:row!important;flex-direction:row!important}.flex-column{-ms-flex-direction:column!important;flex-direction:column!important}.flex-row-reverse{-ms-flex-direction:row-reverse!important;flex-direction:row-reverse!important}.flex-column-reverse{-ms-flex-direction:column-reverse!important;flex-direction:column-reverse!important}.flex-wrap{-ms-flex-wrap:wrap!important;flex-wrap:wrap!important}.flex-nowrap{-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important}.flex-wrap-reverse{-ms-flex-wrap:wrap-reverse!important;flex-wrap:wrap-reverse!important}.flex-fill{-ms-flex:1 1 auto!important;flex:1 1 auto!important}.flex-grow-0{-ms-flex-positive:0!important;flex-grow:0!important}.flex-grow-1{-ms-flex-positive:1!important;flex-grow:1!important}.flex-shrink-0{-ms-flex-negative:0!important;flex-shrink:0!important}.flex-shrink-1{-ms-flex-negative:1!important;flex-shrink:1!important}.justify-content-start{-ms-flex-pack:start!important;justify-content:flex-start!important}.justify-content-end{-ms-flex-pack:end!important;justify-content:flex-end!important}.justify-content-center{-ms-flex-pack:center!important;justify-content:center!important}.justify-content-between{-ms-flex-pack:justify!important;justify-content:space-between!important}.justify-content-around{-ms-flex-pack:distribute!important;justify-content:space-around!important}.align-items-start{-ms-flex-align:start!important;align-items:flex-start!important}.align-items-end{-ms-flex-align:end!important;align-items:flex-end!important}.align-items-center{-ms-flex-align:center!important;align-items:center!important}.align-items-baseline{-ms-flex-align:baseline!important;align-items:baseline!important}.align-items-stretch{-ms-flex-align:stretch!important;align-items:stretch!important}.align-content-start{-ms-flex-line-pack:start!important;align-content:flex-start!important}.align-content-end{-ms-flex-line-pack:end!important;align-content:flex-end!important}.align-content-center{-ms-flex-line-pack:center!important;align-content:center!important}.align-content-between{-ms-flex-line-pack:justify!important;align-content:space-between!important}.align-content-around{-ms-flex-line-pack:distribute!important;align-content:space-around!important}.align-content-stretch{-ms-flex-line-pack:stretch!important;align-content:stretch!important}.align-self-auto{-ms-flex-item-align:auto!important;align-self:auto!important}.align-self-start{-ms-flex-item-align:start!important;align-self:flex-start!important}.align-self-end{-ms-flex-item-align:end!important;align-self:flex-end!important}.align-self-center{-ms-flex-item-align:center!important;align-self:center!important}.align-self-baseline{-ms-flex-item-align:baseline!important;align-self:baseline!important}.align-self-stretch{-ms-flex-item-align:stretch!important;align-self:stretch!important}@media (min-width:576px){.flex-sm-row{-ms-flex-direction:row!important;flex-direction:row!important}.flex-sm-column{-ms-flex-direction:column!important;flex-direction:column!important}.flex-sm-row-reverse{-ms-flex-direction:row-reverse!important;flex-direction:row-reverse!important}.flex-sm-column-reverse{-ms-flex-direction:column-reverse!important;flex-direction:column-reverse!important}.flex-sm-wrap{-ms-flex-wrap:wrap!important;flex-wrap:wrap!important}.flex-sm-nowrap{-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important}.flex-sm-wrap-reverse{-ms-flex-wrap:wrap-reverse!important;flex-wrap:wrap-reverse!important}.flex-sm-fill{-ms-flex:1 1 auto!important;flex:1 1 auto!important}.flex-sm-grow-0{-ms-flex-positive:0!important;flex-grow:0!important}.flex-sm-grow-1{-ms-flex-positive:1!important;flex-grow:1!important}.flex-sm-shrink-0{-ms-flex-negative:0!important;flex-shrink:0!important}.flex-sm-shrink-1{-ms-flex-negative:1!important;flex-shrink:1!important}.justify-content-sm-start{-ms-flex-pack:start!important;justify-content:flex-start!important}.justify-content-sm-end{-ms-flex-pack:end!important;justify-content:flex-end!important}.justify-content-sm-center{-ms-flex-pack:center!important;justify-content:center!important}.justify-content-sm-between{-ms-flex-pack:justify!important;justify-content:space-between!important}.justify-content-sm-around{-ms-flex-pack:distribute!important;justify-content:space-around!important}.align-items-sm-start{-ms-flex-align:start!important;align-items:flex-start!important}.align-items-sm-end{-ms-flex-align:end!important;align-items:flex-end!important}.align-items-sm-center{-ms-flex-align:center!important;align-items:center!important}.align-items-sm-baseline{-ms-flex-align:baseline!important;align-items:baseline!important}.align-items-sm-stretch{-ms-flex-align:stretch!important;align-items:stretch!important}.align-content-sm-start{-ms-flex-line-pack:start!important;align-content:flex-start!important}.align-content-sm-end{-ms-flex-line-pack:end!important;align-content:flex-end!important}.align-content-sm-center{-ms-flex-line-pack:center!important;align-content:center!important}.align-content-sm-between{-ms-flex-line-pack:justify!important;align-content:space-between!important}.align-content-sm-around{-ms-flex-line-pack:distribute!important;align-content:space-around!important}.align-content-sm-stretch{-ms-flex-line-pack:stretch!important;align-content:stretch!important}.align-self-sm-auto{-ms-flex-item-align:auto!important;align-self:auto!important}.align-self-sm-start{-ms-flex-item-align:start!important;align-self:flex-start!important}.align-self-sm-end{-ms-flex-item-align:end!important;align-self:flex-end!important}.align-self-sm-center{-ms-flex-item-align:center!important;align-self:center!important}.align-self-sm-baseline{-ms-flex-item-align:baseline!important;align-self:baseline!important}.align-self-sm-stretch{-ms-flex-item-align:stretch!important;align-self:stretch!important}}@media (min-width:768px){.flex-md-row{-ms-flex-direction:row!important;flex-direction:row!important}.flex-md-column{-ms-flex-direction:column!important;flex-direction:column!important}.flex-md-row-reverse{-ms-flex-direction:row-reverse!important;flex-direction:row-reverse!important}.flex-md-column-reverse{-ms-flex-direction:column-reverse!important;flex-direction:column-reverse!important}.flex-md-wrap{-ms-flex-wrap:wrap!important;flex-wrap:wrap!important}.flex-md-nowrap{-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important}.flex-md-wrap-reverse{-ms-flex-wrap:wrap-reverse!important;flex-wrap:wrap-reverse!important}.flex-md-fill{-ms-flex:1 1 auto!important;flex:1 1 auto!important}.flex-md-grow-0{-ms-flex-positive:0!important;flex-grow:0!important}.flex-md-grow-1{-ms-flex-positive:1!important;flex-grow:1!important}.flex-md-shrink-0{-ms-flex-negative:0!important;flex-shrink:0!important}.flex-md-shrink-1{-ms-flex-negative:1!important;flex-shrink:1!important}.justify-content-md-start{-ms-flex-pack:start!important;justify-content:flex-start!important}.justify-content-md-end{-ms-flex-pack:end!important;justify-content:flex-end!important}.justify-content-md-center{-ms-flex-pack:center!important;justify-content:center!important}.justify-content-md-between{-ms-flex-pack:justify!important;justify-content:space-between!important}.justify-content-md-around{-ms-flex-pack:distribute!important;justify-content:space-around!important}.align-items-md-start{-ms-flex-align:start!important;align-items:flex-start!important}.align-items-md-end{-ms-flex-align:end!important;align-items:flex-end!important}.align-items-md-center{-ms-flex-align:center!important;align-items:center!important}.align-items-md-baseline{-ms-flex-align:baseline!important;align-items:baseline!important}.align-items-md-stretch{-ms-flex-align:stretch!important;align-items:stretch!important}.align-content-md-start{-ms-flex-line-pack:start!important;align-content:flex-start!important}.align-content-md-end{-ms-flex-line-pack:end!important;align-content:flex-end!important}.align-content-md-center{-ms-flex-line-pack:center!important;align-content:center!important}.align-content-md-between{-ms-flex-line-pack:justify!important;align-content:space-between!important}.align-content-md-around{-ms-flex-line-pack:distribute!important;align-content:space-around!important}.align-content-md-stretch{-ms-flex-line-pack:stretch!important;align-content:stretch!important}.align-self-md-auto{-ms-flex-item-align:auto!important;align-self:auto!important}.align-self-md-start{-ms-flex-item-align:start!important;align-self:flex-start!important}.align-self-md-end{-ms-flex-item-align:end!important;align-self:flex-end!important}.align-self-md-center{-ms-flex-item-align:center!important;align-self:center!important}.align-self-md-baseline{-ms-flex-item-align:baseline!important;align-self:baseline!important}.align-self-md-stretch{-ms-flex-item-align:stretch!important;align-self:stretch!important}}@media (min-width:992px){.flex-lg-row{-ms-flex-direction:row!important;flex-direction:row!important}.flex-lg-column{-ms-flex-direction:column!important;flex-direction:column!important}.flex-lg-row-reverse{-ms-flex-direction:row-reverse!important;flex-direction:row-reverse!important}.flex-lg-column-reverse{-ms-flex-direction:column-reverse!important;flex-direction:column-reverse!important}.flex-lg-wrap{-ms-flex-wrap:wrap!important;flex-wrap:wrap!important}.flex-lg-nowrap{-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important}.flex-lg-wrap-reverse{-ms-flex-wrap:wrap-reverse!important;flex-wrap:wrap-reverse!important}.flex-lg-fill{-ms-flex:1 1 auto!important;flex:1 1 auto!important}.flex-lg-grow-0{-ms-flex-positive:0!important;flex-grow:0!important}.flex-lg-grow-1{-ms-flex-positive:1!important;flex-grow:1!important}.flex-lg-shrink-0{-ms-flex-negative:0!important;flex-shrink:0!important}.flex-lg-shrink-1{-ms-flex-negative:1!important;flex-shrink:1!important}.justify-content-lg-start{-ms-flex-pack:start!important;justify-content:flex-start!important}.justify-content-lg-end{-ms-flex-pack:end!important;justify-content:flex-end!important}.justify-content-lg-center{-ms-flex-pack:center!important;justify-content:center!important}.justify-content-lg-between{-ms-flex-pack:justify!important;justify-content:space-between!important}.justify-content-lg-around{-ms-flex-pack:distribute!important;justify-content:space-around!important}.align-items-lg-start{-ms-flex-align:start!important;align-items:flex-start!important}.align-items-lg-end{-ms-flex-align:end!important;align-items:flex-end!important}.align-items-lg-center{-ms-flex-align:center!important;align-items:center!important}.align-items-lg-baseline{-ms-flex-align:baseline!important;align-items:baseline!important}.align-items-lg-stretch{-ms-flex-align:stretch!important;align-items:stretch!important}.align-content-lg-start{-ms-flex-line-pack:start!important;align-content:flex-start!important}.align-content-lg-end{-ms-flex-line-pack:end!important;align-content:flex-end!important}.align-content-lg-center{-ms-flex-line-pack:center!important;align-content:center!important}.align-content-lg-between{-ms-flex-line-pack:justify!important;align-content:space-between!important}.align-content-lg-around{-ms-flex-line-pack:distribute!important;align-content:space-around!important}.align-content-lg-stretch{-ms-flex-line-pack:stretch!important;align-content:stretch!important}.align-self-lg-auto{-ms-flex-item-align:auto!important;align-self:auto!important}.align-self-lg-start{-ms-flex-item-align:start!important;align-self:flex-start!important}.align-self-lg-end{-ms-flex-item-align:end!important;align-self:flex-end!important}.align-self-lg-center{-ms-flex-item-align:center!important;align-self:center!important}.align-self-lg-baseline{-ms-flex-item-align:baseline!important;align-self:baseline!important}.align-self-lg-stretch{-ms-flex-item-align:stretch!important;align-self:stretch!important}}@media (min-width:1200px){.flex-xl-row{-ms-flex-direction:row!important;flex-direction:row!important}.flex-xl-column{-ms-flex-direction:column!important;flex-direction:column!important}.flex-xl-row-reverse{-ms-flex-direction:row-reverse!important;flex-direction:row-reverse!important}.flex-xl-column-reverse{-ms-flex-direction:column-reverse!important;flex-direction:column-reverse!important}.flex-xl-wrap{-ms-flex-wrap:wrap!important;flex-wrap:wrap!important}.flex-xl-nowrap{-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important}.flex-xl-wrap-reverse{-ms-flex-wrap:wrap-reverse!important;flex-wrap:wrap-reverse!important}.flex-xl-fill{-ms-flex:1 1 auto!important;flex:1 1 auto!important}.flex-xl-grow-0{-ms-flex-positive:0!important;flex-grow:0!important}.flex-xl-grow-1{-ms-flex-positive:1!important;flex-grow:1!important}.flex-xl-shrink-0{-ms-flex-negative:0!important;flex-shrink:0!important}.flex-xl-shrink-1{-ms-flex-negative:1!important;flex-shrink:1!important}.justify-content-xl-start{-ms-flex-pack:start!important;justify-content:flex-start!important}.justify-content-xl-end{-ms-flex-pack:end!important;justify-content:flex-end!important}.justify-content-xl-center{-ms-flex-pack:center!important;justify-content:center!important}.justify-content-xl-between{-ms-flex-pack:justify!important;justify-content:space-between!important}.justify-content-xl-around{-ms-flex-pack:distribute!important;justify-content:space-around!important}.align-items-xl-start{-ms-flex-align:start!important;align-items:flex-start!important}.align-items-xl-end{-ms-flex-align:end!important;align-items:flex-end!important}.align-items-xl-center{-ms-flex-align:center!important;align-items:center!important}.align-items-xl-baseline{-ms-flex-align:baseline!important;align-items:baseline!important}.align-items-xl-stretch{-ms-flex-align:stretch!important;align-items:stretch!important}.align-content-xl-start{-ms-flex-line-pack:start!important;align-content:flex-start!important}.align-content-xl-end{-ms-flex-line-pack:end!important;align-content:flex-end!important}.align-content-xl-center{-ms-flex-line-pack:center!important;align-content:center!important}.align-content-xl-between{-ms-flex-line-pack:justify!important;align-content:space-between!important}.align-content-xl-around{-ms-flex-line-pack:distribute!important;align-content:space-around!important}.align-content-xl-stretch{-ms-flex-line-pack:stretch!important;align-content:stretch!important}.align-self-xl-auto{-ms-flex-item-align:auto!important;align-self:auto!important}.align-self-xl-start{-ms-flex-item-align:start!important;align-self:flex-start!important}.align-self-xl-end{-ms-flex-item-align:end!important;align-self:flex-end!important}.align-self-xl-center{-ms-flex-item-align:center!important;align-self:center!important}.align-self-xl-baseline{-ms-flex-item-align:baseline!important;align-self:baseline!important}.align-self-xl-stretch{-ms-flex-item-align:stretch!important;align-self:stretch!important}}.float-left{float:left!important}.float-right{float:right!important}.float-none{float:none!important}@media (min-width:576px){.float-sm-left{float:left!important}.float-sm-right{float:right!important}.float-sm-none{float:none!important}}@media (min-width:768px){.float-md-left{float:left!important}.float-md-right{float:right!important}.float-md-none{float:none!important}}@media (min-width:992px){.float-lg-left{float:left!important}.float-lg-right{float:right!important}.float-lg-none{float:none!important}}@media (min-width:1200px){.float-xl-left{float:left!important}.float-xl-right{float:right!important}.float-xl-none{float:none!important}}.overflow-auto{overflow:auto!important}.overflow-hidden{overflow:hidden!important}.position-static{position:static!important}.position-relative{position:relative!important}.position-absolute{position:absolute!important}.position-fixed{position:fixed!important}.position-sticky{position:-webkit-sticky!important;position:sticky!important}.navbar-sticky{position:fixed;top:0;right:0;left:0;z-index:1030}.fixed-bottom{position:fixed;right:0;bottom:0;left:0;z-index:1030}@supports ((position:-webkit-sticky) or (position:sticky)){.sticky-top{position:-webkit-sticky;position:sticky;top:0;z-index:1020}}.sr-only{position:absolute;width:1px;height:1px;padding:0;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;overflow:visible;clip:auto;white-space:normal}.shadow-sm{box-shadow:0 .125rem .25rem rgba(0,0,0,.075)!important}.shadow{box-shadow:0 .5rem 1rem rgba(0,0,0,.15)!important}.shadow-lg{box-shadow:0 1rem 3rem rgba(0,0,0,.175)!important}.shadow-none{box-shadow:none!important}.w-25{width:25%!important}.w-50{width:50%!important}.w-75{width:75%!important}.w-100{width:100%!important}.w-auto{width:auto!important}.h-25{height:25%!important}.h-50{height:50%!important}.h-75{height:75%!important}.h-100{height:100%!important}.h-auto{height:auto!important}.mw-100{max-width:100%!important}.mh-100{max-height:100%!important}.min-vw-100{min-width:100vw!important}.min-vh-100{min-height:100vh!important}.vw-100{width:100vw!important}.vh-100{height:100vh!important}.stretched-link::after{position:absolute;top:0;right:0;bottom:0;left:0;z-index:1;pointer-events:auto;content:"";background-color:rgba(0,0,0,0)}.m-0{margin:0!important}.mt-0,.my-0{margin-top:0!important}.mr-0,.mx-0{margin-right:0!important}.mb-0,.my-0{margin-bottom:0!important}.ml-0,.mx-0{margin-left:0!important}.m-1{margin:.25rem!important}.mt-1,.my-1{margin-top:.25rem!important}.mr-1,.mx-1{margin-right:.25rem!important}.mb-1,.my-1{margin-bottom:.25rem!important}.ml-1,.mx-1{margin-left:.25rem!important}.m-2{margin:.5rem!important}.mt-2,.my-2{margin-top:.5rem!important}.mr-2,.mx-2{margin-right:.5rem!important}.mb-2,.my-2{margin-bottom:.5rem!important}.ml-2,.mx-2{margin-left:.5rem!important}.m-3{margin:1rem!important}.mt-3,.my-3{margin-top:1rem!important}.mr-3,.mx-3{margin-right:1rem!important}.mb-3,.my-3{margin-bottom:1rem!important}.ml-3,.mx-3{margin-left:1rem!important}.m-4{margin:1.5rem!important}.mt-4,.my-4{margin-top:1.5rem!important}.mr-4,.mx-4{margin-right:1.5rem!important}.mb-4,.my-4{margin-bottom:1.5rem!important}.ml-4,.mx-4{margin-left:1.5rem!important}.m-5{margin:3rem!important}.mt-5,.my-5{margin-top:3rem!important}.mr-5,.mx-5{margin-right:3rem!important}.mb-5,.my-5{margin-bottom:3rem!important}.ml-5,.mx-5{margin-left:3rem!important}.p-0{padding:0!important}.pt-0,.py-0{padding-top:0!important}.pr-0,.px-0{padding-right:0!important}.pb-0,.py-0{padding-bottom:0!important}.pl-0,.px-0{padding-left:0!important}.p-1{padding:.25rem!important}.pt-1,.py-1{padding-top:.25rem!important}.pr-1,.px-1{padding-right:.25rem!important}.pb-1,.py-1{padding-bottom:.25rem!important}.pl-1,.px-1{padding-left:.25rem!important}.p-2{padding:.5rem!important}.pt-2,.py-2{padding-top:.5rem!important}.pr-2,.px-2{padding-right:.5rem!important}.pb-2,.py-2{padding-bottom:.5rem!important}.pl-2,.px-2{padding-left:.5rem!important}.p-3{padding:1rem!important}.pt-3,.py-3{padding-top:1rem!important}.pr-3,.px-3{padding-right:1rem!important}.pb-3,.py-3{padding-bottom:1rem!important}.pl-3,.px-3{padding-left:1rem!important}.p-4{padding:1.5rem!important}.pt-4,.py-4{padding-top:1.5rem!important}.pr-4,.px-4{padding-right:1.5rem!important}.pb-4,.py-4{padding-bottom:1.5rem!important}.pl-4,.px-4{padding-left:1.5rem!important}.p-5{padding:3rem!important}.pt-5,.py-5{padding-top:3rem!important}.pr-5,.px-5{padding-right:3rem!important}.pb-5,.py-5{padding-bottom:3rem!important}.pl-5,.px-5{padding-left:3rem!important}.m-n1{margin:-.25rem!important}.mt-n1,.my-n1{margin-top:-.25rem!important}.mr-n1,.mx-n1{margin-right:-.25rem!important}.mb-n1,.my-n1{margin-bottom:-.25rem!important}.ml-n1,.mx-n1{margin-left:-.25rem!important}.m-n2{margin:-.5rem!important}.mt-n2,.my-n2{margin-top:-.5rem!important}.mr-n2,.mx-n2{margin-right:-.5rem!important}.mb-n2,.my-n2{margin-bottom:-.5rem!important}.ml-n2,.mx-n2{margin-left:-.5rem!important}.m-n3{margin:-1rem!important}.mt-n3,.my-n3{margin-top:-1rem!important}.mr-n3,.mx-n3{margin-right:-1rem!important}.mb-n3,.my-n3{margin-bottom:-1rem!important}.ml-n3,.mx-n3{margin-left:-1rem!important}.m-n4{margin:-1.5rem!important}.mt-n4,.my-n4{margin-top:-1.5rem!important}.mr-n4,.mx-n4{margin-right:-1.5rem!important}.mb-n4,.my-n4{margin-bottom:-1.5rem!important}.ml-n4,.mx-n4{margin-left:-1.5rem!important}.m-n5{margin:-3rem!important}.mt-n5,.my-n5{margin-top:-3rem!important}.mr-n5,.mx-n5{margin-right:-3rem!important}.mb-n5,.my-n5{margin-bottom:-3rem!important}.ml-n5,.mx-n5{margin-left:-3rem!important}.m-auto{margin:auto!important}.mt-auto,.my-auto{margin-top:auto!important}.mr-auto,.mx-auto{margin-right:auto!important}.mb-auto,.my-auto{margin-bottom:auto!important}.ml-auto,.mx-auto{margin-left:auto!important}@media (min-width:576px){.m-sm-0{margin:0!important}.mt-sm-0,.my-sm-0{margin-top:0!important}.mr-sm-0,.mx-sm-0{margin-right:0!important}.mb-sm-0,.my-sm-0{margin-bottom:0!important}.ml-sm-0,.mx-sm-0{margin-left:0!important}.m-sm-1{margin:.25rem!important}.mt-sm-1,.my-sm-1{margin-top:.25rem!important}.mr-sm-1,.mx-sm-1{margin-right:.25rem!important}.mb-sm-1,.my-sm-1{margin-bottom:.25rem!important}.ml-sm-1,.mx-sm-1{margin-left:.25rem!important}.m-sm-2{margin:.5rem!important}.mt-sm-2,.my-sm-2{margin-top:.5rem!important}.mr-sm-2,.mx-sm-2{margin-right:.5rem!important}.mb-sm-2,.my-sm-2{margin-bottom:.5rem!important}.ml-sm-2,.mx-sm-2{margin-left:.5rem!important}.m-sm-3{margin:1rem!important}.mt-sm-3,.my-sm-3{margin-top:1rem!important}.mr-sm-3,.mx-sm-3{margin-right:1rem!important}.mb-sm-3,.my-sm-3{margin-bottom:1rem!important}.ml-sm-3,.mx-sm-3{margin-left:1rem!important}.m-sm-4{margin:1.5rem!important}.mt-sm-4,.my-sm-4{margin-top:1.5rem!important}.mr-sm-4,.mx-sm-4{margin-right:1.5rem!important}.mb-sm-4,.my-sm-4{margin-bottom:1.5rem!important}.ml-sm-4,.mx-sm-4{margin-left:1.5rem!important}.m-sm-5{margin:3rem!important}.mt-sm-5,.my-sm-5{margin-top:3rem!important}.mr-sm-5,.mx-sm-5{margin-right:3rem!important}.mb-sm-5,.my-sm-5{margin-bottom:3rem!important}.ml-sm-5,.mx-sm-5{margin-left:3rem!important}.p-sm-0{padding:0!important}.pt-sm-0,.py-sm-0{padding-top:0!important}.pr-sm-0,.px-sm-0{padding-right:0!important}.pb-sm-0,.py-sm-0{padding-bottom:0!important}.pl-sm-0,.px-sm-0{padding-left:0!important}.p-sm-1{padding:.25rem!important}.pt-sm-1,.py-sm-1{padding-top:.25rem!important}.pr-sm-1,.px-sm-1{padding-right:.25rem!important}.pb-sm-1,.py-sm-1{padding-bottom:.25rem!important}.pl-sm-1,.px-sm-1{padding-left:.25rem!important}.p-sm-2{padding:.5rem!important}.pt-sm-2,.py-sm-2{padding-top:.5rem!important}.pr-sm-2,.px-sm-2{padding-right:.5rem!important}.pb-sm-2,.py-sm-2{padding-bottom:.5rem!important}.pl-sm-2,.px-sm-2{padding-left:.5rem!important}.p-sm-3{padding:1rem!important}.pt-sm-3,.py-sm-3{padding-top:1rem!important}.pr-sm-3,.px-sm-3{padding-right:1rem!important}.pb-sm-3,.py-sm-3{padding-bottom:1rem!important}.pl-sm-3,.px-sm-3{padding-left:1rem!important}.p-sm-4{padding:1.5rem!important}.pt-sm-4,.py-sm-4{padding-top:1.5rem!important}.pr-sm-4,.px-sm-4{padding-right:1.5rem!important}.pb-sm-4,.py-sm-4{padding-bottom:1.5rem!important}.pl-sm-4,.px-sm-4{padding-left:1.5rem!important}.p-sm-5{padding:3rem!important}.pt-sm-5,.py-sm-5{padding-top:3rem!important}.pr-sm-5,.px-sm-5{padding-right:3rem!important}.pb-sm-5,.py-sm-5{padding-bottom:3rem!important}.pl-sm-5,.px-sm-5{padding-left:3rem!important}.m-sm-n1{margin:-.25rem!important}.mt-sm-n1,.my-sm-n1{margin-top:-.25rem!important}.mr-sm-n1,.mx-sm-n1{margin-right:-.25rem!important}.mb-sm-n1,.my-sm-n1{margin-bottom:-.25rem!important}.ml-sm-n1,.mx-sm-n1{margin-left:-.25rem!important}.m-sm-n2{margin:-.5rem!important}.mt-sm-n2,.my-sm-n2{margin-top:-.5rem!important}.mr-sm-n2,.mx-sm-n2{margin-right:-.5rem!important}.mb-sm-n2,.my-sm-n2{margin-bottom:-.5rem!important}.ml-sm-n2,.mx-sm-n2{margin-left:-.5rem!important}.m-sm-n3{margin:-1rem!important}.mt-sm-n3,.my-sm-n3{margin-top:-1rem!important}.mr-sm-n3,.mx-sm-n3{margin-right:-1rem!important}.mb-sm-n3,.my-sm-n3{margin-bottom:-1rem!important}.ml-sm-n3,.mx-sm-n3{margin-left:-1rem!important}.m-sm-n4{margin:-1.5rem!important}.mt-sm-n4,.my-sm-n4{margin-top:-1.5rem!important}.mr-sm-n4,.mx-sm-n4{margin-right:-1.5rem!important}.mb-sm-n4,.my-sm-n4{margin-bottom:-1.5rem!important}.ml-sm-n4,.mx-sm-n4{margin-left:-1.5rem!important}.m-sm-n5{margin:-3rem!important}.mt-sm-n5,.my-sm-n5{margin-top:-3rem!important}.mr-sm-n5,.mx-sm-n5{margin-right:-3rem!important}.mb-sm-n5,.my-sm-n5{margin-bottom:-3rem!important}.ml-sm-n5,.mx-sm-n5{margin-left:-3rem!important}.m-sm-auto{margin:auto!important}.mt-sm-auto,.my-sm-auto{margin-top:auto!important}.mr-sm-auto,.mx-sm-auto{margin-right:auto!important}.mb-sm-auto,.my-sm-auto{margin-bottom:auto!important}.ml-sm-auto,.mx-sm-auto{margin-left:auto!important}}@media (min-width:768px){.m-md-0{margin:0!important}.mt-md-0,.my-md-0{margin-top:0!important}.mr-md-0,.mx-md-0{margin-right:0!important}.mb-md-0,.my-md-0{margin-bottom:0!important}.ml-md-0,.mx-md-0{margin-left:0!important}.m-md-1{margin:.25rem!important}.mt-md-1,.my-md-1{margin-top:.25rem!important}.mr-md-1,.mx-md-1{margin-right:.25rem!important}.mb-md-1,.my-md-1{margin-bottom:.25rem!important}.ml-md-1,.mx-md-1{margin-left:.25rem!important}.m-md-2{margin:.5rem!important}.mt-md-2,.my-md-2{margin-top:.5rem!important}.mr-md-2,.mx-md-2{margin-right:.5rem!important}.mb-md-2,.my-md-2{margin-bottom:.5rem!important}.ml-md-2,.mx-md-2{margin-left:.5rem!important}.m-md-3{margin:1rem!important}.mt-md-3,.my-md-3{margin-top:1rem!important}.mr-md-3,.mx-md-3{margin-right:1rem!important}.mb-md-3,.my-md-3{margin-bottom:1rem!important}.ml-md-3,.mx-md-3{margin-left:1rem!important}.m-md-4{margin:1.5rem!important}.mt-md-4,.my-md-4{margin-top:1.5rem!important}.mr-md-4,.mx-md-4{margin-right:1.5rem!important}.mb-md-4,.my-md-4{margin-bottom:1.5rem!important}.ml-md-4,.mx-md-4{margin-left:1.5rem!important}.m-md-5{margin:3rem!important}.mt-md-5,.my-md-5{margin-top:3rem!important}.mr-md-5,.mx-md-5{margin-right:3rem!important}.mb-md-5,.my-md-5{margin-bottom:3rem!important}.ml-md-5,.mx-md-5{margin-left:3rem!important}.p-md-0{padding:0!important}.pt-md-0,.py-md-0{padding-top:0!important}.pr-md-0,.px-md-0{padding-right:0!important}.pb-md-0,.py-md-0{padding-bottom:0!important}.pl-md-0,.px-md-0{padding-left:0!important}.p-md-1{padding:.25rem!important}.pt-md-1,.py-md-1{padding-top:.25rem!important}.pr-md-1,.px-md-1{padding-right:.25rem!important}.pb-md-1,.py-md-1{padding-bottom:.25rem!important}.pl-md-1,.px-md-1{padding-left:.25rem!important}.p-md-2{padding:.5rem!important}.pt-md-2,.py-md-2{padding-top:.5rem!important}.pr-md-2,.px-md-2{padding-right:.5rem!important}.pb-md-2,.py-md-2{padding-bottom:.5rem!important}.pl-md-2,.px-md-2{padding-left:.5rem!important}.p-md-3{padding:1rem!important}.pt-md-3,.py-md-3{padding-top:1rem!important}.pr-md-3,.px-md-3{padding-right:1rem!important}.pb-md-3,.py-md-3{padding-bottom:1rem!important}.pl-md-3,.px-md-3{padding-left:1rem!important}.p-md-4{padding:1.5rem!important}.pt-md-4,.py-md-4{padding-top:1.5rem!important}.pr-md-4,.px-md-4{padding-right:1.5rem!important}.pb-md-4,.py-md-4{padding-bottom:1.5rem!important}.pl-md-4,.px-md-4{padding-left:1.5rem!important}.p-md-5{padding:3rem!important}.pt-md-5,.py-md-5{padding-top:3rem!important}.pr-md-5,.px-md-5{padding-right:3rem!important}.pb-md-5,.py-md-5{padding-bottom:3rem!important}.pl-md-5,.px-md-5{padding-left:3rem!important}.m-md-n1{margin:-.25rem!important}.mt-md-n1,.my-md-n1{margin-top:-.25rem!important}.mr-md-n1,.mx-md-n1{margin-right:-.25rem!important}.mb-md-n1,.my-md-n1{margin-bottom:-.25rem!important}.ml-md-n1,.mx-md-n1{margin-left:-.25rem!important}.m-md-n2{margin:-.5rem!important}.mt-md-n2,.my-md-n2{margin-top:-.5rem!important}.mr-md-n2,.mx-md-n2{margin-right:-.5rem!important}.mb-md-n2,.my-md-n2{margin-bottom:-.5rem!important}.ml-md-n2,.mx-md-n2{margin-left:-.5rem!important}.m-md-n3{margin:-1rem!important}.mt-md-n3,.my-md-n3{margin-top:-1rem!important}.mr-md-n3,.mx-md-n3{margin-right:-1rem!important}.mb-md-n3,.my-md-n3{margin-bottom:-1rem!important}.ml-md-n3,.mx-md-n3{margin-left:-1rem!important}.m-md-n4{margin:-1.5rem!important}.mt-md-n4,.my-md-n4{margin-top:-1.5rem!important}.mr-md-n4,.mx-md-n4{margin-right:-1.5rem!important}.mb-md-n4,.my-md-n4{margin-bottom:-1.5rem!important}.ml-md-n4,.mx-md-n4{margin-left:-1.5rem!important}.m-md-n5{margin:-3rem!important}.mt-md-n5,.my-md-n5{margin-top:-3rem!important}.mr-md-n5,.mx-md-n5{margin-right:-3rem!important}.mb-md-n5,.my-md-n5{margin-bottom:-3rem!important}.ml-md-n5,.mx-md-n5{margin-left:-3rem!important}.m-md-auto{margin:auto!important}.mt-md-auto,.my-md-auto{margin-top:auto!important}.mr-md-auto,.mx-md-auto{margin-right:auto!important}.mb-md-auto,.my-md-auto{margin-bottom:auto!important}.ml-md-auto,.mx-md-auto{margin-left:auto!important}}@media (min-width:992px){.m-lg-0{margin:0!important}.mt-lg-0,.my-lg-0{margin-top:0!important}.mr-lg-0,.mx-lg-0{margin-right:0!important}.mb-lg-0,.my-lg-0{margin-bottom:0!important}.ml-lg-0,.mx-lg-0{margin-left:0!important}.m-lg-1{margin:.25rem!important}.mt-lg-1,.my-lg-1{margin-top:.25rem!important}.mr-lg-1,.mx-lg-1{margin-right:.25rem!important}.mb-lg-1,.my-lg-1{margin-bottom:.25rem!important}.ml-lg-1,.mx-lg-1{margin-left:.25rem!important}.m-lg-2{margin:.5rem!important}.mt-lg-2,.my-lg-2{margin-top:.5rem!important}.mr-lg-2,.mx-lg-2{margin-right:.5rem!important}.mb-lg-2,.my-lg-2{margin-bottom:.5rem!important}.ml-lg-2,.mx-lg-2{margin-left:.5rem!important}.m-lg-3{margin:1rem!important}.mt-lg-3,.my-lg-3{margin-top:1rem!important}.mr-lg-3,.mx-lg-3{margin-right:1rem!important}.mb-lg-3,.my-lg-3{margin-bottom:1rem!important}.ml-lg-3,.mx-lg-3{margin-left:1rem!important}.m-lg-4{margin:1.5rem!important}.mt-lg-4,.my-lg-4{margin-top:1.5rem!important}.mr-lg-4,.mx-lg-4{margin-right:1.5rem!important}.mb-lg-4,.my-lg-4{margin-bottom:1.5rem!important}.ml-lg-4,.mx-lg-4{margin-left:1.5rem!important}.m-lg-5{margin:3rem!important}.mt-lg-5,.my-lg-5{margin-top:3rem!important}.mr-lg-5,.mx-lg-5{margin-right:3rem!important}.mb-lg-5,.my-lg-5{margin-bottom:3rem!important}.ml-lg-5,.mx-lg-5{margin-left:3rem!important}.p-lg-0{padding:0!important}.pt-lg-0,.py-lg-0{padding-top:0!important}.pr-lg-0,.px-lg-0{padding-right:0!important}.pb-lg-0,.py-lg-0{padding-bottom:0!important}.pl-lg-0,.px-lg-0{padding-left:0!important}.p-lg-1{padding:.25rem!important}.pt-lg-1,.py-lg-1{padding-top:.25rem!important}.pr-lg-1,.px-lg-1{padding-right:.25rem!important}.pb-lg-1,.py-lg-1{padding-bottom:.25rem!important}.pl-lg-1,.px-lg-1{padding-left:.25rem!important}.p-lg-2{padding:.5rem!important}.pt-lg-2,.py-lg-2{padding-top:.5rem!important}.pr-lg-2,.px-lg-2{padding-right:.5rem!important}.pb-lg-2,.py-lg-2{padding-bottom:.5rem!important}.pl-lg-2,.px-lg-2{padding-left:.5rem!important}.p-lg-3{padding:1rem!important}.pt-lg-3,.py-lg-3{padding-top:1rem!important}.pr-lg-3,.px-lg-3{padding-right:1rem!important}.pb-lg-3,.py-lg-3{padding-bottom:1rem!important}.pl-lg-3,.px-lg-3{padding-left:1rem!important}.p-lg-4{padding:1.5rem!important}.pt-lg-4,.py-lg-4{padding-top:1.5rem!important}.pr-lg-4,.px-lg-4{padding-right:1.5rem!important}.pb-lg-4,.py-lg-4{padding-bottom:1.5rem!important}.pl-lg-4,.px-lg-4{padding-left:1.5rem!important}.p-lg-5{padding:3rem!important}.pt-lg-5,.py-lg-5{padding-top:3rem!important}.pr-lg-5,.px-lg-5{padding-right:3rem!important}.pb-lg-5,.py-lg-5{padding-bottom:3rem!important}.pl-lg-5,.px-lg-5{padding-left:3rem!important}.m-lg-n1{margin:-.25rem!important}.mt-lg-n1,.my-lg-n1{margin-top:-.25rem!important}.mr-lg-n1,.mx-lg-n1{margin-right:-.25rem!important}.mb-lg-n1,.my-lg-n1{margin-bottom:-.25rem!important}.ml-lg-n1,.mx-lg-n1{margin-left:-.25rem!important}.m-lg-n2{margin:-.5rem!important}.mt-lg-n2,.my-lg-n2{margin-top:-.5rem!important}.mr-lg-n2,.mx-lg-n2{margin-right:-.5rem!important}.mb-lg-n2,.my-lg-n2{margin-bottom:-.5rem!important}.ml-lg-n2,.mx-lg-n2{margin-left:-.5rem!important}.m-lg-n3{margin:-1rem!important}.mt-lg-n3,.my-lg-n3{margin-top:-1rem!important}.mr-lg-n3,.mx-lg-n3{margin-right:-1rem!important}.mb-lg-n3,.my-lg-n3{margin-bottom:-1rem!important}.ml-lg-n3,.mx-lg-n3{margin-left:-1rem!important}.m-lg-n4{margin:-1.5rem!important}.mt-lg-n4,.my-lg-n4{margin-top:-1.5rem!important}.mr-lg-n4,.mx-lg-n4{margin-right:-1.5rem!important}.mb-lg-n4,.my-lg-n4{margin-bottom:-1.5rem!important}.ml-lg-n4,.mx-lg-n4{margin-left:-1.5rem!important}.m-lg-n5{margin:-3rem!important}.mt-lg-n5,.my-lg-n5{margin-top:-3rem!important}.mr-lg-n5,.mx-lg-n5{margin-right:-3rem!important}.mb-lg-n5,.my-lg-n5{margin-bottom:-3rem!important}.ml-lg-n5,.mx-lg-n5{margin-left:-3rem!important}.m-lg-auto{margin:auto!important}.mt-lg-auto,.my-lg-auto{margin-top:auto!important}.mr-lg-auto,.mx-lg-auto{margin-right:auto!important}.mb-lg-auto,.my-lg-auto{margin-bottom:auto!important}.ml-lg-auto,.mx-lg-auto{margin-left:auto!important}}@media (min-width:1200px){.m-xl-0{margin:0!important}.mt-xl-0,.my-xl-0{margin-top:0!important}.mr-xl-0,.mx-xl-0{margin-right:0!important}.mb-xl-0,.my-xl-0{margin-bottom:0!important}.ml-xl-0,.mx-xl-0{margin-left:0!important}.m-xl-1{margin:.25rem!important}.mt-xl-1,.my-xl-1{margin-top:.25rem!important}.mr-xl-1,.mx-xl-1{margin-right:.25rem!important}.mb-xl-1,.my-xl-1{margin-bottom:.25rem!important}.ml-xl-1,.mx-xl-1{margin-left:.25rem!important}.m-xl-2{margin:.5rem!important}.mt-xl-2,.my-xl-2{margin-top:.5rem!important}.mr-xl-2,.mx-xl-2{margin-right:.5rem!important}.mb-xl-2,.my-xl-2{margin-bottom:.5rem!important}.ml-xl-2,.mx-xl-2{margin-left:.5rem!important}.m-xl-3{margin:1rem!important}.mt-xl-3,.my-xl-3{margin-top:1rem!important}.mr-xl-3,.mx-xl-3{margin-right:1rem!important}.mb-xl-3,.my-xl-3{margin-bottom:1rem!important}.ml-xl-3,.mx-xl-3{margin-left:1rem!important}.m-xl-4{margin:1.5rem!important}.mt-xl-4,.my-xl-4{margin-top:1.5rem!important}.mr-xl-4,.mx-xl-4{margin-right:1.5rem!important}.mb-xl-4,.my-xl-4{margin-bottom:1.5rem!important}.ml-xl-4,.mx-xl-4{margin-left:1.5rem!important}.m-xl-5{margin:3rem!important}.mt-xl-5,.my-xl-5{margin-top:3rem!important}.mr-xl-5,.mx-xl-5{margin-right:3rem!important}.mb-xl-5,.my-xl-5{margin-bottom:3rem!important}.ml-xl-5,.mx-xl-5{margin-left:3rem!important}.p-xl-0{padding:0!important}.pt-xl-0,.py-xl-0{padding-top:0!important}.pr-xl-0,.px-xl-0{padding-right:0!important}.pb-xl-0,.py-xl-0{padding-bottom:0!important}.pl-xl-0,.px-xl-0{padding-left:0!important}.p-xl-1{padding:.25rem!important}.pt-xl-1,.py-xl-1{padding-top:.25rem!important}.pr-xl-1,.px-xl-1{padding-right:.25rem!important}.pb-xl-1,.py-xl-1{padding-bottom:.25rem!important}.pl-xl-1,.px-xl-1{padding-left:.25rem!important}.p-xl-2{padding:.5rem!important}.pt-xl-2,.py-xl-2{padding-top:.5rem!important}.pr-xl-2,.px-xl-2{padding-right:.5rem!important}.pb-xl-2,.py-xl-2{padding-bottom:.5rem!important}.pl-xl-2,.px-xl-2{padding-left:.5rem!important}.p-xl-3{padding:1rem!important}.pt-xl-3,.py-xl-3{padding-top:1rem!important}.pr-xl-3,.px-xl-3{padding-right:1rem!important}.pb-xl-3,.py-xl-3{padding-bottom:1rem!important}.pl-xl-3,.px-xl-3{padding-left:1rem!important}.p-xl-4{padding:1.5rem!important}.pt-xl-4,.py-xl-4{padding-top:1.5rem!important}.pr-xl-4,.px-xl-4{padding-right:1.5rem!important}.pb-xl-4,.py-xl-4{padding-bottom:1.5rem!important}.pl-xl-4,.px-xl-4{padding-left:1.5rem!important}.p-xl-5{padding:3rem!important}.pt-xl-5,.py-xl-5{padding-top:3rem!important}.pr-xl-5,.px-xl-5{padding-right:3rem!important}.pb-xl-5,.py-xl-5{padding-bottom:3rem!important}.pl-xl-5,.px-xl-5{padding-left:3rem!important}.m-xl-n1{margin:-.25rem!important}.mt-xl-n1,.my-xl-n1{margin-top:-.25rem!important}.mr-xl-n1,.mx-xl-n1{margin-right:-.25rem!important}.mb-xl-n1,.my-xl-n1{margin-bottom:-.25rem!important}.ml-xl-n1,.mx-xl-n1{margin-left:-.25rem!important}.m-xl-n2{margin:-.5rem!important}.mt-xl-n2,.my-xl-n2{margin-top:-.5rem!important}.mr-xl-n2,.mx-xl-n2{margin-right:-.5rem!important}.mb-xl-n2,.my-xl-n2{margin-bottom:-.5rem!important}.ml-xl-n2,.mx-xl-n2{margin-left:-.5rem!important}.m-xl-n3{margin:-1rem!important}.mt-xl-n3,.my-xl-n3{margin-top:-1rem!important}.mr-xl-n3,.mx-xl-n3{margin-right:-1rem!important}.mb-xl-n3,.my-xl-n3{margin-bottom:-1rem!important}.ml-xl-n3,.mx-xl-n3{margin-left:-1rem!important}.m-xl-n4{margin:-1.5rem!important}.mt-xl-n4,.my-xl-n4{margin-top:-1.5rem!important}.mr-xl-n4,.mx-xl-n4{margin-right:-1.5rem!important}.mb-xl-n4,.my-xl-n4{margin-bottom:-1.5rem!important}.ml-xl-n4,.mx-xl-n4{margin-left:-1.5rem!important}.m-xl-n5{margin:-3rem!important}.mt-xl-n5,.my-xl-n5{margin-top:-3rem!important}.mr-xl-n5,.mx-xl-n5{margin-right:-3rem!important}.mb-xl-n5,.my-xl-n5{margin-bottom:-3rem!important}.ml-xl-n5,.mx-xl-n5{margin-left:-3rem!important}.m-xl-auto{margin:auto!important}.mt-xl-auto,.my-xl-auto{margin-top:auto!important}.mr-xl-auto,.mx-xl-auto{margin-right:auto!important}.mb-xl-auto,.my-xl-auto{margin-bottom:auto!important}.ml-xl-auto,.mx-xl-auto{margin-left:auto!important}}.text-monospace{font-family:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace!important}.text-justify{text-align:justify!important}.text-wrap{white-space:normal!important}.text-nowrap{white-space:nowrap!important}.text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.text-left{text-align:left!important}.text-right{text-align:right!important}.text-center{text-align:center!important}@media (min-width:576px){.text-sm-left{text-align:left!important}.text-sm-right{text-align:right!important}.text-sm-center{text-align:center!important}}@media (min-width:768px){.text-md-left{text-align:left!important}.text-md-right{text-align:right!important}.text-md-center{text-align:center!important}}@media (min-width:992px){.text-lg-left{text-align:left!important}.text-lg-right{text-align:right!important}.text-lg-center{text-align:center!important}}@media (min-width:1200px){.text-xl-left{text-align:left!important}.text-xl-right{text-align:right!important}.text-xl-center{text-align:center!important}}.text-lowercase{text-transform:lowercase!important}.text-uppercase{text-transform:uppercase!important}.text-capitalize{text-transform:capitalize!important}.font-weight-light{font-weight:300!important}.font-weight-lighter{font-weight:lighter!important}.font-weight-normal{font-weight:400!important}.font-weight-bold{font-weight:700!important}.font-weight-bolder{font-weight:bolder!important}.font-italic{font-style:italic!important}.text-white{color:#fff!important}.text-primary{color:#007bff!important}a.text-primary:focus,a.text-primary:hover{color:#0056b3!important}.text-secondary{color:#6c757d!important}a.text-secondary:focus,a.text-secondary:hover{color:#494f54!important}.text-success{color:#28a745!important}a.text-success:focus,a.text-success:hover{color:#19692c!important}.text-info{color:#17a2b8!important}a.text-info:focus,a.text-info:hover{color:#0f6674!important}.text-warning{color:#ffc107!important}a.text-warning:focus,a.text-warning:hover{color:#ba8b00!important}.text-danger{color:#dc3545!important}a.text-danger:focus,a.text-danger:hover{color:#a71d2a!important}.text-light{color:#f8f9fa!important}a.text-light:focus,a.text-light:hover{color:#cbd3da!important}.text-dark{color:#343a40!important}a.text-dark:focus,a.text-dark:hover{color:#121416!important}.text-body{color:#212529!important}.text-muted{color:#6c757d!important}.text-black-50{color:rgba(0,0,0,.5)!important}.text-white-50{color:rgba(255,255,255,.5)!important}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.text-decoration-none{text-decoration:none!important}.text-break{word-break:break-word!important;overflow-wrap:break-word!important}.text-reset{color:inherit!important}.visible{visibility:visible!important}.invisible{visibility:hidden!important}@media print{*,::after,::before{text-shadow:none!important;box-shadow:none!important}a:not(.btn){text-decoration:underline}abbr[title]::after{content:" (" attr(title) ")"}pre{white-space:pre-wrap!important}blockquote,pre{border:1px solid #adb5bd;page-break-inside:avoid}thead{display:table-header-group}img,tr{page-break-inside:avoid}h2,h3,p{orphans:3;widows:3}h2,h3{page-break-after:avoid}@page{size:a3}body{min-width:992px!important}.container{min-width:992px!important}.navbar{display:none}.badge{border:1px solid #000}.table{border-collapse:collapse!important}.table td,.table th{background-color:#fff!important}.table-bordered td,.table-bordered th{border:1px solid #dee2e6!important}.table-dark{color:inherit}.table-dark tbody+tbody,.table-dark td,.table-dark th,.table-dark thead th{border-color:#dee2e6}.table .thead-dark th{color:inherit;border-color:#dee2e6}}

/*!
 * Font Awesome Free 5.12.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
.fa,
.fas,
.far,
.fal,
.fad,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1; }

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em; }

.fa-xs {
  font-size: .75em; }

.fa-sm {
  font-size: .875em; }

.fa-1x {
  font-size: 1em; }

.fa-2x {
  font-size: 2em; }

.fa-3x {
  font-size: 3em; }

.fa-4x {
  font-size: 4em; }

.fa-5x {
  font-size: 5em; }

.fa-6x {
  font-size: 6em; }

.fa-7x {
  font-size: 7em; }

.fa-8x {
  font-size: 8em; }

.fa-9x {
  font-size: 9em; }

.fa-10x {
  font-size: 10em; }

.fa-fw {
  text-align: center;
  width: 1.25em; }

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0; }
  .fa-ul > li {
    position: relative; }

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit; }

.fa-border {
  border: solid 0.08em #eee;
  border-radius: .1em;
  padding: .2em .25em .15em; }

.fa-pull-left {
  float: left; }

.fa-pull-right {
  float: right; }

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: .3em; }

.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: .3em; }

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear; }

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8); }

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg); }

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg); }

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1); }

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1); }

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1); }

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-flip-both {
  -webkit-filter: none;
          filter: none; }

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2.5em; }

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%; }

.fa-stack-1x {
  line-height: inherit; }

.fa-stack-2x {
  font-size: 2em; }

.fa-inverse {
  color: #fff; }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-500px:before {
  content: "\f26e"; }

.fa-accessible-icon:before {
  content: "\f368"; }

.fa-accusoft:before {
  content: "\f369"; }

.fa-acquisitions-incorporated:before {
  content: "\f6af"; }

.fa-ad:before {
  content: "\f641"; }

.fa-address-book:before {
  content: "\f2b9"; }

.fa-address-card:before {
  content: "\f2bb"; }

.fa-adjust:before {
  content: "\f042"; }

.fa-adn:before {
  content: "\f170"; }

.fa-adobe:before {
  content: "\f778"; }

.fa-adversal:before {
  content: "\f36a"; }

.fa-affiliatetheme:before {
  content: "\f36b"; }

.fa-air-freshener:before {
  content: "\f5d0"; }

.fa-airbnb:before {
  content: "\f834"; }

.fa-algolia:before {
  content: "\f36c"; }

.fa-align-center:before {
  content: "\f037"; }

.fa-align-justify:before {
  content: "\f039"; }

.fa-align-left:before {
  content: "\f036"; }

.fa-align-right:before {
  content: "\f038"; }

.fa-alipay:before {
  content: "\f642"; }

.fa-allergies:before {
  content: "\f461"; }

.fa-amazon:before {
  content: "\f270"; }

.fa-amazon-pay:before {
  content: "\f42c"; }

.fa-ambulance:before {
  content: "\f0f9"; }

.fa-american-sign-language-interpreting:before {
  content: "\f2a3"; }

.fa-amilia:before {
  content: "\f36d"; }

.fa-anchor:before {
  content: "\f13d"; }

.fa-android:before {
  content: "\f17b"; }

.fa-angellist:before {
  content: "\f209"; }

.fa-angle-double-down:before {
  content: "\f103"; }

.fa-angle-double-left:before {
  content: "\f100"; }

.fa-angle-double-right:before {
  content: "\f101"; }

.fa-angle-double-up:before {
  content: "\f102"; }

.fa-angle-down:before {
  content: "\f107"; }

.fa-angle-left:before {
  content: "\f104"; }

.fa-angle-right:before {
  content: "\f105"; }

.fa-angle-up:before {
  content: "\f106"; }

.fa-angry:before {
  content: "\f556"; }

.fa-angrycreative:before {
  content: "\f36e"; }

.fa-angular:before {
  content: "\f420"; }

.fa-ankh:before {
  content: "\f644"; }

.fa-app-store:before {
  content: "\f36f"; }

.fa-app-store-ios:before {
  content: "\f370"; }

.fa-apper:before {
  content: "\f371"; }

.fa-apple:before {
  content: "\f179"; }

.fa-apple-alt:before {
  content: "\f5d1"; }

.fa-apple-pay:before {
  content: "\f415"; }

.fa-archive:before {
  content: "\f187"; }

.fa-archway:before {
  content: "\f557"; }

.fa-arrow-alt-circle-down:before {
  content: "\f358"; }

.fa-arrow-alt-circle-left:before {
  content: "\f359"; }

.fa-arrow-alt-circle-right:before {
  content: "\f35a"; }

.fa-arrow-alt-circle-up:before {
  content: "\f35b"; }

.fa-arrow-circle-down:before {
  content: "\f0ab"; }

.fa-arrow-circle-left:before {
  content: "\f0a8"; }

.fa-arrow-circle-right:before {
  content: "\f0a9"; }

.fa-arrow-circle-up:before {
  content: "\f0aa"; }

.fa-arrow-down:before {
  content: "\f063"; }

.fa-arrow-left:before {
  content: "\f060"; }

.fa-arrow-right:before {
  content: "\f061"; }

.fa-arrow-up:before {
  content: "\f062"; }

.fa-arrows-alt:before {
  content: "\f0b2"; }

.fa-arrows-alt-h:before {
  content: "\f337"; }

.fa-arrows-alt-v:before {
  content: "\f338"; }

.fa-artstation:before {
  content: "\f77a"; }

.fa-assistive-listening-systems:before {
  content: "\f2a2"; }

.fa-asterisk:before {
  content: "\f069"; }

.fa-asymmetrik:before {
  content: "\f372"; }

.fa-at:before {
  content: "\f1fa"; }

.fa-atlas:before {
  content: "\f558"; }

.fa-atlassian:before {
  content: "\f77b"; }

.fa-atom:before {
  content: "\f5d2"; }

.fa-audible:before {
  content: "\f373"; }

.fa-audio-description:before {
  content: "\f29e"; }

.fa-autoprefixer:before {
  content: "\f41c"; }

.fa-avianex:before {
  content: "\f374"; }

.fa-aviato:before {
  content: "\f421"; }

.fa-award:before {
  content: "\f559"; }

.fa-aws:before {
  content: "\f375"; }

.fa-baby:before {
  content: "\f77c"; }

.fa-baby-carriage:before {
  content: "\f77d"; }

.fa-backspace:before {
  content: "\f55a"; }

.fa-backward:before {
  content: "\f04a"; }

.fa-bacon:before {
  content: "\f7e5"; }

.fa-bahai:before {
  content: "\f666"; }

.fa-balance-scale:before {
  content: "\f24e"; }

.fa-balance-scale-left:before {
  content: "\f515"; }

.fa-balance-scale-right:before {
  content: "\f516"; }

.fa-ban:before {
  content: "\f05e"; }

.fa-band-aid:before {
  content: "\f462"; }

.fa-bandcamp:before {
  content: "\f2d5"; }

.fa-barcode:before {
  content: "\f02a"; }

.fa-bars:before {
  content: "\f0c9"; }

.fa-baseball-ball:before {
  content: "\f433"; }

.fa-basketball-ball:before {
  content: "\f434"; }

.fa-bath:before {
  content: "\f2cd"; }

.fa-battery-empty:before {
  content: "\f244"; }

.fa-battery-full:before {
  content: "\f240"; }

.fa-battery-half:before {
  content: "\f242"; }

.fa-battery-quarter:before {
  content: "\f243"; }

.fa-battery-three-quarters:before {
  content: "\f241"; }

.fa-battle-net:before {
  content: "\f835"; }

.fa-bed:before {
  content: "\f236"; }

.fa-beer:before {
  content: "\f0fc"; }

.fa-behance:before {
  content: "\f1b4"; }

.fa-behance-square:before {
  content: "\f1b5"; }

.fa-bell:before {
  content: "\f0f3"; }

.fa-bell-slash:before {
  content: "\f1f6"; }

.fa-bezier-curve:before {
  content: "\f55b"; }

.fa-bible:before {
  content: "\f647"; }

.fa-bicycle:before {
  content: "\f206"; }

.fa-biking:before {
  content: "\f84a"; }

.fa-bimobject:before {
  content: "\f378"; }

.fa-binoculars:before {
  content: "\f1e5"; }

.fa-biohazard:before {
  content: "\f780"; }

.fa-birthday-cake:before {
  content: "\f1fd"; }

.fa-bitbucket:before {
  content: "\f171"; }

.fa-bitcoin:before {
  content: "\f379"; }

.fa-bity:before {
  content: "\f37a"; }

.fa-black-tie:before {
  content: "\f27e"; }

.fa-blackberry:before {
  content: "\f37b"; }

.fa-blender:before {
  content: "\f517"; }

.fa-blender-phone:before {
  content: "\f6b6"; }

.fa-blind:before {
  content: "\f29d"; }

.fa-blog:before {
  content: "\f781"; }

.fa-blogger:before {
  content: "\f37c"; }

.fa-blogger-b:before {
  content: "\f37d"; }

.fa-bluetooth:before {
  content: "\f293"; }

.fa-bluetooth-b:before {
  content: "\f294"; }

.fa-bold:before {
  content: "\f032"; }

.fa-bolt:before {
  content: "\f0e7"; }

.fa-bomb:before {
  content: "\f1e2"; }

.fa-bone:before {
  content: "\f5d7"; }

.fa-bong:before {
  content: "\f55c"; }

.fa-book:before {
  content: "\f02d"; }

.fa-book-dead:before {
  content: "\f6b7"; }

.fa-book-medical:before {
  content: "\f7e6"; }

.fa-book-open:before {
  content: "\f518"; }

.fa-book-reader:before {
  content: "\f5da"; }

.fa-bookmark:before {
  content: "\f02e"; }

.fa-bootstrap:before {
  content: "\f836"; }

.fa-border-all:before {
  content: "\f84c"; }

.fa-border-none:before {
  content: "\f850"; }

.fa-border-style:before {
  content: "\f853"; }

.fa-bowling-ball:before {
  content: "\f436"; }

.fa-box:before {
  content: "\f466"; }

.fa-box-open:before {
  content: "\f49e"; }

.fa-boxes:before {
  content: "\f468"; }

.fa-braille:before {
  content: "\f2a1"; }

.fa-brain:before {
  content: "\f5dc"; }

.fa-bread-slice:before {
  content: "\f7ec"; }

.fa-briefcase:before {
  content: "\f0b1"; }

.fa-briefcase-medical:before {
  content: "\f469"; }

.fa-broadcast-tower:before {
  content: "\f519"; }

.fa-broom:before {
  content: "\f51a"; }

.fa-brush:before {
  content: "\f55d"; }

.fa-btc:before {
  content: "\f15a"; }

.fa-buffer:before {
  content: "\f837"; }

.fa-bug:before {
  content: "\f188"; }

.fa-building:before {
  content: "\f1ad"; }

.fa-bullhorn:before {
  content: "\f0a1"; }

.fa-bullseye:before {
  content: "\f140"; }

.fa-burn:before {
  content: "\f46a"; }

.fa-buromobelexperte:before {
  content: "\f37f"; }

.fa-bus:before {
  content: "\f207"; }

.fa-bus-alt:before {
  content: "\f55e"; }

.fa-business-time:before {
  content: "\f64a"; }

.fa-buy-n-large:before {
  content: "\f8a6"; }

.fa-buysellads:before {
  content: "\f20d"; }

.fa-calculator:before {
  content: "\f1ec"; }

.fa-calendar:before {
  content: "\f133"; }

.fa-calendar-alt:before {
  content: "\f073"; }

.fa-calendar-check:before {
  content: "\f274"; }

.fa-calendar-day:before {
  content: "\f783"; }

.fa-calendar-minus:before {
  content: "\f272"; }

.fa-calendar-plus:before {
  content: "\f271"; }

.fa-calendar-times:before {
  content: "\f273"; }

.fa-calendar-week:before {
  content: "\f784"; }

.fa-camera:before {
  content: "\f030"; }

.fa-camera-retro:before {
  content: "\f083"; }

.fa-campground:before {
  content: "\f6bb"; }

.fa-canadian-maple-leaf:before {
  content: "\f785"; }

.fa-candy-cane:before {
  content: "\f786"; }

.fa-cannabis:before {
  content: "\f55f"; }

.fa-capsules:before {
  content: "\f46b"; }

.fa-car:before {
  content: "\f1b9"; }

.fa-car-alt:before {
  content: "\f5de"; }

.fa-car-battery:before {
  content: "\f5df"; }

.fa-car-crash:before {
  content: "\f5e1"; }

.fa-car-side:before {
  content: "\f5e4"; }

.fa-caravan:before {
  content: "\f8ff"; }

.fa-caret-down:before {
  content: "\f0d7"; }

.fa-caret-left:before {
  content: "\f0d9"; }

.fa-caret-right:before {
  content: "\f0da"; }

.fa-caret-square-down:before {
  content: "\f150"; }

.fa-caret-square-left:before {
  content: "\f191"; }

.fa-caret-square-right:before {
  content: "\f152"; }

.fa-caret-square-up:before {
  content: "\f151"; }

.fa-caret-up:before {
  content: "\f0d8"; }

.fa-carrot:before {
  content: "\f787"; }

.fa-cart-arrow-down:before {
  content: "\f218"; }

.fa-cart-plus:before {
  content: "\f217"; }

.fa-cash-register:before {
  content: "\f788"; }

.fa-cat:before {
  content: "\f6be"; }

.fa-cc-amazon-pay:before {
  content: "\f42d"; }

.fa-cc-amex:before {
  content: "\f1f3"; }

.fa-cc-apple-pay:before {
  content: "\f416"; }

.fa-cc-diners-club:before {
  content: "\f24c"; }

.fa-cc-discover:before {
  content: "\f1f2"; }

.fa-cc-jcb:before {
  content: "\f24b"; }

.fa-cc-mastercard:before {
  content: "\f1f1"; }

.fa-cc-paypal:before {
  content: "\f1f4"; }

.fa-cc-stripe:before {
  content: "\f1f5"; }

.fa-cc-visa:before {
  content: "\f1f0"; }

.fa-centercode:before {
  content: "\f380"; }

.fa-centos:before {
  content: "\f789"; }

.fa-certificate:before {
  content: "\f0a3"; }

.fa-chair:before {
  content: "\f6c0"; }

.fa-chalkboard:before {
  content: "\f51b"; }

.fa-chalkboard-teacher:before {
  content: "\f51c"; }

.fa-charging-station:before {
  content: "\f5e7"; }

.fa-chart-area:before {
  content: "\f1fe"; }

.fa-chart-bar:before {
  content: "\f080"; }

.fa-chart-line:before {
  content: "\f201"; }

.fa-chart-pie:before {
  content: "\f200"; }

.fa-check:before {
  content: "\f00c"; }

.fa-check-circle:before {
  content: "\f058"; }

.fa-check-double:before {
  content: "\f560"; }

.fa-check-square:before {
  content: "\f14a"; }

.fa-cheese:before {
  content: "\f7ef"; }

.fa-chess:before {
  content: "\f439"; }

.fa-chess-bishop:before {
  content: "\f43a"; }

.fa-chess-board:before {
  content: "\f43c"; }

.fa-chess-king:before {
  content: "\f43f"; }

.fa-chess-knight:before {
  content: "\f441"; }

.fa-chess-pawn:before {
  content: "\f443"; }

.fa-chess-queen:before {
  content: "\f445"; }

.fa-chess-rook:before {
  content: "\f447"; }

.fa-chevron-circle-down:before {
  content: "\f13a"; }

.fa-chevron-circle-left:before {
  content: "\f137"; }

.fa-chevron-circle-right:before {
  content: "\f138"; }

.fa-chevron-circle-up:before {
  content: "\f139"; }

.fa-chevron-down:before {
  content: "\f078"; }

.fa-chevron-left:before {
  content: "\f053"; }

.fa-chevron-right:before {
  content: "\f054"; }

.fa-chevron-up:before {
  content: "\f077"; }

.fa-child:before {
  content: "\f1ae"; }

.fa-chrome:before {
  content: "\f268"; }

.fa-chromecast:before {
  content: "\f838"; }

.fa-church:before {
  content: "\f51d"; }

.fa-circle:before {
  content: "\f111"; }

.fa-circle-notch:before {
  content: "\f1ce"; }

.fa-city:before {
  content: "\f64f"; }

.fa-clinic-medical:before {
  content: "\f7f2"; }

.fa-clipboard:before {
  content: "\f328"; }

.fa-clipboard-check:before {
  content: "\f46c"; }

.fa-clipboard-list:before {
  content: "\f46d"; }

.fa-clock:before {
  content: "\f017"; }

.fa-clone:before {
  content: "\f24d"; }

.fa-closed-captioning:before {
  content: "\f20a"; }

.fa-cloud:before {
  content: "\f0c2"; }

.fa-cloud-download-alt:before {
  content: "\f381"; }

.fa-cloud-meatball:before {
  content: "\f73b"; }

.fa-cloud-moon:before {
  content: "\f6c3"; }

.fa-cloud-moon-rain:before {
  content: "\f73c"; }

.fa-cloud-rain:before {
  content: "\f73d"; }

.fa-cloud-showers-heavy:before {
  content: "\f740"; }

.fa-cloud-sun:before {
  content: "\f6c4"; }

.fa-cloud-sun-rain:before {
  content: "\f743"; }

.fa-cloud-upload-alt:before {
  content: "\f382"; }

.fa-cloudscale:before {
  content: "\f383"; }

.fa-cloudsmith:before {
  content: "\f384"; }

.fa-cloudversify:before {
  content: "\f385"; }

.fa-cocktail:before {
  content: "\f561"; }

.fa-code:before {
  content: "\f121"; }

.fa-code-branch:before {
  content: "\f126"; }

.fa-codepen:before {
  content: "\f1cb"; }

.fa-codiepie:before {
  content: "\f284"; }

.fa-coffee:before {
  content: "\f0f4"; }

.fa-cog:before {
  content: "\f013"; }

.fa-cogs:before {
  content: "\f085"; }

.fa-coins:before {
  content: "\f51e"; }

.fa-columns:before {
  content: "\f0db"; }

.fa-comment:before {
  content: "\f075"; }

.fa-comment-alt:before {
  content: "\f27a"; }

.fa-comment-dollar:before {
  content: "\f651"; }

.fa-comment-dots:before {
  content: "\f4ad"; }

.fa-comment-medical:before {
  content: "\f7f5"; }

.fa-comment-slash:before {
  content: "\f4b3"; }

.fa-comments:before {
  content: "\f086"; }

.fa-comments-dollar:before {
  content: "\f653"; }

.fa-compact-disc:before {
  content: "\f51f"; }

.fa-compass:before {
  content: "\f14e"; }

.fa-compress:before {
  content: "\f066"; }

.fa-compress-alt:before {
  content: "\f422"; }

.fa-compress-arrows-alt:before {
  content: "\f78c"; }

.fa-concierge-bell:before {
  content: "\f562"; }

.fa-confluence:before {
  content: "\f78d"; }

.fa-connectdevelop:before {
  content: "\f20e"; }

.fa-contao:before {
  content: "\f26d"; }

.fa-cookie:before {
  content: "\f563"; }

.fa-cookie-bite:before {
  content: "\f564"; }

.fa-copy:before {
  content: "\f0c5"; }

.fa-copyright:before {
  content: "\f1f9"; }

.fa-cotton-bureau:before {
  content: "\f89e"; }

.fa-couch:before {
  content: "\f4b8"; }

.fa-cpanel:before {
  content: "\f388"; }

.fa-creative-commons:before {
  content: "\f25e"; }

.fa-creative-commons-by:before {
  content: "\f4e7"; }

.fa-creative-commons-nc:before {
  content: "\f4e8"; }

.fa-creative-commons-nc-eu:before {
  content: "\f4e9"; }

.fa-creative-commons-nc-jp:before {
  content: "\f4ea"; }

.fa-creative-commons-nd:before {
  content: "\f4eb"; }

.fa-creative-commons-pd:before {
  content: "\f4ec"; }

.fa-creative-commons-pd-alt:before {
  content: "\f4ed"; }

.fa-creative-commons-remix:before {
  content: "\f4ee"; }

.fa-creative-commons-sa:before {
  content: "\f4ef"; }

.fa-creative-commons-sampling:before {
  content: "\f4f0"; }

.fa-creative-commons-sampling-plus:before {
  content: "\f4f1"; }

.fa-creative-commons-share:before {
  content: "\f4f2"; }

.fa-creative-commons-zero:before {
  content: "\f4f3"; }

.fa-credit-card:before {
  content: "\f09d"; }

.fa-critical-role:before {
  content: "\f6c9"; }

.fa-crop:before {
  content: "\f125"; }

.fa-crop-alt:before {
  content: "\f565"; }

.fa-cross:before {
  content: "\f654"; }

.fa-crosshairs:before {
  content: "\f05b"; }

.fa-crow:before {
  content: "\f520"; }

.fa-crown:before {
  content: "\f521"; }

.fa-crutch:before {
  content: "\f7f7"; }

.fa-css3:before {
  content: "\f13c"; }

.fa-css3-alt:before {
  content: "\f38b"; }

.fa-cube:before {
  content: "\f1b2"; }

.fa-cubes:before {
  content: "\f1b3"; }

.fa-cut:before {
  content: "\f0c4"; }

.fa-cuttlefish:before {
  content: "\f38c"; }

.fa-d-and-d:before {
  content: "\f38d"; }

.fa-d-and-d-beyond:before {
  content: "\f6ca"; }

.fa-dashcube:before {
  content: "\f210"; }

.fa-database:before {
  content: "\f1c0"; }

.fa-deaf:before {
  content: "\f2a4"; }

.fa-delicious:before {
  content: "\f1a5"; }

.fa-democrat:before {
  content: "\f747"; }

.fa-deploydog:before {
  content: "\f38e"; }

.fa-deskpro:before {
  content: "\f38f"; }

.fa-desktop:before {
  content: "\f108"; }

.fa-dev:before {
  content: "\f6cc"; }

.fa-deviantart:before {
  content: "\f1bd"; }

.fa-dharmachakra:before {
  content: "\f655"; }

.fa-dhl:before {
  content: "\f790"; }

.fa-diagnoses:before {
  content: "\f470"; }

.fa-diaspora:before {
  content: "\f791"; }

.fa-dice:before {
  content: "\f522"; }

.fa-dice-d20:before {
  content: "\f6cf"; }

.fa-dice-d6:before {
  content: "\f6d1"; }

.fa-dice-five:before {
  content: "\f523"; }

.fa-dice-four:before {
  content: "\f524"; }

.fa-dice-one:before {
  content: "\f525"; }

.fa-dice-six:before {
  content: "\f526"; }

.fa-dice-three:before {
  content: "\f527"; }

.fa-dice-two:before {
  content: "\f528"; }

.fa-digg:before {
  content: "\f1a6"; }

.fa-digital-ocean:before {
  content: "\f391"; }

.fa-digital-tachograph:before {
  content: "\f566"; }

.fa-directions:before {
  content: "\f5eb"; }

.fa-discord:before {
  content: "\f392"; }

.fa-discourse:before {
  content: "\f393"; }

.fa-divide:before {
  content: "\f529"; }

.fa-dizzy:before {
  content: "\f567"; }

.fa-dna:before {
  content: "\f471"; }

.fa-dochub:before {
  content: "\f394"; }

.fa-docker:before {
  content: "\f395"; }

.fa-dog:before {
  content: "\f6d3"; }

.fa-dollar-sign:before {
  content: "\f155"; }

.fa-dolly:before {
  content: "\f472"; }

.fa-dolly-flatbed:before {
  content: "\f474"; }

.fa-donate:before {
  content: "\f4b9"; }

.fa-door-closed:before {
  content: "\f52a"; }

.fa-door-open:before {
  content: "\f52b"; }

.fa-dot-circle:before {
  content: "\f192"; }

.fa-dove:before {
  content: "\f4ba"; }

.fa-download:before {
  content: "\f019"; }

.fa-draft2digital:before {
  content: "\f396"; }

.fa-drafting-compass:before {
  content: "\f568"; }

.fa-dragon:before {
  content: "\f6d5"; }

.fa-draw-polygon:before {
  content: "\f5ee"; }

.fa-dribbble:before {
  content: "\f17d"; }

.fa-dribbble-square:before {
  content: "\f397"; }

.fa-dropbox:before {
  content: "\f16b"; }

.fa-drum:before {
  content: "\f569"; }

.fa-drum-steelpan:before {
  content: "\f56a"; }

.fa-drumstick-bite:before {
  content: "\f6d7"; }

.fa-drupal:before {
  content: "\f1a9"; }

.fa-dumbbell:before {
  content: "\f44b"; }

.fa-dumpster:before {
  content: "\f793"; }

.fa-dumpster-fire:before {
  content: "\f794"; }

.fa-dungeon:before {
  content: "\f6d9"; }

.fa-dyalog:before {
  content: "\f399"; }

.fa-earlybirds:before {
  content: "\f39a"; }

.fa-ebay:before {
  content: "\f4f4"; }

.fa-edge:before {
  content: "\f282"; }

.fa-edit:before {
  content: "\f044"; }

.fa-egg:before {
  content: "\f7fb"; }

.fa-eject:before {
  content: "\f052"; }

.fa-elementor:before {
  content: "\f430"; }

.fa-ellipsis-h:before {
  content: "\f141"; }

.fa-ellipsis-v:before {
  content: "\f142"; }

.fa-ello:before {
  content: "\f5f1"; }

.fa-ember:before {
  content: "\f423"; }

.fa-empire:before {
  content: "\f1d1"; }

.fa-envelope:before {
  content: "\f0e0"; }

.fa-envelope-open:before {
  content: "\f2b6"; }

.fa-envelope-open-text:before {
  content: "\f658"; }

.fa-envelope-square:before {
  content: "\f199"; }

.fa-envira:before {
  content: "\f299"; }

.fa-equals:before {
  content: "\f52c"; }

.fa-eraser:before {
  content: "\f12d"; }

.fa-erlang:before {
  content: "\f39d"; }

.fa-ethereum:before {
  content: "\f42e"; }

.fa-ethernet:before {
  content: "\f796"; }

.fa-etsy:before {
  content: "\f2d7"; }

.fa-euro-sign:before {
  content: "\f153"; }

.fa-evernote:before {
  content: "\f839"; }

.fa-exchange-alt:before {
  content: "\f362"; }

.fa-exclamation:before {
  content: "\f12a"; }

.fa-exclamation-circle:before {
  content: "\f06a"; }

.fa-exclamation-triangle:before {
  content: "\f071"; }

.fa-expand:before {
  content: "\f065"; }

.fa-expand-alt:before {
  content: "\f424"; }

.fa-expand-arrows-alt:before {
  content: "\f31e"; }

.fa-expeditedssl:before {
  content: "\f23e"; }

.fa-external-link-alt:before {
  content: "\f35d"; }

.fa-external-link-square-alt:before {
  content: "\f360"; }

.fa-eye:before {
  content: "\f06e"; }

.fa-eye-dropper:before {
  content: "\f1fb"; }

.fa-eye-slash:before {
  content: "\f070"; }

.fa-facebook:before {
  content: "\f09a"; }

.fa-facebook-f:before {
  content: "\f39e"; }

.fa-facebook-messenger:before {
  content: "\f39f"; }

.fa-facebook-square:before {
  content: "\f082"; }

.fa-fan:before {
  content: "\f863"; }

.fa-fantasy-flight-games:before {
  content: "\f6dc"; }

.fa-fast-backward:before {
  content: "\f049"; }

.fa-fast-forward:before {
  content: "\f050"; }

.fa-fax:before {
  content: "\f1ac"; }

.fa-feather:before {
  content: "\f52d"; }

.fa-feather-alt:before {
  content: "\f56b"; }

.fa-fedex:before {
  content: "\f797"; }

.fa-fedora:before {
  content: "\f798"; }

.fa-female:before {
  content: "\f182"; }

.fa-fighter-jet:before {
  content: "\f0fb"; }

.fa-figma:before {
  content: "\f799"; }

.fa-file:before {
  content: "\f15b"; }

.fa-file-alt:before {
  content: "\f15c"; }

.fa-file-archive:before {
  content: "\f1c6"; }

.fa-file-audio:before {
  content: "\f1c7"; }

.fa-file-code:before {
  content: "\f1c9"; }

.fa-file-contract:before {
  content: "\f56c"; }

.fa-file-csv:before {
  content: "\f6dd"; }

.fa-file-download:before {
  content: "\f56d"; }

.fa-file-excel:before {
  content: "\f1c3"; }

.fa-file-export:before {
  content: "\f56e"; }

.fa-file-image:before {
  content: "\f1c5"; }

.fa-file-import:before {
  content: "\f56f"; }

.fa-file-invoice:before {
  content: "\f570"; }

.fa-file-invoice-dollar:before {
  content: "\f571"; }

.fa-file-medical:before {
  content: "\f477"; }

.fa-file-medical-alt:before {
  content: "\f478"; }

.fa-file-pdf:before {
  content: "\f1c1"; }

.fa-file-powerpoint:before {
  content: "\f1c4"; }

.fa-file-prescription:before {
  content: "\f572"; }

.fa-file-signature:before {
  content: "\f573"; }

.fa-file-upload:before {
  content: "\f574"; }

.fa-file-video:before {
  content: "\f1c8"; }

.fa-file-word:before {
  content: "\f1c2"; }

.fa-fill:before {
  content: "\f575"; }

.fa-fill-drip:before {
  content: "\f576"; }

.fa-film:before {
  content: "\f008"; }

.fa-filter:before {
  content: "\f0b0"; }

.fa-fingerprint:before {
  content: "\f577"; }

.fa-fire:before {
  content: "\f06d"; }

.fa-fire-alt:before {
  content: "\f7e4"; }

.fa-fire-extinguisher:before {
  content: "\f134"; }

.fa-firefox:before {
  content: "\f269"; }

.fa-firefox-browser:before {
  content: "\f907"; }

.fa-first-aid:before {
  content: "\f479"; }

.fa-first-order:before {
  content: "\f2b0"; }

.fa-first-order-alt:before {
  content: "\f50a"; }

.fa-firstdraft:before {
  content: "\f3a1"; }

.fa-fish:before {
  content: "\f578"; }

.fa-fist-raised:before {
  content: "\f6de"; }

.fa-flag:before {
  content: "\f024"; }

.fa-flag-checkered:before {
  content: "\f11e"; }

.fa-flag-usa:before {
  content: "\f74d"; }

.fa-flask:before {
  content: "\f0c3"; }

.fa-flickr:before {
  content: "\f16e"; }

.fa-flipboard:before {
  content: "\f44d"; }

.fa-flushed:before {
  content: "\f579"; }

.fa-fly:before {
  content: "\f417"; }

.fa-folder:before {
  content: "\f07b"; }

.fa-folder-minus:before {
  content: "\f65d"; }

.fa-folder-open:before {
  content: "\f07c"; }

.fa-folder-plus:before {
  content: "\f65e"; }

.fa-font:before {
  content: "\f031"; }

.fa-font-awesome:before {
  content: "\f2b4"; }

.fa-font-awesome-alt:before {
  content: "\f35c"; }

.fa-font-awesome-flag:before {
  content: "\f425"; }

.fa-font-awesome-logo-full:before {
  content: "\f4e6"; }

.fa-fonticons:before {
  content: "\f280"; }

.fa-fonticons-fi:before {
  content: "\f3a2"; }

.fa-football-ball:before {
  content: "\f44e"; }

.fa-fort-awesome:before {
  content: "\f286"; }

.fa-fort-awesome-alt:before {
  content: "\f3a3"; }

.fa-forumbee:before {
  content: "\f211"; }

.fa-forward:before {
  content: "\f04e"; }

.fa-foursquare:before {
  content: "\f180"; }

.fa-free-code-camp:before {
  content: "\f2c5"; }

.fa-freebsd:before {
  content: "\f3a4"; }

.fa-frog:before {
  content: "\f52e"; }

.fa-frown:before {
  content: "\f119"; }

.fa-frown-open:before {
  content: "\f57a"; }

.fa-fulcrum:before {
  content: "\f50b"; }

.fa-funnel-dollar:before {
  content: "\f662"; }

.fa-futbol:before {
  content: "\f1e3"; }

.fa-galactic-republic:before {
  content: "\f50c"; }

.fa-galactic-senate:before {
  content: "\f50d"; }

.fa-gamepad:before {
  content: "\f11b"; }

.fa-gas-pump:before {
  content: "\f52f"; }

.fa-gavel:before {
  content: "\f0e3"; }

.fa-gem:before {
  content: "\f3a5"; }

.fa-genderless:before {
  content: "\f22d"; }

.fa-get-pocket:before {
  content: "\f265"; }

.fa-gg:before {
  content: "\f260"; }

.fa-gg-circle:before {
  content: "\f261"; }

.fa-ghost:before {
  content: "\f6e2"; }

.fa-gift:before {
  content: "\f06b"; }

.fa-gifts:before {
  content: "\f79c"; }

.fa-git:before {
  content: "\f1d3"; }

.fa-git-alt:before {
  content: "\f841"; }

.fa-git-square:before {
  content: "\f1d2"; }

.fa-github:before {
  content: "\f09b"; }

.fa-github-alt:before {
  content: "\f113"; }

.fa-github-square:before {
  content: "\f092"; }

.fa-gitkraken:before {
  content: "\f3a6"; }

.fa-gitlab:before {
  content: "\f296"; }

.fa-gitter:before {
  content: "\f426"; }

.fa-glass-cheers:before {
  content: "\f79f"; }

.fa-glass-martini:before {
  content: "\f000"; }

.fa-glass-martini-alt:before {
  content: "\f57b"; }

.fa-glass-whiskey:before {
  content: "\f7a0"; }

.fa-glasses:before {
  content: "\f530"; }

.fa-glide:before {
  content: "\f2a5"; }

.fa-glide-g:before {
  content: "\f2a6"; }

.fa-globe:before {
  content: "\f0ac"; }

.fa-globe-africa:before {
  content: "\f57c"; }

.fa-globe-americas:before {
  content: "\f57d"; }

.fa-globe-asia:before {
  content: "\f57e"; }

.fa-globe-europe:before {
  content: "\f7a2"; }

.fa-gofore:before {
  content: "\f3a7"; }

.fa-golf-ball:before {
  content: "\f450"; }

.fa-goodreads:before {
  content: "\f3a8"; }

.fa-goodreads-g:before {
  content: "\f3a9"; }

.fa-google:before {
  content: "\f1a0"; }

.fa-google-drive:before {
  content: "\f3aa"; }

.fa-google-play:before {
  content: "\f3ab"; }

.fa-google-plus:before {
  content: "\f2b3"; }

.fa-google-plus-g:before {
  content: "\f0d5"; }

.fa-google-plus-square:before {
  content: "\f0d4"; }

.fa-google-wallet:before {
  content: "\f1ee"; }

.fa-gopuram:before {
  content: "\f664"; }

.fa-graduation-cap:before {
  content: "\f19d"; }

.fa-gratipay:before {
  content: "\f184"; }

.fa-grav:before {
  content: "\f2d6"; }

.fa-greater-than:before {
  content: "\f531"; }

.fa-greater-than-equal:before {
  content: "\f532"; }

.fa-grimace:before {
  content: "\f57f"; }

.fa-grin:before {
  content: "\f580"; }

.fa-grin-alt:before {
  content: "\f581"; }

.fa-grin-beam:before {
  content: "\f582"; }

.fa-grin-beam-sweat:before {
  content: "\f583"; }

.fa-grin-hearts:before {
  content: "\f584"; }

.fa-grin-squint:before {
  content: "\f585"; }

.fa-grin-squint-tears:before {
  content: "\f586"; }

.fa-grin-stars:before {
  content: "\f587"; }

.fa-grin-tears:before {
  content: "\f588"; }

.fa-grin-tongue:before {
  content: "\f589"; }

.fa-grin-tongue-squint:before {
  content: "\f58a"; }

.fa-grin-tongue-wink:before {
  content: "\f58b"; }

.fa-grin-wink:before {
  content: "\f58c"; }

.fa-grip-horizontal:before {
  content: "\f58d"; }

.fa-grip-lines:before {
  content: "\f7a4"; }

.fa-grip-lines-vertical:before {
  content: "\f7a5"; }

.fa-grip-vertical:before {
  content: "\f58e"; }

.fa-gripfire:before {
  content: "\f3ac"; }

.fa-grunt:before {
  content: "\f3ad"; }

.fa-guitar:before {
  content: "\f7a6"; }

.fa-gulp:before {
  content: "\f3ae"; }

.fa-h-square:before {
  content: "\f0fd"; }

.fa-hacker-news:before {
  content: "\f1d4"; }

.fa-hacker-news-square:before {
  content: "\f3af"; }

.fa-hackerrank:before {
  content: "\f5f7"; }

.fa-hamburger:before {
  content: "\f805"; }

.fa-hammer:before {
  content: "\f6e3"; }

.fa-hamsa:before {
  content: "\f665"; }

.fa-hand-holding:before {
  content: "\f4bd"; }

.fa-hand-holding-heart:before {
  content: "\f4be"; }

.fa-hand-holding-usd:before {
  content: "\f4c0"; }

.fa-hand-lizard:before {
  content: "\f258"; }

.fa-hand-middle-finger:before {
  content: "\f806"; }

.fa-hand-paper:before {
  content: "\f256"; }

.fa-hand-peace:before {
  content: "\f25b"; }

.fa-hand-point-down:before {
  content: "\f0a7"; }

.fa-hand-point-left:before {
  content: "\f0a5"; }

.fa-hand-point-right:before {
  content: "\f0a4"; }

.fa-hand-point-up:before {
  content: "\f0a6"; }

.fa-hand-pointer:before {
  content: "\f25a"; }

.fa-hand-rock:before {
  content: "\f255"; }

.fa-hand-scissors:before {
  content: "\f257"; }

.fa-hand-spock:before {
  content: "\f259"; }

.fa-hands:before {
  content: "\f4c2"; }

.fa-hands-helping:before {
  content: "\f4c4"; }

.fa-handshake:before {
  content: "\f2b5"; }

.fa-hanukiah:before {
  content: "\f6e6"; }

.fa-hard-hat:before {
  content: "\f807"; }

.fa-hashtag:before {
  content: "\f292"; }

.fa-hat-cowboy:before {
  content: "\f8c0"; }

.fa-hat-cowboy-side:before {
  content: "\f8c1"; }

.fa-hat-wizard:before {
  content: "\f6e8"; }

.fa-hdd:before {
  content: "\f0a0"; }

.fa-heading:before {
  content: "\f1dc"; }

.fa-headphones:before {
  content: "\f025"; }

.fa-headphones-alt:before {
  content: "\f58f"; }

.fa-headset:before {
  content: "\f590"; }

.fa-heart:before {
  content: "\f004"; }

.fa-heart-broken:before {
  content: "\f7a9"; }

.fa-heartbeat:before {
  content: "\f21e"; }

.fa-helicopter:before {
  content: "\f533"; }

.fa-highlighter:before {
  content: "\f591"; }

.fa-hiking:before {
  content: "\f6ec"; }

.fa-hippo:before {
  content: "\f6ed"; }

.fa-hips:before {
  content: "\f452"; }

.fa-hire-a-helper:before {
  content: "\f3b0"; }

.fa-history:before {
  content: "\f1da"; }

.fa-hockey-puck:before {
  content: "\f453"; }

.fa-holly-berry:before {
  content: "\f7aa"; }

.fa-home:before {
  content: "\f015"; }

.fa-hooli:before {
  content: "\f427"; }

.fa-hornbill:before {
  content: "\f592"; }

.fa-horse:before {
  content: "\f6f0"; }

.fa-horse-head:before {
  content: "\f7ab"; }

.fa-hospital:before {
  content: "\f0f8"; }

.fa-hospital-alt:before {
  content: "\f47d"; }

.fa-hospital-symbol:before {
  content: "\f47e"; }

.fa-hot-tub:before {
  content: "\f593"; }

.fa-hotdog:before {
  content: "\f80f"; }

.fa-hotel:before {
  content: "\f594"; }

.fa-hotjar:before {
  content: "\f3b1"; }

.fa-hourglass:before {
  content: "\f254"; }

.fa-hourglass-end:before {
  content: "\f253"; }

.fa-hourglass-half:before {
  content: "\f252"; }

.fa-hourglass-start:before {
  content: "\f251"; }

.fa-house-damage:before {
  content: "\f6f1"; }

.fa-houzz:before {
  content: "\f27c"; }

.fa-hryvnia:before {
  content: "\f6f2"; }

.fa-html5:before {
  content: "\f13b"; }

.fa-hubspot:before {
  content: "\f3b2"; }

.fa-i-cursor:before {
  content: "\f246"; }

.fa-ice-cream:before {
  content: "\f810"; }

.fa-icicles:before {
  content: "\f7ad"; }

.fa-icons:before {
  content: "\f86d"; }

.fa-id-badge:before {
  content: "\f2c1"; }

.fa-id-card:before {
  content: "\f2c2"; }

.fa-id-card-alt:before {
  content: "\f47f"; }

.fa-ideal:before {
  content: "\f913"; }

.fa-igloo:before {
  content: "\f7ae"; }

.fa-image:before {
  content: "\f03e"; }

.fa-images:before {
  content: "\f302"; }

.fa-imdb:before {
  content: "\f2d8"; }

.fa-inbox:before {
  content: "\f01c"; }

.fa-indent:before {
  content: "\f03c"; }

.fa-industry:before {
  content: "\f275"; }

.fa-infinity:before {
  content: "\f534"; }

.fa-info:before {
  content: "\f129"; }

.fa-info-circle:before {
  content: "\f05a"; }

.fa-instagram:before {
  content: "\f16d"; }

.fa-intercom:before {
  content: "\f7af"; }

.fa-internet-explorer:before {
  content: "\f26b"; }

.fa-invision:before {
  content: "\f7b0"; }

.fa-ioxhost:before {
  content: "\f208"; }

.fa-italic:before {
  content: "\f033"; }

.fa-itch-io:before {
  content: "\f83a"; }

.fa-itunes:before {
  content: "\f3b4"; }

.fa-itunes-note:before {
  content: "\f3b5"; }

.fa-java:before {
  content: "\f4e4"; }

.fa-jedi:before {
  content: "\f669"; }

.fa-jedi-order:before {
  content: "\f50e"; }

.fa-jenkins:before {
  content: "\f3b6"; }

.fa-jira:before {
  content: "\f7b1"; }

.fa-joget:before {
  content: "\f3b7"; }

.fa-joint:before {
  content: "\f595"; }

.fa-joomla:before {
  content: "\f1aa"; }

.fa-journal-whills:before {
  content: "\f66a"; }

.fa-js:before {
  content: "\f3b8"; }

.fa-js-square:before {
  content: "\f3b9"; }

.fa-jsfiddle:before {
  content: "\f1cc"; }

.fa-kaaba:before {
  content: "\f66b"; }

.fa-kaggle:before {
  content: "\f5fa"; }

.fa-key:before {
  content: "\f084"; }

.fa-keybase:before {
  content: "\f4f5"; }

.fa-keyboard:before {
  content: "\f11c"; }

.fa-keycdn:before {
  content: "\f3ba"; }

.fa-khanda:before {
  content: "\f66d"; }

.fa-kickstarter:before {
  content: "\f3bb"; }

.fa-kickstarter-k:before {
  content: "\f3bc"; }

.fa-kiss:before {
  content: "\f596"; }

.fa-kiss-beam:before {
  content: "\f597"; }

.fa-kiss-wink-heart:before {
  content: "\f598"; }

.fa-kiwi-bird:before {
  content: "\f535"; }

.fa-korvue:before {
  content: "\f42f"; }

.fa-landmark:before {
  content: "\f66f"; }

.fa-language:before {
  content: "\f1ab"; }

.fa-laptop:before {
  content: "\f109"; }

.fa-laptop-code:before {
  content: "\f5fc"; }

.fa-laptop-medical:before {
  content: "\f812"; }

.fa-laravel:before {
  content: "\f3bd"; }

.fa-lastfm:before {
  content: "\f202"; }

.fa-lastfm-square:before {
  content: "\f203"; }

.fa-laugh:before {
  content: "\f599"; }

.fa-laugh-beam:before {
  content: "\f59a"; }

.fa-laugh-squint:before {
  content: "\f59b"; }

.fa-laugh-wink:before {
  content: "\f59c"; }

.fa-layer-group:before {
  content: "\f5fd"; }

.fa-leaf:before {
  content: "\f06c"; }

.fa-leanpub:before {
  content: "\f212"; }

.fa-lemon:before {
  content: "\f094"; }

.fa-less:before {
  content: "\f41d"; }

.fa-less-than:before {
  content: "\f536"; }

.fa-less-than-equal:before {
  content: "\f537"; }

.fa-level-down-alt:before {
  content: "\f3be"; }

.fa-level-up-alt:before {
  content: "\f3bf"; }

.fa-life-ring:before {
  content: "\f1cd"; }

.fa-lightbulb:before {
  content: "\f0eb"; }

.fa-line:before {
  content: "\f3c0"; }

.fa-link:before {
  content: "\f0c1"; }

.fa-linkedin:before {
  content: "\f08c"; }

.fa-linkedin-in:before {
  content: "\f0e1"; }

.fa-linode:before {
  content: "\f2b8"; }

.fa-linux:before {
  content: "\f17c"; }

.fa-lira-sign:before {
  content: "\f195"; }

.fa-list:before {
  content: "\f03a"; }

.fa-list-alt:before {
  content: "\f022"; }

.fa-list-ol:before {
  content: "\f0cb"; }

.fa-list-ul:before {
  content: "\f0ca"; }

.fa-location-arrow:before {
  content: "\f124"; }

.fa-lock:before {
  content: "\f023"; }

.fa-lock-open:before {
  content: "\f3c1"; }

.fa-long-arrow-alt-down:before {
  content: "\f309"; }

.fa-long-arrow-alt-left:before {
  content: "\f30a"; }

.fa-long-arrow-alt-right:before {
  content: "\f30b"; }

.fa-long-arrow-alt-up:before {
  content: "\f30c"; }

.fa-low-vision:before {
  content: "\f2a8"; }

.fa-luggage-cart:before {
  content: "\f59d"; }

.fa-lyft:before {
  content: "\f3c3"; }

.fa-magento:before {
  content: "\f3c4"; }

.fa-magic:before {
  content: "\f0d0"; }

.fa-magnet:before {
  content: "\f076"; }

.fa-mail-bulk:before {
  content: "\f674"; }

.fa-mailchimp:before {
  content: "\f59e"; }

.fa-male:before {
  content: "\f183"; }

.fa-mandalorian:before {
  content: "\f50f"; }

.fa-map:before {
  content: "\f279"; }

.fa-map-marked:before {
  content: "\f59f"; }

.fa-map-marked-alt:before {
  content: "\f5a0"; }

.fa-map-marker:before {
  content: "\f041"; }

.fa-map-marker-alt:before {
  content: "\f3c5"; }

.fa-map-pin:before {
  content: "\f276"; }

.fa-map-signs:before {
  content: "\f277"; }

.fa-markdown:before {
  content: "\f60f"; }

.fa-marker:before {
  content: "\f5a1"; }

.fa-mars:before {
  content: "\f222"; }

.fa-mars-double:before {
  content: "\f227"; }

.fa-mars-stroke:before {
  content: "\f229"; }

.fa-mars-stroke-h:before {
  content: "\f22b"; }

.fa-mars-stroke-v:before {
  content: "\f22a"; }

.fa-mask:before {
  content: "\f6fa"; }

.fa-mastodon:before {
  content: "\f4f6"; }

.fa-maxcdn:before {
  content: "\f136"; }

.fa-mdb:before {
  content: "\f8ca"; }

.fa-medal:before {
  content: "\f5a2"; }

.fa-medapps:before {
  content: "\f3c6"; }

.fa-medium:before {
  content: "\f23a"; }

.fa-medium-m:before {
  content: "\f3c7"; }

.fa-medkit:before {
  content: "\f0fa"; }

.fa-medrt:before {
  content: "\f3c8"; }

.fa-meetup:before {
  content: "\f2e0"; }

.fa-megaport:before {
  content: "\f5a3"; }

.fa-meh:before {
  content: "\f11a"; }

.fa-meh-blank:before {
  content: "\f5a4"; }

.fa-meh-rolling-eyes:before {
  content: "\f5a5"; }

.fa-memory:before {
  content: "\f538"; }

.fa-mendeley:before {
  content: "\f7b3"; }

.fa-menorah:before {
  content: "\f676"; }

.fa-mercury:before {
  content: "\f223"; }

.fa-meteor:before {
  content: "\f753"; }

.fa-microblog:before {
  content: "\f91a"; }

.fa-microchip:before {
  content: "\f2db"; }

.fa-microphone:before {
  content: "\f130"; }

.fa-microphone-alt:before {
  content: "\f3c9"; }

.fa-microphone-alt-slash:before {
  content: "\f539"; }

.fa-microphone-slash:before {
  content: "\f131"; }

.fa-microscope:before {
  content: "\f610"; }

.fa-microsoft:before {
  content: "\f3ca"; }

.fa-minus:before {
  content: "\f068"; }

.fa-minus-circle:before {
  content: "\f056"; }

.fa-minus-square:before {
  content: "\f146"; }

.fa-mitten:before {
  content: "\f7b5"; }

.fa-mix:before {
  content: "\f3cb"; }

.fa-mixcloud:before {
  content: "\f289"; }

.fa-mizuni:before {
  content: "\f3cc"; }

.fa-mobile:before {
  content: "\f10b"; }

.fa-mobile-alt:before {
  content: "\f3cd"; }

.fa-modx:before {
  content: "\f285"; }

.fa-monero:before {
  content: "\f3d0"; }

.fa-money-bill:before {
  content: "\f0d6"; }

.fa-money-bill-alt:before {
  content: "\f3d1"; }

.fa-money-bill-wave:before {
  content: "\f53a"; }

.fa-money-bill-wave-alt:before {
  content: "\f53b"; }

.fa-money-check:before {
  content: "\f53c"; }

.fa-money-check-alt:before {
  content: "\f53d"; }

.fa-monument:before {
  content: "\f5a6"; }

.fa-moon:before {
  content: "\f186"; }

.fa-mortar-pestle:before {
  content: "\f5a7"; }

.fa-mosque:before {
  content: "\f678"; }

.fa-motorcycle:before {
  content: "\f21c"; }

.fa-mountain:before {
  content: "\f6fc"; }

.fa-mouse:before {
  content: "\f8cc"; }

.fa-mouse-pointer:before {
  content: "\f245"; }

.fa-mug-hot:before {
  content: "\f7b6"; }

.fa-music:before {
  content: "\f001"; }

.fa-napster:before {
  content: "\f3d2"; }

.fa-neos:before {
  content: "\f612"; }

.fa-network-wired:before {
  content: "\f6ff"; }

.fa-neuter:before {
  content: "\f22c"; }

.fa-newspaper:before {
  content: "\f1ea"; }

.fa-nimblr:before {
  content: "\f5a8"; }

.fa-node:before {
  content: "\f419"; }

.fa-node-js:before {
  content: "\f3d3"; }

.fa-not-equal:before {
  content: "\f53e"; }

.fa-notes-medical:before {
  content: "\f481"; }

.fa-npm:before {
  content: "\f3d4"; }

.fa-ns8:before {
  content: "\f3d5"; }

.fa-nutritionix:before {
  content: "\f3d6"; }

.fa-object-group:before {
  content: "\f247"; }

.fa-object-ungroup:before {
  content: "\f248"; }

.fa-odnoklassniki:before {
  content: "\f263"; }

.fa-odnoklassniki-square:before {
  content: "\f264"; }

.fa-oil-can:before {
  content: "\f613"; }

.fa-old-republic:before {
  content: "\f510"; }

.fa-om:before {
  content: "\f679"; }

.fa-opencart:before {
  content: "\f23d"; }

.fa-openid:before {
  content: "\f19b"; }

.fa-opera:before {
  content: "\f26a"; }

.fa-optin-monster:before {
  content: "\f23c"; }

.fa-orcid:before {
  content: "\f8d2"; }

.fa-osi:before {
  content: "\f41a"; }

.fa-otter:before {
  content: "\f700"; }

.fa-outdent:before {
  content: "\f03b"; }

.fa-page4:before {
  content: "\f3d7"; }

.fa-pagelines:before {
  content: "\f18c"; }

.fa-pager:before {
  content: "\f815"; }

.fa-paint-brush:before {
  content: "\f1fc"; }

.fa-paint-roller:before {
  content: "\f5aa"; }

.fa-palette:before {
  content: "\f53f"; }

.fa-palfed:before {
  content: "\f3d8"; }

.fa-pallet:before {
  content: "\f482"; }

.fa-paper-plane:before {
  content: "\f1d8"; }

.fa-paperclip:before {
  content: "\f0c6"; }

.fa-parachute-box:before {
  content: "\f4cd"; }

.fa-paragraph:before {
  content: "\f1dd"; }

.fa-parking:before {
  content: "\f540"; }

.fa-passport:before {
  content: "\f5ab"; }

.fa-pastafarianism:before {
  content: "\f67b"; }

.fa-paste:before {
  content: "\f0ea"; }

.fa-patreon:before {
  content: "\f3d9"; }

.fa-pause:before {
  content: "\f04c"; }

.fa-pause-circle:before {
  content: "\f28b"; }

.fa-paw:before {
  content: "\f1b0"; }

.fa-paypal:before {
  content: "\f1ed"; }

.fa-peace:before {
  content: "\f67c"; }

.fa-pen:before {
  content: "\f304"; }

.fa-pen-alt:before {
  content: "\f305"; }

.fa-pen-fancy:before {
  content: "\f5ac"; }

.fa-pen-nib:before {
  content: "\f5ad"; }

.fa-pen-square:before {
  content: "\f14b"; }

.fa-pencil-alt:before {
  content: "\f303"; }

.fa-pencil-ruler:before {
  content: "\f5ae"; }

.fa-penny-arcade:before {
  content: "\f704"; }

.fa-people-carry:before {
  content: "\f4ce"; }

.fa-pepper-hot:before {
  content: "\f816"; }

.fa-percent:before {
  content: "\f295"; }

.fa-percentage:before {
  content: "\f541"; }

.fa-periscope:before {
  content: "\f3da"; }

.fa-person-booth:before {
  content: "\f756"; }

.fa-phabricator:before {
  content: "\f3db"; }

.fa-phoenix-framework:before {
  content: "\f3dc"; }

.fa-phoenix-squadron:before {
  content: "\f511"; }

.fa-phone:before {
  content: "\f095"; }

.fa-phone-alt:before {
  content: "\f879"; }

.fa-phone-slash:before {
  content: "\f3dd"; }

.fa-phone-square:before {
  content: "\f098"; }

.fa-phone-square-alt:before {
  content: "\f87b"; }

.fa-phone-volume:before {
  content: "\f2a0"; }

.fa-photo-video:before {
  content: "\f87c"; }

.fa-php:before {
  content: "\f457"; }

.fa-pied-piper:before {
  content: "\f2ae"; }

.fa-pied-piper-alt:before {
  content: "\f1a8"; }

.fa-pied-piper-hat:before {
  content: "\f4e5"; }

.fa-pied-piper-pp:before {
  content: "\f1a7"; }

.fa-pied-piper-square:before {
  content: "\f91e"; }

.fa-piggy-bank:before {
  content: "\f4d3"; }

.fa-pills:before {
  content: "\f484"; }

.fa-pinterest:before {
  content: "\f0d2"; }

.fa-pinterest-p:before {
  content: "\f231"; }

.fa-pinterest-square:before {
  content: "\f0d3"; }

.fa-pizza-slice:before {
  content: "\f818"; }

.fa-place-of-worship:before {
  content: "\f67f"; }

.fa-plane:before {
  content: "\f072"; }

.fa-plane-arrival:before {
  content: "\f5af"; }

.fa-plane-departure:before {
  content: "\f5b0"; }

.fa-play:before {
  content: "\f04b"; }

.fa-play-circle:before {
  content: "\f144"; }

.fa-playstation:before {
  content: "\f3df"; }

.fa-plug:before {
  content: "\f1e6"; }

.fa-plus:before {
  content: "\f067"; }

.fa-plus-circle:before {
  content: "\f055"; }

.fa-plus-square:before {
  content: "\f0fe"; }

.fa-podcast:before {
  content: "\f2ce"; }

.fa-poll:before {
  content: "\f681"; }

.fa-poll-h:before {
  content: "\f682"; }

.fa-poo:before {
  content: "\f2fe"; }

.fa-poo-storm:before {
  content: "\f75a"; }

.fa-poop:before {
  content: "\f619"; }

.fa-portrait:before {
  content: "\f3e0"; }

.fa-pound-sign:before {
  content: "\f154"; }

.fa-power-off:before {
  content: "\f011"; }

.fa-pray:before {
  content: "\f683"; }

.fa-praying-hands:before {
  content: "\f684"; }

.fa-prescription:before {
  content: "\f5b1"; }

.fa-prescription-bottle:before {
  content: "\f485"; }

.fa-prescription-bottle-alt:before {
  content: "\f486"; }

.fa-print:before {
  content: "\f02f"; }

.fa-procedures:before {
  content: "\f487"; }

.fa-product-hunt:before {
  content: "\f288"; }

.fa-project-diagram:before {
  content: "\f542"; }

.fa-pushed:before {
  content: "\f3e1"; }

.fa-puzzle-piece:before {
  content: "\f12e"; }

.fa-python:before {
  content: "\f3e2"; }

.fa-qq:before {
  content: "\f1d6"; }

.fa-qrcode:before {
  content: "\f029"; }

.fa-question:before {
  content: "\f128"; }

.fa-question-circle:before {
  content: "\f059"; }

.fa-quidditch:before {
  content: "\f458"; }

.fa-quinscape:before {
  content: "\f459"; }

.fa-quora:before {
  content: "\f2c4"; }

.fa-quote-left:before {
  content: "\f10d"; }

.fa-quote-right:before {
  content: "\f10e"; }

.fa-quran:before {
  content: "\f687"; }

.fa-r-project:before {
  content: "\f4f7"; }

.fa-radiation:before {
  content: "\f7b9"; }

.fa-radiation-alt:before {
  content: "\f7ba"; }

.fa-rainbow:before {
  content: "\f75b"; }

.fa-random:before {
  content: "\f074"; }

.fa-raspberry-pi:before {
  content: "\f7bb"; }

.fa-ravelry:before {
  content: "\f2d9"; }

.fa-react:before {
  content: "\f41b"; }

.fa-reacteurope:before {
  content: "\f75d"; }

.fa-readme:before {
  content: "\f4d5"; }

.fa-rebel:before {
  content: "\f1d0"; }

.fa-receipt:before {
  content: "\f543"; }

.fa-record-vinyl:before {
  content: "\f8d9"; }

.fa-recycle:before {
  content: "\f1b8"; }

.fa-red-river:before {
  content: "\f3e3"; }

.fa-reddit:before {
  content: "\f1a1"; }

.fa-reddit-alien:before {
  content: "\f281"; }

.fa-reddit-square:before {
  content: "\f1a2"; }

.fa-redhat:before {
  content: "\f7bc"; }

.fa-redo:before {
  content: "\f01e"; }

.fa-redo-alt:before {
  content: "\f2f9"; }

.fa-registered:before {
  content: "\f25d"; }

.fa-remove-format:before {
  content: "\f87d"; }

.fa-renren:before {
  content: "\f18b"; }

.fa-reply:before {
  content: "\f3e5"; }

.fa-reply-all:before {
  content: "\f122"; }

.fa-replyd:before {
  content: "\f3e6"; }

.fa-republican:before {
  content: "\f75e"; }

.fa-researchgate:before {
  content: "\f4f8"; }

.fa-resolving:before {
  content: "\f3e7"; }

.fa-restroom:before {
  content: "\f7bd"; }

.fa-retweet:before {
  content: "\f079"; }

.fa-rev:before {
  content: "\f5b2"; }

.fa-ribbon:before {
  content: "\f4d6"; }

.fa-ring:before {
  content: "\f70b"; }

.fa-road:before {
  content: "\f018"; }

.fa-robot:before {
  content: "\f544"; }

.fa-rocket:before {
  content: "\f135"; }

.fa-rocketchat:before {
  content: "\f3e8"; }

.fa-rockrms:before {
  content: "\f3e9"; }

.fa-route:before {
  content: "\f4d7"; }

.fa-rss:before {
  content: "\f09e"; }

.fa-rss-square:before {
  content: "\f143"; }

.fa-ruble-sign:before {
  content: "\f158"; }

.fa-ruler:before {
  content: "\f545"; }

.fa-ruler-combined:before {
  content: "\f546"; }

.fa-ruler-horizontal:before {
  content: "\f547"; }

.fa-ruler-vertical:before {
  content: "\f548"; }

.fa-running:before {
  content: "\f70c"; }

.fa-rupee-sign:before {
  content: "\f156"; }

.fa-sad-cry:before {
  content: "\f5b3"; }

.fa-sad-tear:before {
  content: "\f5b4"; }

.fa-safari:before {
  content: "\f267"; }

.fa-salesforce:before {
  content: "\f83b"; }

.fa-sass:before {
  content: "\f41e"; }

.fa-satellite:before {
  content: "\f7bf"; }

.fa-satellite-dish:before {
  content: "\f7c0"; }

.fa-save:before {
  content: "\f0c7"; }

.fa-schlix:before {
  content: "\f3ea"; }

.fa-school:before {
  content: "\f549"; }

.fa-screwdriver:before {
  content: "\f54a"; }

.fa-scribd:before {
  content: "\f28a"; }

.fa-scroll:before {
  content: "\f70e"; }

.fa-sd-card:before {
  content: "\f7c2"; }

.fa-search:before {
  content: "\f002"; }

.fa-search-dollar:before {
  content: "\f688"; }

.fa-search-location:before {
  content: "\f689"; }

.fa-search-minus:before {
  content: "\f010"; }

.fa-search-plus:before {
  content: "\f00e"; }

.fa-searchengin:before {
  content: "\f3eb"; }

.fa-seedling:before {
  content: "\f4d8"; }

.fa-sellcast:before {
  content: "\f2da"; }

.fa-sellsy:before {
  content: "\f213"; }

.fa-server:before {
  content: "\f233"; }

.fa-servicestack:before {
  content: "\f3ec"; }

.fa-shapes:before {
  content: "\f61f"; }

.fa-share:before {
  content: "\f064"; }

.fa-share-alt:before {
  content: "\f1e0"; }

.fa-share-alt-square:before {
  content: "\f1e1"; }

.fa-share-square:before {
  content: "\f14d"; }

.fa-shekel-sign:before {
  content: "\f20b"; }

.fa-shield-alt:before {
  content: "\f3ed"; }

.fa-ship:before {
  content: "\f21a"; }

.fa-shipping-fast:before {
  content: "\f48b"; }

.fa-shirtsinbulk:before {
  content: "\f214"; }

.fa-shoe-prints:before {
  content: "\f54b"; }

.fa-shopping-bag:before {
  content: "\f290"; }

.fa-shopping-basket:before {
  content: "\f291"; }

.fa-shopping-cart:before {
  content: "\f07a"; }

.fa-shopware:before {
  content: "\f5b5"; }

.fa-shower:before {
  content: "\f2cc"; }

.fa-shuttle-van:before {
  content: "\f5b6"; }

.fa-sign:before {
  content: "\f4d9"; }

.fa-sign-in-alt:before {
  content: "\f2f6"; }

.fa-sign-language:before {
  content: "\f2a7"; }

.fa-sign-out-alt:before {
  content: "\f2f5"; }

.fa-signal:before {
  content: "\f012"; }

.fa-signature:before {
  content: "\f5b7"; }

.fa-sim-card:before {
  content: "\f7c4"; }

.fa-simplybuilt:before {
  content: "\f215"; }

.fa-sistrix:before {
  content: "\f3ee"; }

.fa-sitemap:before {
  content: "\f0e8"; }

.fa-sith:before {
  content: "\f512"; }

.fa-skating:before {
  content: "\f7c5"; }

.fa-sketch:before {
  content: "\f7c6"; }

.fa-skiing:before {
  content: "\f7c9"; }

.fa-skiing-nordic:before {
  content: "\f7ca"; }

.fa-skull:before {
  content: "\f54c"; }

.fa-skull-crossbones:before {
  content: "\f714"; }

.fa-skyatlas:before {
  content: "\f216"; }

.fa-skype:before {
  content: "\f17e"; }

.fa-slack:before {
  content: "\f198"; }

.fa-slack-hash:before {
  content: "\f3ef"; }

.fa-slash:before {
  content: "\f715"; }

.fa-sleigh:before {
  content: "\f7cc"; }

.fa-heros-h:before {
  content: "\f1de"; }

.fa-slideshare:before {
  content: "\f1e7"; }

.fa-smile:before {
  content: "\f118"; }

.fa-smile-beam:before {
  content: "\f5b8"; }

.fa-smile-wink:before {
  content: "\f4da"; }

.fa-smog:before {
  content: "\f75f"; }

.fa-smoking:before {
  content: "\f48d"; }

.fa-smoking-ban:before {
  content: "\f54d"; }

.fa-sms:before {
  content: "\f7cd"; }

.fa-snapchat:before {
  content: "\f2ab"; }

.fa-snapchat-ghost:before {
  content: "\f2ac"; }

.fa-snapchat-square:before {
  content: "\f2ad"; }

.fa-snowboarding:before {
  content: "\f7ce"; }

.fa-snowflake:before {
  content: "\f2dc"; }

.fa-snowman:before {
  content: "\f7d0"; }

.fa-snowplow:before {
  content: "\f7d2"; }

.fa-socks:before {
  content: "\f696"; }

.fa-solar-panel:before {
  content: "\f5ba"; }

.fa-sort:before {
  content: "\f0dc"; }

.fa-sort-alpha-down:before {
  content: "\f15d"; }

.fa-sort-alpha-down-alt:before {
  content: "\f881"; }

.fa-sort-alpha-up:before {
  content: "\f15e"; }

.fa-sort-alpha-up-alt:before {
  content: "\f882"; }

.fa-sort-amount-down:before {
  content: "\f160"; }

.fa-sort-amount-down-alt:before {
  content: "\f884"; }

.fa-sort-amount-up:before {
  content: "\f161"; }

.fa-sort-amount-up-alt:before {
  content: "\f885"; }

.fa-sort-down:before {
  content: "\f0dd"; }

.fa-sort-numeric-down:before {
  content: "\f162"; }

.fa-sort-numeric-down-alt:before {
  content: "\f886"; }

.fa-sort-numeric-up:before {
  content: "\f163"; }

.fa-sort-numeric-up-alt:before {
  content: "\f887"; }

.fa-sort-up:before {
  content: "\f0de"; }

.fa-soundcloud:before {
  content: "\f1be"; }

.fa-sourcetree:before {
  content: "\f7d3"; }

.fa-spa:before {
  content: "\f5bb"; }

.fa-space-shuttle:before {
  content: "\f197"; }

.fa-speakap:before {
  content: "\f3f3"; }

.fa-speaker-deck:before {
  content: "\f83c"; }

.fa-spell-check:before {
  content: "\f891"; }

.fa-spider:before {
  content: "\f717"; }

.fa-spinner:before {
  content: "\f110"; }

.fa-splotch:before {
  content: "\f5bc"; }

.fa-spotify:before {
  content: "\f1bc"; }

.fa-spray-can:before {
  content: "\f5bd"; }

.fa-square:before {
  content: "\f0c8"; }

.fa-square-full:before {
  content: "\f45c"; }

.fa-square-root-alt:before {
  content: "\f698"; }

.fa-squarespace:before {
  content: "\f5be"; }

.fa-stack-exchange:before {
  content: "\f18d"; }

.fa-stack-overflow:before {
  content: "\f16c"; }

.fa-stackpath:before {
  content: "\f842"; }

.fa-stamp:before {
  content: "\f5bf"; }

.fa-star:before {
  content: "\f005"; }

.fa-star-and-crescent:before {
  content: "\f699"; }

.fa-star-half:before {
  content: "\f089"; }

.fa-star-half-alt:before {
  content: "\f5c0"; }

.fa-star-of-david:before {
  content: "\f69a"; }

.fa-star-of-life:before {
  content: "\f621"; }

.fa-staylinked:before {
  content: "\f3f5"; }

.fa-steam:before {
  content: "\f1b6"; }

.fa-steam-square:before {
  content: "\f1b7"; }

.fa-steam-symbol:before {
  content: "\f3f6"; }

.fa-step-backward:before {
  content: "\f048"; }

.fa-step-forward:before {
  content: "\f051"; }

.fa-stethoscope:before {
  content: "\f0f1"; }

.fa-sticker-mule:before {
  content: "\f3f7"; }

.fa-sticky-note:before {
  content: "\f249"; }

.fa-stop:before {
  content: "\f04d"; }

.fa-stop-circle:before {
  content: "\f28d"; }

.fa-stopwatch:before {
  content: "\f2f2"; }

.fa-store:before {
  content: "\f54e"; }

.fa-store-alt:before {
  content: "\f54f"; }

.fa-strava:before {
  content: "\f428"; }

.fa-stream:before {
  content: "\f550"; }

.fa-street-view:before {
  content: "\f21d"; }

.fa-strikethrough:before {
  content: "\f0cc"; }

.fa-stripe:before {
  content: "\f429"; }

.fa-stripe-s:before {
  content: "\f42a"; }

.fa-stroopwafel:before {
  content: "\f551"; }

.fa-studiovinari:before {
  content: "\f3f8"; }

.fa-stumbleupon:before {
  content: "\f1a4"; }

.fa-stumbleupon-circle:before {
  content: "\f1a3"; }

.fa-subscript:before {
  content: "\f12c"; }

.fa-subway:before {
  content: "\f239"; }

.fa-suitcase:before {
  content: "\f0f2"; }

.fa-suitcase-rolling:before {
  content: "\f5c1"; }

.fa-sun:before {
  content: "\f185"; }

.fa-superpowers:before {
  content: "\f2dd"; }

.fa-superscript:before {
  content: "\f12b"; }

.fa-supple:before {
  content: "\f3f9"; }

.fa-surprise:before {
  content: "\f5c2"; }

.fa-suse:before {
  content: "\f7d6"; }

.fa-swatchbook:before {
  content: "\f5c3"; }

.fa-swift:before {
  content: "\f8e1"; }

.fa-swimmer:before {
  content: "\f5c4"; }

.fa-swimming-pool:before {
  content: "\f5c5"; }

.fa-symfony:before {
  content: "\f83d"; }

.fa-synagogue:before {
  content: "\f69b"; }

.fa-sync:before {
  content: "\f021"; }

.fa-sync-alt:before {
  content: "\f2f1"; }

.fa-syringe:before {
  content: "\f48e"; }

.fa-table:before {
  content: "\f0ce"; }

.fa-table-tennis:before {
  content: "\f45d"; }

.fa-tablet:before {
  content: "\f10a"; }

.fa-tablet-alt:before {
  content: "\f3fa"; }

.fa-tablets:before {
  content: "\f490"; }

.fa-tachometer-alt:before {
  content: "\f3fd"; }

.fa-tag:before {
  content: "\f02b"; }

.fa-tags:before {
  content: "\f02c"; }

.fa-tape:before {
  content: "\f4db"; }

.fa-tasks:before {
  content: "\f0ae"; }

.fa-taxi:before {
  content: "\f1ba"; }

.fa-teamspeak:before {
  content: "\f4f9"; }

.fa-teeth:before {
  content: "\f62e"; }

.fa-teeth-open:before {
  content: "\f62f"; }

.fa-telegram:before {
  content: "\f2c6"; }

.fa-telegram-plane:before {
  content: "\f3fe"; }

.fa-temperature-high:before {
  content: "\f769"; }

.fa-temperature-low:before {
  content: "\f76b"; }

.fa-tencent-weibo:before {
  content: "\f1d5"; }

.fa-tenge:before {
  content: "\f7d7"; }

.fa-terminal:before {
  content: "\f120"; }

.fa-text-height:before {
  content: "\f034"; }

.fa-text-width:before {
  content: "\f035"; }

.fa-th:before {
  content: "\f00a"; }

.fa-th-large:before {
  content: "\f009"; }

.fa-th-list:before {
  content: "\f00b"; }

.fa-the-red-yeti:before {
  content: "\f69d"; }

.fa-theater-masks:before {
  content: "\f630"; }

.fa-themeco:before {
  content: "\f5c6"; }

.fa-themeisle:before {
  content: "\f2b2"; }

.fa-thermometer:before {
  content: "\f491"; }

.fa-thermometer-empty:before {
  content: "\f2cb"; }

.fa-thermometer-full:before {
  content: "\f2c7"; }

.fa-thermometer-half:before {
  content: "\f2c9"; }

.fa-thermometer-quarter:before {
  content: "\f2ca"; }

.fa-thermometer-three-quarters:before {
  content: "\f2c8"; }

.fa-think-peaks:before {
  content: "\f731"; }

.fa-thumbs-down:before {
  content: "\f165"; }

.fa-thumbs-up:before {
  content: "\f164"; }

.fa-thumbtack:before {
  content: "\f08d"; }

.fa-ticket-alt:before {
  content: "\f3ff"; }

.fa-times:before {
  content: "\f00d"; }

.fa-times-circle:before {
  content: "\f057"; }

.fa-tint:before {
  content: "\f043"; }

.fa-tint-slash:before {
  content: "\f5c7"; }

.fa-tired:before {
  content: "\f5c8"; }

.fa-toggle-off:before {
  content: "\f204"; }

.fa-toggle-on:before {
  content: "\f205"; }

.fa-toilet:before {
  content: "\f7d8"; }

.fa-toilet-paper:before {
  content: "\f71e"; }

.fa-toolbox:before {
  content: "\f552"; }

.fa-tools:before {
  content: "\f7d9"; }

.fa-tooth:before {
  content: "\f5c9"; }

.fa-torah:before {
  content: "\f6a0"; }

.fa-torii-gate:before {
  content: "\f6a1"; }

.fa-tractor:before {
  content: "\f722"; }

.fa-trade-federation:before {
  content: "\f513"; }

.fa-trademark:before {
  content: "\f25c"; }

.fa-traffic-light:before {
  content: "\f637"; }

.fa-trailer:before {
  content: "\f941"; }

.fa-train:before {
  content: "\f238"; }

.fa-tram:before {
  content: "\f7da"; }

.fa-transgender:before {
  content: "\f224"; }

.fa-transgender-alt:before {
  content: "\f225"; }

.fa-trash:before {
  content: "\f1f8"; }

.fa-trash-alt:before {
  content: "\f2ed"; }

.fa-trash-restore:before {
  content: "\f829"; }

.fa-trash-restore-alt:before {
  content: "\f82a"; }

.fa-tree:before {
  content: "\f1bb"; }

.fa-trello:before {
  content: "\f181"; }

.fa-tripadvisor:before {
  content: "\f262"; }

.fa-trophy:before {
  content: "\f091"; }

.fa-truck:before {
  content: "\f0d1"; }

.fa-truck-loading:before {
  content: "\f4de"; }

.fa-truck-monster:before {
  content: "\f63b"; }

.fa-truck-moving:before {
  content: "\f4df"; }

.fa-truck-pickup:before {
  content: "\f63c"; }

.fa-tshirt:before {
  content: "\f553"; }

.fa-tty:before {
  content: "\f1e4"; }

.fa-tumblr:before {
  content: "\f173"; }

.fa-tumblr-square:before {
  content: "\f174"; }

.fa-tv:before {
  content: "\f26c"; }

.fa-twitch:before {
  content: "\f1e8"; }

.fa-twitter:before {
  content: "\f099"; }

.fa-twitter-square:before {
  content: "\f081"; }

.fa-typo3:before {
  content: "\f42b"; }

.fa-uber:before {
  content: "\f402"; }

.fa-ubuntu:before {
  content: "\f7df"; }

.fa-uikit:before {
  content: "\f403"; }

.fa-umbraco:before {
  content: "\f8e8"; }

.fa-umbrella:before {
  content: "\f0e9"; }

.fa-umbrella-beach:before {
  content: "\f5ca"; }

.fa-underline:before {
  content: "\f0cd"; }

.fa-undo:before {
  content: "\f0e2"; }

.fa-undo-alt:before {
  content: "\f2ea"; }

.fa-uniregistry:before {
  content: "\f404"; }

.fa-unity:before {
  content: "\f949"; }

.fa-universal-access:before {
  content: "\f29a"; }

.fa-university:before {
  content: "\f19c"; }

.fa-unlink:before {
  content: "\f127"; }

.fa-unlock:before {
  content: "\f09c"; }

.fa-unlock-alt:before {
  content: "\f13e"; }

.fa-untappd:before {
  content: "\f405"; }

.fa-upload:before {
  content: "\f093"; }

.fa-ups:before {
  content: "\f7e0"; }

.fa-usb:before {
  content: "\f287"; }

.fa-user:before {
  content: "\f007"; }

.fa-user-alt:before {
  content: "\f406"; }

.fa-user-alt-slash:before {
  content: "\f4fa"; }

.fa-user-astronaut:before {
  content: "\f4fb"; }

.fa-user-check:before {
  content: "\f4fc"; }

.fa-user-circle:before {
  content: "\f2bd"; }

.fa-user-clock:before {
  content: "\f4fd"; }

.fa-user-cog:before {
  content: "\f4fe"; }

.fa-user-edit:before {
  content: "\f4ff"; }

.fa-user-friends:before {
  content: "\f500"; }

.fa-user-graduate:before {
  content: "\f501"; }

.fa-user-injured:before {
  content: "\f728"; }

.fa-user-lock:before {
  content: "\f502"; }

.fa-user-md:before {
  content: "\f0f0"; }

.fa-user-minus:before {
  content: "\f503"; }

.fa-user-ninja:before {
  content: "\f504"; }

.fa-user-nurse:before {
  content: "\f82f"; }

.fa-user-plus:before {
  content: "\f234"; }

.fa-user-secret:before {
  content: "\f21b"; }

.fa-user-shield:before {
  content: "\f505"; }

.fa-user-slash:before {
  content: "\f506"; }

.fa-user-tag:before {
  content: "\f507"; }

.fa-user-tie:before {
  content: "\f508"; }

.fa-user-times:before {
  content: "\f235"; }

.fa-users:before {
  content: "\f0c0"; }

.fa-users-cog:before {
  content: "\f509"; }

.fa-usps:before {
  content: "\f7e1"; }

.fa-ussunnah:before {
  content: "\f407"; }

.fa-utensil-spoon:before {
  content: "\f2e5"; }

.fa-utensils:before {
  content: "\f2e7"; }

.fa-vaadin:before {
  content: "\f408"; }

.fa-vector-square:before {
  content: "\f5cb"; }

.fa-venus:before {
  content: "\f221"; }

.fa-venus-double:before {
  content: "\f226"; }

.fa-venus-mars:before {
  content: "\f228"; }

.fa-viacoin:before {
  content: "\f237"; }

.fa-viadeo:before {
  content: "\f2a9"; }

.fa-viadeo-square:before {
  content: "\f2aa"; }

.fa-vial:before {
  content: "\f492"; }

.fa-vials:before {
  content: "\f493"; }

.fa-viber:before {
  content: "\f409"; }

.fa-video:before {
  content: "\f03d"; }

.fa-video-slash:before {
  content: "\f4e2"; }

.fa-vihara:before {
  content: "\f6a7"; }

.fa-vimeo:before {
  content: "\f40a"; }

.fa-vimeo-square:before {
  content: "\f194"; }

.fa-vimeo-v:before {
  content: "\f27d"; }

.fa-vine:before {
  content: "\f1ca"; }

.fa-vk:before {
  content: "\f189"; }

.fa-vnv:before {
  content: "\f40b"; }

.fa-voicemail:before {
  content: "\f897"; }

.fa-volleyball-ball:before {
  content: "\f45f"; }

.fa-volume-down:before {
  content: "\f027"; }

.fa-volume-mute:before {
  content: "\f6a9"; }

.fa-volume-off:before {
  content: "\f026"; }

.fa-volume-up:before {
  content: "\f028"; }

.fa-vote-yea:before {
  content: "\f772"; }

.fa-vr-cardboard:before {
  content: "\f729"; }

.fa-vuejs:before {
  content: "\f41f"; }

.fa-walking:before {
  content: "\f554"; }

.fa-wallet:before {
  content: "\f555"; }

.fa-warehouse:before {
  content: "\f494"; }

.fa-water:before {
  content: "\f773"; }

.fa-wave-square:before {
  content: "\f83e"; }

.fa-waze:before {
  content: "\f83f"; }

.fa-weebly:before {
  content: "\f5cc"; }

.fa-weibo:before {
  content: "\f18a"; }

.fa-weight:before {
  content: "\f496"; }

.fa-weight-hanging:before {
  content: "\f5cd"; }

.fa-weixin:before {
  content: "\f1d7"; }

.fa-whatsapp:before {
  content: "\f232"; }

.fa-whatsapp-square:before {
  content: "\f40c"; }

.fa-wheelchair:before {
  content: "\f193"; }

.fa-whmcs:before {
  content: "\f40d"; }

.fa-wifi:before {
  content: "\f1eb"; }

.fa-wikipedia-w:before {
  content: "\f266"; }

.fa-wind:before {
  content: "\f72e"; }

.fa-window-close:before {
  content: "\f410"; }

.fa-window-maximize:before {
  content: "\f2d0"; }

.fa-window-minimize:before {
  content: "\f2d1"; }

.fa-window-restore:before {
  content: "\f2d2"; }

.fa-windows:before {
  content: "\f17a"; }

.fa-wine-bottle:before {
  content: "\f72f"; }

.fa-wine-glass:before {
  content: "\f4e3"; }

.fa-wine-glass-alt:before {
  content: "\f5ce"; }

.fa-wix:before {
  content: "\f5cf"; }

.fa-wizards-of-the-coast:before {
  content: "\f730"; }

.fa-wolf-pack-battalion:before {
  content: "\f514"; }

.fa-won-sign:before {
  content: "\f159"; }

.fa-wordpress:before {
  content: "\f19a"; }

.fa-wordpress-simple:before {
  content: "\f411"; }

.fa-wpbeginner:before {
  content: "\f297"; }

.fa-wpexplorer:before {
  content: "\f2de"; }

.fa-wpforms:before {
  content: "\f298"; }

.fa-wpressr:before {
  content: "\f3e4"; }

.fa-wrench:before {
  content: "\f0ad"; }

.fa-x-ray:before {
  content: "\f497"; }

.fa-xbox:before {
  content: "\f412"; }

.fa-xing:before {
  content: "\f168"; }

.fa-xing-square:before {
  content: "\f169"; }

.fa-y-combinator:before {
  content: "\f23b"; }

.fa-yahoo:before {
  content: "\f19e"; }

.fa-yammer:before {
  content: "\f840"; }

.fa-yandex:before {
  content: "\f413"; }

.fa-yandex-international:before {
  content: "\f414"; }

.fa-yarn:before {
  content: "\f7e3"; }

.fa-yelp:before {
  content: "\f1e9"; }

.fa-yen-sign:before {
  content: "\f157"; }

.fa-yin-yang:before {
  content: "\f6ad"; }

.fa-yoast:before {
  content: "\f2b1"; }

.fa-youtube:before {
  content: "\f167"; }

.fa-youtube-square:before {
  content: "\f431"; }

.fa-zhihu:before {
  content: "\f63f"; }

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto; }
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-style: normal;
  font-weight: normal;
  font-display: auto;
  src: url("../fonts/fa-brands-400.eot");
  src: url("../fonts/fa-brands-400.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-brands-400.woff2") format("woff2"), url("../fonts/fa-brands-400.woff") format("woff"), url("../fonts/fa-brands-400.ttf") format("truetype"), url("../fonts/fa-brands-400.svg#fontawesome") format("svg"); }

.fab {
  font-family: 'Font Awesome 5 Brands'; }
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 400;
  font-display: auto;
  src: url("../fonts/fa-regular-400.eot");
  src: url("../fonts/fa-regular-400.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-regular-400.woff2") format("woff2"), url("../fonts/fa-regular-400.woff") format("woff"), url("../fonts/fa-regular-400.ttf") format("truetype"), url("../fonts/fa-regular-400.svg#fontawesome") format("svg"); }

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400; }
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  font-display: auto;
  src: url("../fonts/fa-solid-900.eot");
  src: url("../fonts/fa-solid-900.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-solid-900.woff2") format("woff2"), url("../fonts/fa-solid-900.woff") format("woff"), url("../fonts/fa-solid-900.ttf") format("truetype"), url("../fonts/fa-solid-900.svg#fontawesome") format("svg"); }

.fa,
.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900; }

@charset "UTF-8";@font-face{font-family:ytpregular;src:url(font/ytp-regular.eot)}@font-face{font-family:ytpregular;src:url(data:application/x-font-woff;charset=utf-8;base64,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)format('woff'),url(font/ytp-regular.ttf)format('truetype');font-weight:400;font-style:normal}.mb_YTPlayer:focus{outline:0}.YTPWrapper{display:block;transform:translateZ(0)translate3d(0,0,0);transform-style:preserve-3d;perspective:1000;-webkit-backface-visibility:hidden;backface-visibility:hidden;box-sizing:border-box}.mb_YTPlayer .loading{position:absolute;top:10px;right:10px;font-size:12px;color:#fff;background:rgba(0,0,0,.51);text-align:center;padding:2px 4px;border-radius:5px;font-family:"Droid Sans",sans-serif;-webkit-animation:fade .1s infinite alternate;animation:fade .1s infinite alternate}@-webkit-keyframes fade{0%{opacity:.5}100%{opacity:1}}@keyframes fade{0%{opacity:.5}100%{opacity:1}}.YTPFullscreen{display:block!important;position:fixed!important;width:100%!important;height:100%!important;top:0!important;left:0!important;margin:0!important;border:none!important;opacity:1!important;background-color:#000!important;padding:0!important}.inlinePlayButton{font-family:ytpregular;font-size:45px;color:#fff;text-align:center;border-radius:100%;background-color:rgba(0,0,0,.4);position:absolute;width:80px;height:80px;line-height:80px;top:0;bottom:0;left:0;right:0;margin:auto;cursor:pointer;opacity:.8;transition:all .4s;transform:scale(1.3)}.mb_YTPlayer:hover .inlinePlayButton{opacity:1;transform:scale(1)}.inlinePlayButton:hover{background-color:rgba(0,0,0,1);transform:scale(1.3)!important}.mbYTP_wrapper iframe{max-width:8000px!important}.inline_YTPlayer{margin-bottom:20px;vertical-align:top;position:relative;left:0;overflow:hidden;border-radius:4px;box-shadow:0 0 5px rgba(0,0,0,.7);background:rgba(0,0,0,.5)}.inline_YTPlayer img{border:none!important;margin:0!important;padding:0!important;transform:none!important}.mb_YTPBar,.mb_YTPBar .buttonBar{box-sizing:border-box;left:0;padding:5px;width:100%}.mb_YTPBar .ytpicon{font-size:20px;font-family:ytpregular}.mb_YTPBar .mb_YTPUrl.ytpicon{font-size:30px}.mb_YTPBar{transition:opacity .5s;display:block;height:10px;background:#333;position:fixed;bottom:0;text-align:left;z-index:1000;font:14px/16px sans-serif;color:#fff;opacity:.1}.mb_YTPBar.visible,.mb_YTPBar:hover{opacity:1}.mb_YTPBar .buttonBar{transition:all .5s;background:0 0;font:12px/14px san-serif;position:absolute;top:-30px;height:40px}.mb_YTPBar:hover .buttonBar{background:rgba(0,0,0,.4)}.mb_YTPBar span{display:inline-block;font:16px/20px sans-serif;position:relative;width:30px;height:25px;vertical-align:middle}.mb_YTPBar span.mb_YTPTime{width:130px}.mb_YTPBar span.mb_OnlyYT,.mb_YTPBar span.mb_YTPUrl{position:absolute;width:auto;display:block;top:6px;right:10px;cursor:pointer}.mb_YTPBar span.mb_YTPUrl img{width:60px}.mb_YTPBar span.mb_OnlyYT{left:300px;right:auto}.mb_YTPBar span.mb_OnlyYT img{width:25px}.mb_YTPBar span.mb_YTPUrl a{color:#fff}.mb_YTPBar .mb_YTPMuteUnmute,.mb_YTPBar .mb_YTPPlayPause,.mb_YTPlayer .mb_YTPBar .mb_YTPPlayPause img{cursor:pointer}.mb_YTPBar .mb_YTPProgress{height:10px;width:100%;background:#222;bottom:0;left:0}.mb_YTPBar .mb_YTPLoaded{height:10px;width:0;background:#444;left:0}.mb_YTPBar .mb_YTPseekbar{height:10px;width:0;background:#bb110e;bottom:0;left:0;box-shadow:rgba(82,82,82,.47)1px 1px 3px}.mb_YTPBar .YTPOverlay{backface-visibility:hidden;-webkit-backface-visibility:hidden;-webkit-transform-style:"flat";box-sizing:border-box}.YTPOverlay.raster{background:url(images/raster.png)}.YTPOverlay.raster.retina{background:url(images/<EMAIL>)}.YTPOverlay.raster-dot{background:url(images/raster_dot.png)}.YTPOverlay.raster-dot.retina{background:url(images/<EMAIL>)}.mb_YTPBar .simpleSlider{position:relative;width:100px;height:10px;border:1px solid #fff;overflow:hidden;box-sizing:border-box;margin-right:10px;cursor:pointer!important;border-radius:3px}.mb_YTPBar.compact .simpleSlider{width:40px}.mb_YTPBar.compact span.mb_OnlyYT{left:260px}.mb_YTPBar .simpleSlider.muted{opacity:.3}.mb_YTPBar .level{position:absolute;left:0;bottom:0;background-color:#fff;box-sizing:border-box}.mb_YTPBar .level.horizontal{height:100%;width:0}.mb_YTPBar .level.vertical{height:auto;width:100%}
@font-face {
	font-family: 'Linearicons-Free';
	src:url('../fonts/Linearicons-Free.eot?w118d');
	src:url('../fonts/Linearicons-Free.eot?#iefixw118d') format('embedded-opentype'),
		url('../fonts/Linearicons-Free.woff2?w118d') format('woff2'),
		url('../fonts/Linearicons-Free.woff?w118d') format('woff'),
		url('../fonts/Linearicons-Free.ttf?w118d') format('truetype'),
		url('../fonts/Linearicons-Free.svg?w118d#Linearicons-Free') format('svg');
	font-weight: normal;
	font-style: normal;
}

.lnr {
	font-family: 'Linearicons-Free';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.lnr-home:before {
	content: "\e800";
}
.lnr-apartment:before {
	content: "\e801";
}
.lnr-pencil:before {
	content: "\e802";
}
.lnr-magic-wand:before {
	content: "\e803";
}
.lnr-drop:before {
	content: "\e804";
}
.lnr-lighter:before {
	content: "\e805";
}
.lnr-poop:before {
	content: "\e806";
}
.lnr-sun:before {
	content: "\e807";
}
.lnr-moon:before {
	content: "\e808";
}
.lnr-cloud:before {
	content: "\e809";
}
.lnr-cloud-upload:before {
	content: "\e80a";
}
.lnr-cloud-download:before {
	content: "\e80b";
}
.lnr-cloud-sync:before {
	content: "\e80c";
}
.lnr-cloud-check:before {
	content: "\e80d";
}
.lnr-database:before {
	content: "\e80e";
}
.lnr-lock:before {
	content: "\e80f";
}
.lnr-cog:before {
	content: "\e810";
}
.lnr-trash:before {
	content: "\e811";
}
.lnr-dice:before {
	content: "\e812";
}
.lnr-heart:before {
	content: "\e813";
}
.lnr-star:before {
	content: "\e814";
}
.lnr-star-half:before {
	content: "\e815";
}
.lnr-star-empty:before {
	content: "\e816";
}
.lnr-flag:before {
	content: "\e817";
}
.lnr-envelope:before {
	content: "\e818";
}
.lnr-paperclip:before {
	content: "\e819";
}
.lnr-inbox:before {
	content: "\e81a";
}
.lnr-eye:before {
	content: "\e81b";
}
.lnr-printer:before {
	content: "\e81c";
}
.lnr-file-empty:before {
	content: "\e81d";
}
.lnr-file-add:before {
	content: "\e81e";
}
.lnr-enter:before {
	content: "\e81f";
}
.lnr-exit:before {
	content: "\e820";
}
.lnr-graduation-hat:before {
	content: "\e821";
}
.lnr-license:before {
	content: "\e822";
}
.lnr-music-note:before {
	content: "\e823";
}
.lnr-film-play:before {
	content: "\e824";
}
.lnr-camera-video:before {
	content: "\e825";
}
.lnr-camera:before {
	content: "\e826";
}
.lnr-picture:before {
	content: "\e827";
}
.lnr-book:before {
	content: "\e828";
}
.lnr-bookmark:before {
	content: "\e829";
}
.lnr-user:before {
	content: "\e82a";
}
.lnr-users:before {
	content: "\e82b";
}
.lnr-shirt:before {
	content: "\e82c";
}
.lnr-store:before {
	content: "\e82d";
}
.lnr-cart:before {
	content: "\e82e";
}
.lnr-tag:before {
	content: "\e82f";
}
.lnr-phone-handset:before {
	content: "\e830";
}
.lnr-phone:before {
	content: "\e831";
}
.lnr-pushpin:before {
	content: "\e832";
}
.lnr-map-marker:before {
	content: "\e833";
}
.lnr-map:before {
	content: "\e834";
}
.lnr-location:before {
	content: "\e835";
}
.lnr-calendar-full:before {
	content: "\e836";
}
.lnr-keyboard:before {
	content: "\e837";
}
.lnr-spell-check:before {
	content: "\e838";
}
.lnr-screen:before {
	content: "\e839";
}
.lnr-smartphone:before {
	content: "\e83a";
}
.lnr-tablet:before {
	content: "\e83b";
}
.lnr-laptop:before {
	content: "\e83c";
}
.lnr-laptop-phone:before {
	content: "\e83d";
}
.lnr-power-switch:before {
	content: "\e83e";
}
.lnr-bubble:before {
	content: "\e83f";
}
.lnr-heart-pulse:before {
	content: "\e840";
}
.lnr-construction:before {
	content: "\e841";
}
.lnr-pie-chart:before {
	content: "\e842";
}
.lnr-chart-bars:before {
	content: "\e843";
}
.lnr-gift:before {
	content: "\e844";
}
.lnr-diamond:before {
	content: "\e845";
}
.lnr-linearicons:before {
	content: "\e846";
}
.lnr-dinner:before {
	content: "\e847";
}
.lnr-coffee-cup:before {
	content: "\e848";
}
.lnr-leaf:before {
	content: "\e849";
}
.lnr-paw:before {
	content: "\e84a";
}
.lnr-rocket:before {
	content: "\e84b";
}
.lnr-briefcase:before {
	content: "\e84c";
}
.lnr-bus:before {
	content: "\e84d";
}
.lnr-car:before {
	content: "\e84e";
}
.lnr-train:before {
	content: "\e84f";
}
.lnr-bicycle:before {
	content: "\e850";
}
.lnr-wheelchair:before {
	content: "\e851";
}
.lnr-select:before {
	content: "\e852";
}
.lnr-earth:before {
	content: "\e853";
}
.lnr-smile:before {
	content: "\e854";
}
.lnr-sad:before {
	content: "\e855";
}
.lnr-neutral:before {
	content: "\e856";
}
.lnr-mustache:before {
	content: "\e857";
}
.lnr-alarm:before {
	content: "\e858";
}
.lnr-bullhorn:before {
	content: "\e859";
}
.lnr-volume-high:before {
	content: "\e85a";
}
.lnr-volume-medium:before {
	content: "\e85b";
}
.lnr-volume-low:before {
	content: "\e85c";
}
.lnr-volume:before {
	content: "\e85d";
}
.lnr-mic:before {
	content: "\e85e";
}
.lnr-hourglass:before {
	content: "\e85f";
}
.lnr-undo:before {
	content: "\e860";
}
.lnr-redo:before {
	content: "\e861";
}
.lnr-sync:before {
	content: "\e862";
}
.lnr-history:before {
	content: "\e863";
}
.lnr-clock:before {
	content: "\e864";
}
.lnr-download:before {
	content: "\e865";
}
.lnr-upload:before {
	content: "\e866";
}
.lnr-enter-down:before {
	content: "\e867";
}
.lnr-exit-up:before {
	content: "\e868";
}
.lnr-bug:before {
	content: "\e869";
}
.lnr-code:before {
	content: "\e86a";
}
.lnr-link:before {
	content: "\e86b";
}
.lnr-unlink:before {
	content: "\e86c";
}
.lnr-thumbs-up:before {
	content: "\e86d";
}
.lnr-thumbs-down:before {
	content: "\e86e";
}
.lnr-magnifier:before {
	content: "\e86f";
}
.lnr-cross:before {
	content: "\e870";
}
.lnr-menu:before {
	content: "\e871";
}
.lnr-list:before {
	content: "\e872";
}
.lnr-chevron-up:before {
	content: "\e873";
}
.lnr-chevron-down:before {
	content: "\e874";
}
.lnr-chevron-left:before {
	content: "\e875";
}
.lnr-chevron-right:before {
	content: "\e876";
}
.lnr-arrow-up:before {
	content: "\e877";
}
.lnr-arrow-down:before {
	content: "\e878";
}
.lnr-arrow-left:before {
	content: "\e879";
}
.lnr-arrow-right:before {
	content: "\e87a";
}
.lnr-move:before {
	content: "\e87b";
}
.lnr-warning:before {
	content: "\e87c";
}
.lnr-question-circle:before {
	content: "\e87d";
}
.lnr-menu-circle:before {
	content: "\e87e";
}
.lnr-checkmark-circle:before {
	content: "\e87f";
}
.lnr-cross-circle:before {
	content: "\e880";
}
.lnr-plus-circle:before {
	content: "\e881";
}
.lnr-circle-minus:before {
	content: "\e882";
}
.lnr-arrow-up-circle:before {
	content: "\e883";
}
.lnr-arrow-down-circle:before {
	content: "\e884";
}
.lnr-arrow-left-circle:before {
	content: "\e885";
}
.lnr-arrow-right-circle:before {
	content: "\e886";
}
.lnr-chevron-up-circle:before {
	content: "\e887";
}
.lnr-chevron-down-circle:before {
	content: "\e888";
}
.lnr-chevron-left-circle:before {
	content: "\e889";
}
.lnr-chevron-right-circle:before {
	content: "\e88a";
}
.lnr-crop:before {
	content: "\e88b";
}
.lnr-frame-expand:before {
	content: "\e88c";
}
.lnr-frame-contract:before {
	content: "\e88d";
}
.lnr-layers:before {
	content: "\e88e";
}
.lnr-funnel:before {
	content: "\e88f";
}
.lnr-text-format:before {
	content: "\e890";
}
.lnr-text-format-remove:before {
	content: "\e891";
}
.lnr-text-size:before {
	content: "\e892";
}
.lnr-bold:before {
	content: "\e893";
}
.lnr-italic:before {
	content: "\e894";
}
.lnr-underline:before {
	content: "\e895";
}
.lnr-strikethrough:before {
	content: "\e896";
}
.lnr-highlight:before {
	content: "\e897";
}
.lnr-text-align-left:before {
	content: "\e898";
}
.lnr-text-align-center:before {
	content: "\e899";
}
.lnr-text-align-right:before {
	content: "\e89a";
}
.lnr-text-align-justify:before {
	content: "\e89b";
}
.lnr-line-spacing:before {
	content: "\e89c";
}
.lnr-indent-increase:before {
	content: "\e89d";
}
.lnr-indent-decrease:before {
	content: "\e89e";
}
.lnr-pilcrow:before {
	content: "\e89f";
}
.lnr-direction-ltr:before {
	content: "\e8a0";
}
.lnr-direction-rtl:before {
	content: "\e8a1";
}
.lnr-page-break:before {
	content: "\e8a2";
}
.lnr-sort-alpha-asc:before {
	content: "\e8a3";
}
.lnr-sort-amount-asc:before {
	content: "\e8a4";
}
.lnr-hand:before {
	content: "\e8a5";
}
.lnr-pointer-up:before {
	content: "\e8a6";
}
.lnr-pointer-right:before {
	content: "\e8a7";
}
.lnr-pointer-down:before {
	content: "\e8a8";
}
.lnr-pointer-left:before {
	content: "\e8a9";
}

/**
 * Owl Carousel v2.3.4
 * Copyright 2013-2018 David Deutsch
 * Licensed under: SEE LICENSE IN https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE
 */
.owl-carousel,.owl-carousel .owl-item{-webkit-tap-highlight-color:transparent;position:relative}.owl-carousel{display:none;width:100%;z-index:1}.owl-carousel .owl-stage{position:relative;-ms-touch-action:pan-Y;touch-action:manipulation;-moz-backface-visibility:hidden}.owl-carousel .owl-stage:after{content:".";display:block;clear:both;visibility:hidden;line-height:0;height:0}.owl-carousel .owl-stage-outer{position:relative;overflow:hidden;-webkit-transform:translate3d(0,0,0)}.owl-carousel .owl-item,.owl-carousel .owl-wrapper{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;-ms-backface-visibility:hidden;-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0)}.owl-carousel .owl-item{min-height:1px;float:left;-webkit-backface-visibility:hidden;-webkit-touch-callout:none}.owl-carousel .owl-item img{display:block;width:100%}.owl-carousel .owl-dots.disabled,.owl-carousel .owl-nav.disabled{display:none}.no-js .owl-carousel,.owl-carousel.owl-loaded{display:block}.owl-carousel .owl-dot,.owl-carousel .owl-nav .owl-next,.owl-carousel .owl-nav .owl-prev{cursor:pointer;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.owl-carousel .owl-nav button.owl-next,.owl-carousel .owl-nav button.owl-prev,.owl-carousel button.owl-dot{background:0 0;color:inherit;border:none;padding:0!important;font:inherit}.owl-carousel.owl-loading{opacity:0;display:block}.owl-carousel.owl-hidden{opacity:0}.owl-carousel.owl-refresh .owl-item{visibility:hidden}.owl-carousel.owl-drag .owl-item{-ms-touch-action:pan-y;touch-action:pan-y;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.owl-carousel.owl-grab{cursor:move;cursor:grab}.owl-carousel.owl-rtl{direction:rtl}.owl-carousel.owl-rtl .owl-item{float:right}.owl-carousel .animated{animation-duration:1s;animation-fill-mode:both}.owl-carousel .owl-animated-in{z-index:0}.owl-carousel .owl-animated-out{z-index:1}.owl-carousel .fadeOut{animation-name:fadeOut}@keyframes fadeOut{0%{opacity:1}100%{opacity:0}}.owl-height{transition:height .5s ease-in-out}.owl-carousel .owl-item .owl-lazy{opacity:0;transition:opacity .4s ease}.owl-carousel .owl-item .owl-lazy:not([src]),.owl-carousel .owl-item .owl-lazy[src^=""]{max-height:0}.owl-carousel .owl-item img.owl-lazy{transform-style:preserve-3d}.owl-carousel .owl-video-wrapper{position:relative;height:100%;background:#000}.owl-carousel .owl-video-play-icon{position:absolute;height:80px;width:80px;left:50%;top:50%;margin-left:-40px;margin-top:-40px;background:url(owl.video.play.png) no-repeat;cursor:pointer;z-index:1;-webkit-backface-visibility:hidden;transition:transform .1s ease}.owl-carousel .owl-video-play-icon:hover{-ms-transform:scale(1.3,1.3);transform:scale(1.3,1.3)}.owl-carousel .owl-video-playing .owl-video-play-icon,.owl-carousel .owl-video-playing .owl-video-tn{display:none}.owl-carousel .owl-video-tn{opacity:0;height:100%;background-position:center center;background-repeat:no-repeat;background-size:contain;transition:opacity .4s ease}.owl-carousel .owl-video-frame{position:relative;z-index:1;height:100%;width:100%}
/**
 * Owl Carousel v2.3.4
 * Copyright 2013-2018 David Deutsch
 * Licensed under: SEE LICENSE IN https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE
 */
.owl-theme .owl-dots,.owl-theme .owl-nav{text-align:center;-webkit-tap-highlight-color:transparent}.owl-theme .owl-nav{margin-top:10px}.owl-theme .owl-nav [class*=owl-]{color:#FFF;font-size:14px;margin:5px;padding:4px 7px;background:#D6D6D6;display:inline-block;cursor:pointer;border-radius:3px}.owl-theme .owl-nav [class*=owl-]:hover{background:#869791;color:#FFF;text-decoration:none}.owl-theme .owl-nav .disabled{opacity:.5;cursor:default}.owl-theme .owl-nav.disabled+.owl-dots{margin-top:10px}.owl-theme .owl-dots .owl-dot{display:inline-block;zoom:1}.owl-theme .owl-dots .owl-dot span{width:10px;height:10px;margin:5px 7px;background:#D6D6D6;display:block;-webkit-backface-visibility:visible;transition:opacity .2s ease;border-radius:30px}.owl-theme .owl-dots .owl-dot.active span,.owl-theme .owl-dots .owl-dot:hover span{background:#869791}
@font-face {
	font-family: 'Stroke-Gap-Icons';
	src: url('fonts/Stroke-Gap-Icons.eot');
}
@font-face {
	font-family: 'Stroke-Gap-Icons';
	src: url(data:application/x-font-ttf;charset=utf-8;base64,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) format('truetype'),
		 url(data:application/font-woff;charset=utf-8;base64,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) format('woff');
	font-weight: normal;
	font-style: normal;
}

.icon {
	font-family: 'Stroke-Gap-Icons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-WorldWide:before {
	content: "\e600";
}
.icon-WorldGlobe:before {
	content: "\e601";
}
.icon-Underpants:before {
	content: "\e602";
}
.icon-Tshirt:before {
	content: "\e603";
}
.icon-Trousers:before {
	content: "\e604";
}
.icon-Tie:before {
	content: "\e605";
}
.icon-TennisBall:before {
	content: "\e606";
}
.icon-Telesocpe:before {
	content: "\e607";
}
.icon-Stop:before {
	content: "\e608";
}
.icon-Starship:before {
	content: "\e609";
}
.icon-Starship2:before {
	content: "\e60a";
}
.icon-Speaker:before {
	content: "\e60b";
}
.icon-Speaker2:before {
	content: "\e60c";
}
.icon-Soccer:before {
	content: "\e60d";
}
.icon-Snikers:before {
	content: "\e60e";
}
.icon-Scisors:before {
	content: "\e60f";
}
.icon-Puzzle:before {
	content: "\e610";
}
.icon-Printer:before {
	content: "\e611";
}
.icon-Pool:before {
	content: "\e612";
}
.icon-Podium:before {
	content: "\e613";
}
.icon-Play:before {
	content: "\e614";
}
.icon-Planet:before {
	content: "\e615";
}
.icon-Pause:before {
	content: "\e616";
}
.icon-Next:before {
	content: "\e617";
}
.icon-MusicNote2:before {
	content: "\e618";
}
.icon-MusicNote:before {
	content: "\e619";
}
.icon-MusicMixer:before {
	content: "\e61a";
}
.icon-Microphone:before {
	content: "\e61b";
}
.icon-Medal:before {
	content: "\e61c";
}
.icon-ManFigure:before {
	content: "\e61d";
}
.icon-Magnet:before {
	content: "\e61e";
}
.icon-Like:before {
	content: "\e61f";
}
.icon-Hanger:before {
	content: "\e620";
}
.icon-Handicap:before {
	content: "\e621";
}
.icon-Forward:before {
	content: "\e622";
}
.icon-Footbal:before {
	content: "\e623";
}
.icon-Flag:before {
	content: "\e624";
}
.icon-FemaleFigure:before {
	content: "\e625";
}
.icon-Dislike:before {
	content: "\e626";
}
.icon-DiamondRing:before {
	content: "\e627";
}
.icon-Cup:before {
	content: "\e628";
}
.icon-Crown:before {
	content: "\e629";
}
.icon-Column:before {
	content: "\e62a";
}
.icon-Click:before {
	content: "\e62b";
}
.icon-Cassette:before {
	content: "\e62c";
}
.icon-Bomb:before {
	content: "\e62d";
}
.icon-BatteryLow:before {
	content: "\e62e";
}
.icon-BatteryFull:before {
	content: "\e62f";
}
.icon-Bascketball:before {
	content: "\e630";
}
.icon-Astronaut:before {
	content: "\e631";
}
.icon-WineGlass:before {
	content: "\e632";
}
.icon-Water:before {
	content: "\e633";
}
.icon-Wallet:before {
	content: "\e634";
}
.icon-Umbrella:before {
	content: "\e635";
}
.icon-TV:before {
	content: "\e636";
}
.icon-TeaMug:before {
	content: "\e637";
}
.icon-Tablet:before {
	content: "\e638";
}
.icon-Soda:before {
	content: "\e639";
}
.icon-SodaCan:before {
	content: "\e63a";
}
.icon-SimCard:before {
	content: "\e63b";
}
.icon-Signal:before {
	content: "\e63c";
}
.icon-Shaker:before {
	content: "\e63d";
}
.icon-Radio:before {
	content: "\e63e";
}
.icon-Pizza:before {
	content: "\e63f";
}
.icon-Phone:before {
	content: "\e640";
}
.icon-Notebook:before {
	content: "\e641";
}
.icon-Mug:before {
	content: "\e642";
}
.icon-Mastercard:before {
	content: "\e643";
}
.icon-Ipod:before {
	content: "\e644";
}
.icon-Info:before {
	content: "\e645";
}
.icon-Icecream2:before {
	content: "\e646";
}
.icon-Icecream1:before {
	content: "\e647";
}
.icon-Hourglass:before {
	content: "\e648";
}
.icon-Help:before {
	content: "\e649";
}
.icon-Goto:before {
	content: "\e64a";
}
.icon-Glasses:before {
	content: "\e64b";
}
.icon-Gameboy:before {
	content: "\e64c";
}
.icon-ForkandKnife:before {
	content: "\e64d";
}
.icon-Export:before {
	content: "\e64e";
}
.icon-Exit:before {
	content: "\e64f";
}
.icon-Espresso:before {
	content: "\e650";
}
.icon-Drop:before {
	content: "\e651";
}
.icon-Download:before {
	content: "\e652";
}
.icon-Dollars:before {
	content: "\e653";
}
.icon-Dollar:before {
	content: "\e654";
}
.icon-DesktopMonitor:before {
	content: "\e655";
}
.icon-Corkscrew:before {
	content: "\e656";
}
.icon-CoffeeToGo:before {
	content: "\e657";
}
.icon-Chart:before {
	content: "\e658";
}
.icon-ChartUp:before {
	content: "\e659";
}
.icon-ChartDown:before {
	content: "\e65a";
}
.icon-Calculator:before {
	content: "\e65b";
}
.icon-Bread:before {
	content: "\e65c";
}
.icon-Bourbon:before {
	content: "\e65d";
}
.icon-BottleofWIne:before {
	content: "\e65e";
}
.icon-Bag:before {
	content: "\e65f";
}
.icon-Arrow:before {
	content: "\e660";
}
.icon-Antenna2:before {
	content: "\e661";
}
.icon-Antenna1:before {
	content: "\e662";
}
.icon-Anchor:before {
	content: "\e663";
}
.icon-Wheelbarrow:before {
	content: "\e664";
}
.icon-Webcam:before {
	content: "\e665";
}
.icon-Unlinked:before {
	content: "\e666";
}
.icon-Truck:before {
	content: "\e667";
}
.icon-Timer:before {
	content: "\e668";
}
.icon-Time:before {
	content: "\e669";
}
.icon-StorageBox:before {
	content: "\e66a";
}
.icon-Star:before {
	content: "\e66b";
}
.icon-ShoppingCart:before {
	content: "\e66c";
}
.icon-Shield:before {
	content: "\e66d";
}
.icon-Seringe:before {
	content: "\e66e";
}
.icon-Pulse:before {
	content: "\e66f";
}
.icon-Plaster:before {
	content: "\e670";
}
.icon-Plaine:before {
	content: "\e671";
}
.icon-Pill:before {
	content: "\e672";
}
.icon-PicnicBasket:before {
	content: "\e673";
}
.icon-Phone2:before {
	content: "\e674";
}
.icon-Pencil:before {
	content: "\e675";
}
.icon-Pen:before {
	content: "\e676";
}
.icon-PaperClip:before {
	content: "\e677";
}
.icon-On-Off:before {
	content: "\e678";
}
.icon-Mouse:before {
	content: "\e679";
}
.icon-Megaphone:before {
	content: "\e67a";
}
.icon-Linked:before {
	content: "\e67b";
}
.icon-Keyboard:before {
	content: "\e67c";
}
.icon-House:before {
	content: "\e67d";
}
.icon-Heart:before {
	content: "\e67e";
}
.icon-Headset:before {
	content: "\e67f";
}
.icon-FullShoppingCart:before {
	content: "\e680";
}
.icon-FullScreen:before {
	content: "\e681";
}
.icon-Folder:before {
	content: "\e682";
}
.icon-Floppy:before {
	content: "\e683";
}
.icon-Files:before {
	content: "\e684";
}
.icon-File:before {
	content: "\e685";
}
.icon-FileBox:before {
	content: "\e686";
}
.icon-ExitFullScreen:before {
	content: "\e687";
}
.icon-EmptyBox:before {
	content: "\e688";
}
.icon-Delete:before {
	content: "\e689";
}
.icon-Controller:before {
	content: "\e68a";
}
.icon-Compass:before {
	content: "\e68b";
}
.icon-CompassTool:before {
	content: "\e68c";
}
.icon-ClipboardText:before {
	content: "\e68d";
}
.icon-ClipboardChart:before {
	content: "\e68e";
}
.icon-ChemicalGlass:before {
	content: "\e68f";
}
.icon-CD:before {
	content: "\e690";
}
.icon-Carioca:before {
	content: "\e691";
}
.icon-Car:before {
	content: "\e692";
}
.icon-Book:before {
	content: "\e693";
}
.icon-BigTruck:before {
	content: "\e694";
}
.icon-Bicycle:before {
	content: "\e695";
}
.icon-Wrench:before {
	content: "\e696";
}
.icon-Web:before {
	content: "\e697";
}
.icon-Watch:before {
	content: "\e698";
}
.icon-Volume:before {
	content: "\e699";
}
.icon-Video:before {
	content: "\e69a";
}
.icon-Users:before {
	content: "\e69b";
}
.icon-User:before {
	content: "\e69c";
}
.icon-UploadCLoud:before {
	content: "\e69d";
}
.icon-Typing:before {
	content: "\e69e";
}
.icon-Tools:before {
	content: "\e69f";
}
.icon-Tag:before {
	content: "\e6a0";
}
.icon-Speedometter:before {
	content: "\e6a1";
}
.icon-Share:before {
	content: "\e6a2";
}
.icon-Settings:before {
	content: "\e6a3";
}
.icon-Search:before {
	content: "\e6a4";
}
.icon-Screwdriver:before {
	content: "\e6a5";
}
.icon-Rolodex:before {
	content: "\e6a6";
}
.icon-Ringer:before {
	content: "\e6a7";
}
.icon-Resume:before {
	content: "\e6a8";
}
.icon-Restart:before {
	content: "\e6a9";
}
.icon-PowerOff:before {
	content: "\e6aa";
}
.icon-Pointer:before {
	content: "\e6ab";
}
.icon-Picture:before {
	content: "\e6ac";
}
.icon-OpenedLock:before {
	content: "\e6ad";
}
.icon-Notes:before {
	content: "\e6ae";
}
.icon-Mute:before {
	content: "\e6af";
}
.icon-Movie:before {
	content: "\e6b0";
}
.icon-Microphone2:before {
	content: "\e6b1";
}
.icon-Message:before {
	content: "\e6b2";
}
.icon-MessageRight:before {
	content: "\e6b3";
}
.icon-MessageLeft:before {
	content: "\e6b4";
}
.icon-Menu:before {
	content: "\e6b5";
}
.icon-Media:before {
	content: "\e6b6";
}
.icon-Mail:before {
	content: "\e6b7";
}
.icon-List:before {
	content: "\e6b8";
}
.icon-Layers:before {
	content: "\e6b9";
}
.icon-Key:before {
	content: "\e6ba";
}
.icon-Imbox:before {
	content: "\e6bb";
}
.icon-Eye:before {
	content: "\e6bc";
}
.icon-Edit:before {
	content: "\e6bd";
}
.icon-DSLRCamera:before {
	content: "\e6be";
}
.icon-DownloadCloud:before {
	content: "\e6bf";
}
.icon-CompactCamera:before {
	content: "\e6c0";
}
.icon-Cloud:before {
	content: "\e6c1";
}
.icon-ClosedLock:before {
	content: "\e6c2";
}
.icon-Chart2:before {
	content: "\e6c3";
}
.icon-Bulb:before {
	content: "\e6c4";
}
.icon-Briefcase:before {
	content: "\e6c5";
}
.icon-Blog:before {
	content: "\e6c6";
}
.icon-Agenda:before {
	content: "\e6c7";
}
