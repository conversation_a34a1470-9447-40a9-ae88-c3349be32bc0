!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t=t||self).bootstrap={},t.j<PERSON><PERSON>y,t.Pop<PERSON>)}(this,function(t,p,u){"use strict";function n(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function o(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}function e(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)}return i}function r(s){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?e(Object(o),!0).forEach(function(t){var e,i,n;e=s,n=o[i=t],i in e?Object.defineProperty(e,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach(function(t){Object.defineProperty(s,t,Object.getOwnPropertyDescriptor(o,t))})}return s}p=p&&p.hasOwnProperty("default")?p.default:p,u=u&&u.hasOwnProperty("default")?u.default:u;var i="transitionend";var g={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e=t.getAttribute("data-target");if(!e||"#"===e){var i=t.getAttribute("href");e=i&&"#"!==i?i.trim():""}try{return document.querySelector(e)?e:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=p(t).css("transition-duration"),i=p(t).css("transition-delay"),n=parseFloat(e),s=parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(e)+parseFloat(i))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){p(t).trigger(i)},supportsTransitionEnd:function(){return Boolean(i)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,i){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var s=i[n],o=e[n],r=o&&g.isElement(o)?"element":{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new Error(t.toUpperCase()+': Option "'+n+'" provided type "'+r+'" but expected type "'+s+'".')}},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?g.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===p)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=p.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};g.jQueryDetection(),p.fn.emulateTransitionEnd=function(t){var e=this,i=!1;return p(this).one(g.TRANSITION_END,function(){i=!0}),setTimeout(function(){i||g.triggerTransitionEnd(e)},t),this},p.event.special[g.TRANSITION_END]={bindType:i,delegateType:i,handle:function(t){if(p(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var s,a="alert",l="bs.alert",h="."+l,c=p.fn[a],d={CLOSE:"close"+h,CLOSED:"closed"+h,CLICK_DATA_API:"click"+h+".data-api"},f=((s=m.prototype).close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},s.dispose=function(){p.removeData(this._element,l),this._element=null},s._getRootElement=function(t){var e=g.getSelectorFromElement(t),i=!1;return e&&(i=document.querySelector(e)),i||p(t).closest(".alert")[0]},s._triggerCloseEvent=function(t){var e=p.Event(d.CLOSE);return p(t).trigger(e),e},s._removeElement=function(e){var i=this;if(p(e).removeClass("show"),p(e).hasClass("fade")){var t=g.getTransitionDurationFromElement(e);p(e).one(g.TRANSITION_END,function(t){return i._destroyElement(e,t)}).emulateTransitionEnd(t)}else this._destroyElement(e)},s._destroyElement=function(t){p(t).detach().trigger(d.CLOSED).remove()},m._jQueryInterface=function(i){return this.each(function(){var t=p(this),e=t.data(l);e||(e=new m(this),t.data(l,e)),"close"===i&&e[i](this)})},m._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},o(m,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),m);function m(t){this._element=t}p(document).on(d.CLICK_DATA_API,'[data-dismiss="alert"]',f._handleDismiss(new f)),p.fn[a]=f._jQueryInterface,p.fn[a].Constructor=f,p.fn[a].noConflict=function(){return p.fn[a]=c,f._jQueryInterface};var _,v="button",y="bs.button",w="."+y,b=".data-api",E=p.fn[v],C="active",T='[data-toggle^="button"]',S='input:not([type="hidden"])',D=".btn",x={CLICK_DATA_API:"click"+w+b,FOCUS_BLUR_DATA_API:"focus"+w+b+" blur"+w+b,LOAD_DATA_API:"load"+w+b},A=((_=I.prototype).toggle=function(){var t=!0,e=!0,i=p(this._element).closest('[data-toggle="buttons"]')[0];if(i){var n=this._element.querySelector(S);if(n){if("radio"===n.type)if(n.checked&&this._element.classList.contains(C))t=!1;else{var s=i.querySelector(".active");s&&p(s).removeClass(C)}else("checkbox"!==n.type||"LABEL"===this._element.tagName&&n.checked===this._element.classList.contains(C))&&(t=!1);t&&(n.checked=!this._element.classList.contains(C),p(n).trigger("change")),n.focus(),e=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(e&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(C)),t&&p(this._element).toggleClass(C))},_.dispose=function(){p.removeData(this._element,y),this._element=null},I._jQueryInterface=function(e){return this.each(function(){var t=p(this).data(y);t||(t=new I(this),p(this).data(y,t)),"toggle"===e&&t[e]()})},o(I,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),I);function I(t){this._element=t}p(document).on(x.CLICK_DATA_API,T,function(t){var e=t.target;if(p(e).hasClass("btn")||(e=p(e).closest(D)[0]),!e||e.hasAttribute("disabled")||e.classList.contains("disabled"))t.preventDefault();else{var i=e.querySelector(S);if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void t.preventDefault();A._jQueryInterface.call(p(e),"toggle")}}).on(x.FOCUS_BLUR_DATA_API,T,function(t){var e=p(t.target).closest(D)[0];p(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),p(window).on(x.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,i=t.length;e<i;e++){var n=t[e],s=n.querySelector(S);s.checked||s.hasAttribute("checked")?n.classList.add(C):n.classList.remove(C)}for(var o=0,r=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<r;o++){var a=t[o];"true"===a.getAttribute("aria-pressed")?a.classList.add(C):a.classList.remove(C)}}),p.fn[v]=A._jQueryInterface,p.fn[v].Constructor=A,p.fn[v].noConflict=function(){return p.fn[v]=E,A._jQueryInterface};var O,N="carousel",k="bs.carousel",P="."+k,L=".data-api",j=p.fn[N],H={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},M={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},z="next",$="prev",R={SLIDE:"slide"+P,SLID:"slid"+P,KEYDOWN:"keydown"+P,MOUSEENTER:"mouseenter"+P,MOUSELEAVE:"mouseleave"+P,TOUCHSTART:"touchstart"+P,TOUCHMOVE:"touchmove"+P,TOUCHEND:"touchend"+P,POINTERDOWN:"pointerdown"+P,POINTERUP:"pointerup"+P,DRAG_START:"dragstart"+P,LOAD_DATA_API:"load"+P+L,CLICK_DATA_API:"click"+P+L},W="active",F=".active.carousel-item",B={TOUCH:"touch",PEN:"pen"},U=((O=q.prototype).next=function(){this._isSliding||this._slide(z)},O.nextWhenVisible=function(){!document.hidden&&p(this._element).is(":visible")&&"hidden"!==p(this._element).css("visibility")&&this.next()},O.prev=function(){this._isSliding||this._slide($)},O.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(g.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},O.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},O.to=function(t){var e=this;this._activeElement=this._element.querySelector(F);var i=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)p(this._element).one(R.SLID,function(){return e.to(t)});else{if(i===t)return this.pause(),void this.cycle();var n=i<t?z:$;this._slide(n,this._items[t])}},O.dispose=function(){p(this._element).off(P),p.removeData(this._element,k),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},O._getConfig=function(t){return t=r({},H,{},t),g.typeCheckConfig(N,t,M),t},O._handleSwipe=function(){var t=Math.abs(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next()}},O._addEventListeners=function(){var e=this;this._config.keyboard&&p(this._element).on(R.KEYDOWN,function(t){return e._keydown(t)}),"hover"===this._config.pause&&p(this._element).on(R.MOUSEENTER,function(t){return e.pause(t)}).on(R.MOUSELEAVE,function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},O._addTouchEventListeners=function(){var i=this;if(this._touchSupported){var e=function(t){i._pointerEvent&&B[t.originalEvent.pointerType.toUpperCase()]?i.touchStartX=t.originalEvent.clientX:i._pointerEvent||(i.touchStartX=t.originalEvent.touches[0].clientX)},n=function(t){i._pointerEvent&&B[t.originalEvent.pointerType.toUpperCase()]&&(i.touchDeltaX=t.originalEvent.clientX-i.touchStartX),i._handleSwipe(),"hover"===i._config.pause&&(i.pause(),i.touchTimeout&&clearTimeout(i.touchTimeout),i.touchTimeout=setTimeout(function(t){return i.cycle(t)},500+i._config.interval))};p(this._element.querySelectorAll(".carousel-item img")).on(R.DRAG_START,function(t){return t.preventDefault()}),this._pointerEvent?(p(this._element).on(R.POINTERDOWN,function(t){return e(t)}),p(this._element).on(R.POINTERUP,function(t){return n(t)}),this._element.classList.add("pointer-event")):(p(this._element).on(R.TOUCHSTART,function(t){return e(t)}),p(this._element).on(R.TOUCHMOVE,function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?i.touchDeltaX=0:i.touchDeltaX=e.originalEvent.touches[0].clientX-i.touchStartX}),p(this._element).on(R.TOUCHEND,function(t){return n(t)}))}},O._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},O._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},O._getItemByDirection=function(t,e){var i=t===z,n=t===$,s=this._getItemIndex(e),o=this._items.length-1;if((n&&0===s||i&&s===o)&&!this._config.wrap)return e;var r=(s+(t===$?-1:1))%this._items.length;return-1==r?this._items[this._items.length-1]:this._items[r]},O._triggerSlideEvent=function(t,e){var i=this._getItemIndex(t),n=this._getItemIndex(this._element.querySelector(F)),s=p.Event(R.SLIDE,{relatedTarget:t,direction:e,from:n,to:i});return p(this._element).trigger(s),s},O._setActiveIndicatorElement=function(t){if(this._indicatorsElement){var e=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));p(e).removeClass(W);var i=this._indicatorsElement.children[this._getItemIndex(t)];i&&p(i).addClass(W)}},O._slide=function(t,e){var i,n,s,o=this,r=this._element.querySelector(F),a=this._getItemIndex(r),l=e||r&&this._getItemByDirection(t,r),h=this._getItemIndex(l),c=Boolean(this._interval);if(s=t===z?(i="carousel-item-left",n="carousel-item-next","left"):(i="carousel-item-right",n="carousel-item-prev","right"),l&&p(l).hasClass(W))this._isSliding=!1;else if(!this._triggerSlideEvent(l,s).isDefaultPrevented()&&r&&l){this._isSliding=!0,c&&this.pause(),this._setActiveIndicatorElement(l);var u=p.Event(R.SLID,{relatedTarget:l,direction:s,from:a,to:h});if(p(this._element).hasClass("slide")){p(l).addClass(n),g.reflow(l),p(r).addClass(i),p(l).addClass(i);var d=parseInt(l.getAttribute("data-interval"),10);d?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=d):this._config.interval=this._config.defaultInterval||this._config.interval;var f=g.getTransitionDurationFromElement(r);p(r).one(g.TRANSITION_END,function(){p(l).removeClass(i+" "+n).addClass(W),p(r).removeClass(W+" "+n+" "+i),o._isSliding=!1,setTimeout(function(){return p(o._element).trigger(u)},0)}).emulateTransitionEnd(f)}else p(r).removeClass(W),p(l).addClass(W),this._isSliding=!1,p(this._element).trigger(u);c&&this.cycle()}},q._jQueryInterface=function(n){return this.each(function(){var t=p(this).data(k),e=r({},H,{},p(this).data());"object"==typeof n&&(e=r({},e,{},n));var i="string"==typeof n?n:e.slide;if(t||(t=new q(this,e),p(this).data(k,t)),"number"==typeof n)t.to(n);else if("string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},q._dataApiClickHandler=function(t){var e=g.getSelectorFromElement(this);if(e){var i=p(e)[0];if(i&&p(i).hasClass("carousel")){var n=r({},p(i).data(),{},p(this).data()),s=this.getAttribute("data-slide-to");s&&(n.interval=!1),q._jQueryInterface.call(p(i),n),s&&p(i).data(k).to(s),t.preventDefault()}}},o(q,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return H}}]),q);function q(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}p(document).on(R.CLICK_DATA_API,"[data-slide], [data-slide-to]",U._dataApiClickHandler),p(window).on(R.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),e=0,i=t.length;e<i;e++){var n=p(t[e]);U._jQueryInterface.call(n,n.data())}}),p.fn[N]=U._jQueryInterface,p.fn[N].Constructor=U,p.fn[N].noConflict=function(){return p.fn[N]=j,U._jQueryInterface};var Q,K="collapse",V="bs.collapse",Y="."+V,X=p.fn[K],Z={toggle:!0,parent:""},G={toggle:"boolean",parent:"(string|element)"},J={SHOW:"show"+Y,SHOWN:"shown"+Y,HIDE:"hide"+Y,HIDDEN:"hidden"+Y,CLICK_DATA_API:"click"+Y+".data-api"},tt="show",et="collapse",it="collapsing",nt="collapsed",st='[data-toggle="collapse"]',ot=((Q=rt.prototype).toggle=function(){p(this._element).hasClass(tt)?this.hide():this.show()},Q.show=function(){var t,e,i=this;if(!(this._isTransitioning||p(this._element).hasClass(tt)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(t){return"string"==typeof i._config.parent?t.getAttribute("data-parent")===i._config.parent:t.classList.contains(et)})).length&&(t=null),t&&(e=p(t).not(this._selector).data(V))&&e._isTransitioning))){var n=p.Event(J.SHOW);if(p(this._element).trigger(n),!n.isDefaultPrevented()){t&&(rt._jQueryInterface.call(p(t).not(this._selector),"hide"),e||p(t).data(V,null));var s=this._getDimension();p(this._element).removeClass(et).addClass(it),this._element.style[s]=0,this._triggerArray.length&&p(this._triggerArray).removeClass(nt).attr("aria-expanded",!0),this.setTransitioning(!0);var o="scroll"+(s[0].toUpperCase()+s.slice(1)),r=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,function(){p(i._element).removeClass(it).addClass(et).addClass(tt),i._element.style[s]="",i.setTransitioning(!1),p(i._element).trigger(J.SHOWN)}).emulateTransitionEnd(r),this._element.style[s]=this._element[o]+"px"}}},Q.hide=function(){var t=this;if(!this._isTransitioning&&p(this._element).hasClass(tt)){var e=p.Event(J.HIDE);if(p(this._element).trigger(e),!e.isDefaultPrevented()){var i=this._getDimension();this._element.style[i]=this._element.getBoundingClientRect()[i]+"px",g.reflow(this._element),p(this._element).addClass(it).removeClass(et).removeClass(tt);var n=this._triggerArray.length;if(0<n)for(var s=0;s<n;s++){var o=this._triggerArray[s],r=g.getSelectorFromElement(o);null!==r&&(p([].slice.call(document.querySelectorAll(r))).hasClass(tt)||p(o).addClass(nt).attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[i]="";var a=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,function(){t.setTransitioning(!1),p(t._element).removeClass(it).addClass(et).trigger(J.HIDDEN)}).emulateTransitionEnd(a)}}},Q.setTransitioning=function(t){this._isTransitioning=t},Q.dispose=function(){p.removeData(this._element,V),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},Q._getConfig=function(t){return(t=r({},Z,{},t)).toggle=Boolean(t.toggle),g.typeCheckConfig(K,t,G),t},Q._getDimension=function(){return p(this._element).hasClass("width")?"width":"height"},Q._getParent=function(){var t,i=this;g.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',n=[].slice.call(t.querySelectorAll(e));return p(n).each(function(t,e){i._addAriaAndCollapsedClass(rt._getTargetFromElement(e),[e])}),t},Q._addAriaAndCollapsedClass=function(t,e){var i=p(t).hasClass(tt);e.length&&p(e).toggleClass(nt,!i).attr("aria-expanded",i)},rt._getTargetFromElement=function(t){var e=g.getSelectorFromElement(t);return e?document.querySelector(e):null},rt._jQueryInterface=function(n){return this.each(function(){var t=p(this),e=t.data(V),i=r({},Z,{},t.data(),{},"object"==typeof n&&n?n:{});if(!e&&i.toggle&&/show|hide/.test(n)&&(i.toggle=!1),e||(e=new rt(this,i),t.data(V,e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},o(rt,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Z}}]),rt);function rt(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var i=[].slice.call(document.querySelectorAll(st)),n=0,s=i.length;n<s;n++){var o=i[n],r=g.getSelectorFromElement(o),a=[].slice.call(document.querySelectorAll(r)).filter(function(t){return t===e});null!==r&&0<a.length&&(this._selector=r,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}p(document).on(J.CLICK_DATA_API,st,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var i=p(this),e=g.getSelectorFromElement(this),n=[].slice.call(document.querySelectorAll(e));p(n).each(function(){var t=p(this),e=t.data(V)?"toggle":i.data();ot._jQueryInterface.call(t,e)})}),p.fn[K]=ot._jQueryInterface,p.fn[K].Constructor=ot,p.fn[K].noConflict=function(){return p.fn[K]=X,ot._jQueryInterface};var at,lt="dropdown",ht="bs.dropdown",ct="."+ht,ut=".data-api",dt=p.fn[lt],ft=new RegExp("38|40|27"),pt={HIDE:"hide"+ct,HIDDEN:"hidden"+ct,SHOW:"show"+ct,SHOWN:"shown"+ct,CLICK:"click"+ct,CLICK_DATA_API:"click"+ct+ut,KEYDOWN_DATA_API:"keydown"+ct+ut,KEYUP_DATA_API:"keyup"+ct+ut},gt="disabled",mt="show",_t="dropdown-menu-right",vt='[data-toggle="dropdown"]',yt=".dropdown-menu",wt={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},bt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},Et=((at=Ct.prototype).toggle=function(){if(!this._element.disabled&&!p(this._element).hasClass(gt)){var t=p(this._menu).hasClass(mt);Ct._clearMenus(),t||this.show(!0)}},at.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||p(this._element).hasClass(gt)||p(this._menu).hasClass(mt))){var e={relatedTarget:this._element},i=p.Event(pt.SHOW,e),n=Ct._getParentFromElement(this._element);if(p(n).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===u)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var s=this._element;"parent"===this._config.reference?s=n:g.isElement(this._config.reference)&&(s=this._config.reference,void 0!==this._config.reference.jquery&&(s=this._config.reference[0])),"scrollParent"!==this._config.boundary&&p(n).addClass("position-static"),this._popper=new u(s,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===p(n).closest(".navbar-nav").length&&p(document.body).children().on("mouseover",null,p.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),p(this._menu).toggleClass(mt),p(n).toggleClass(mt).trigger(p.Event(pt.SHOWN,e))}}},at.hide=function(){if(!this._element.disabled&&!p(this._element).hasClass(gt)&&p(this._menu).hasClass(mt)){var t={relatedTarget:this._element},e=p.Event(pt.HIDE,t),i=Ct._getParentFromElement(this._element);p(i).trigger(e),e.isDefaultPrevented()||(this._popper&&this._popper.destroy(),p(this._menu).toggleClass(mt),p(i).toggleClass(mt).trigger(p.Event(pt.HIDDEN,t)))}},at.dispose=function(){p.removeData(this._element,ht),p(this._element).off(ct),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},at.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},at._addEventListeners=function(){var e=this;p(this._element).on(pt.CLICK,function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},at._getConfig=function(t){return t=r({},this.constructor.Default,{},p(this._element).data(),{},t),g.typeCheckConfig(lt,t,this.constructor.DefaultType),t},at._getMenuElement=function(){if(!this._menu){var t=Ct._getParentFromElement(this._element);t&&(this._menu=t.querySelector(yt))}return this._menu},at._getPlacement=function(){var t=p(this._element.parentNode),e="bottom-start";return t.hasClass("dropup")?(e="top-start",p(this._menu).hasClass(_t)&&(e="top-end")):t.hasClass("dropright")?e="right-start":t.hasClass("dropleft")?e="left-start":p(this._menu).hasClass(_t)&&(e="bottom-end"),e},at._detectNavbar=function(){return 0<p(this._element).closest(".navbar").length},at._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=r({},t.offsets,{},e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},at._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),r({},t,{},this._config.popperConfig)},Ct._jQueryInterface=function(e){return this.each(function(){var t=p(this).data(ht);if(t||(t=new Ct(this,"object"==typeof e?e:null),p(this).data(ht,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},Ct._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll(vt)),i=0,n=e.length;i<n;i++){var s=Ct._getParentFromElement(e[i]),o=p(e[i]).data(ht),r={relatedTarget:e[i]};if(t&&"click"===t.type&&(r.clickEvent=t),o){var a=o._menu;if(p(s).hasClass(mt)&&!(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&p.contains(s,t.target))){var l=p.Event(pt.HIDE,r);p(s).trigger(l),l.isDefaultPrevented()||("ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),e[i].setAttribute("aria-expanded","false"),o._popper&&o._popper.destroy(),p(a).removeClass(mt),p(s).removeClass(mt).trigger(p.Event(pt.HIDDEN,r)))}}}},Ct._getParentFromElement=function(t){var e,i=g.getSelectorFromElement(t);return i&&(e=document.querySelector(i)),e||t.parentNode},Ct._dataApiKeydownHandler=function(t){if((/input|textarea/i.test(t.target.tagName)?!(32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||p(t.target).closest(yt).length)):ft.test(t.which))&&(t.preventDefault(),t.stopPropagation(),!this.disabled&&!p(this).hasClass(gt))){var e=Ct._getParentFromElement(this),i=p(e).hasClass(mt);if(i||27!==t.which)if(i&&(!i||27!==t.which&&32!==t.which)){var n=[].slice.call(e.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(t){return p(t).is(":visible")});if(0!==n.length){var s=n.indexOf(t.target);38===t.which&&0<s&&s--,40===t.which&&s<n.length-1&&s++,s<0&&(s=0),n[s].focus()}}else{if(27===t.which){var o=e.querySelector(vt);p(o).trigger("focus")}p(this).trigger("click")}}},o(Ct,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return wt}},{key:"DefaultType",get:function(){return bt}}]),Ct);function Ct(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}p(document).on(pt.KEYDOWN_DATA_API,vt,Et._dataApiKeydownHandler).on(pt.KEYDOWN_DATA_API,yt,Et._dataApiKeydownHandler).on(pt.CLICK_DATA_API+" "+pt.KEYUP_DATA_API,Et._clearMenus).on(pt.CLICK_DATA_API,vt,function(t){t.preventDefault(),t.stopPropagation(),Et._jQueryInterface.call(p(this),"toggle")}).on(pt.CLICK_DATA_API,".dropdown form",function(t){t.stopPropagation()}),p.fn[lt]=Et._jQueryInterface,p.fn[lt].Constructor=Et,p.fn[lt].noConflict=function(){return p.fn[lt]=dt,Et._jQueryInterface};var Tt,St="modal",Dt="bs.modal",xt="."+Dt,At=p.fn[St],It={backdrop:!0,keyboard:!0,focus:!0,show:!0},Ot={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},Nt={HIDE:"hide"+xt,HIDE_PREVENTED:"hidePrevented"+xt,HIDDEN:"hidden"+xt,SHOW:"show"+xt,SHOWN:"shown"+xt,FOCUSIN:"focusin"+xt,RESIZE:"resize"+xt,CLICK_DISMISS:"click.dismiss"+xt,KEYDOWN_DISMISS:"keydown.dismiss"+xt,MOUSEUP_DISMISS:"mouseup.dismiss"+xt,MOUSEDOWN_DISMISS:"mousedown.dismiss"+xt,CLICK_DATA_API:"click"+xt+".data-api"},kt="modal-open",Pt="fade",Lt="show",jt="modal-static",Ht=".navbar-sticky, .fixed-bottom, .is-fixed, .sticky-top",Mt=".sticky-top",zt=((Tt=$t.prototype).toggle=function(t){return this._isShown?this.hide():this.show(t)},Tt.show=function(t){var e=this;if(!this._isShown&&!this._isTransitioning){p(this._element).hasClass(Pt)&&(this._isTransitioning=!0);var i=p.Event(Nt.SHOW,{relatedTarget:t});p(this._element).trigger(i),this._isShown||i.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),p(this._element).on(Nt.CLICK_DISMISS,'[data-dismiss="modal"]',function(t){return e.hide(t)}),p(this._dialog).on(Nt.MOUSEDOWN_DISMISS,function(){p(e._element).one(Nt.MOUSEUP_DISMISS,function(t){p(t.target).is(e._element)&&(e._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return e._showElement(t)}))}},Tt.hide=function(t){var e=this;if(t&&t.preventDefault(),this._isShown&&!this._isTransitioning){var i=p.Event(Nt.HIDE);if(p(this._element).trigger(i),this._isShown&&!i.isDefaultPrevented()){this._isShown=!1;var n=p(this._element).hasClass(Pt);if(n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),p(document).off(Nt.FOCUSIN),p(this._element).removeClass(Lt),p(this._element).off(Nt.CLICK_DISMISS),p(this._dialog).off(Nt.MOUSEDOWN_DISMISS),n){var s=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,function(t){return e._hideModal(t)}).emulateTransitionEnd(s)}else this._hideModal()}}},Tt.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return p(t).off(xt)}),p(document).off(Nt.FOCUSIN),p.removeData(this._element,Dt),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},Tt.handleUpdate=function(){this._adjustDialog()},Tt._getConfig=function(t){return t=r({},It,{},t),g.typeCheckConfig(St,t,Ot),t},Tt._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var e=p.Event(Nt.HIDE_PREVENTED);if(p(this._element).trigger(e),e.defaultPrevented)return;this._element.classList.add(jt);var i=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,function(){t._element.classList.remove(jt)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},Tt._showElement=function(t){var e=this,i=p(this._element).hasClass(Pt),n=this._dialog?this._dialog.querySelector(".modal-body"):null;function s(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,p(e._element).trigger(o)}this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),p(this._dialog).hasClass("modal-dialog-scrollable")&&n?n.scrollTop=0:this._element.scrollTop=0,i&&g.reflow(this._element),p(this._element).addClass(Lt),this._config.focus&&this._enforceFocus();var o=p.Event(Nt.SHOWN,{relatedTarget:t});if(i){var r=g.getTransitionDurationFromElement(this._dialog);p(this._dialog).one(g.TRANSITION_END,s).emulateTransitionEnd(r)}else s()},Tt._enforceFocus=function(){var e=this;p(document).off(Nt.FOCUSIN).on(Nt.FOCUSIN,function(t){document!==t.target&&e._element!==t.target&&0===p(e._element).has(t.target).length&&e._element.focus()})},Tt._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?p(this._element).on(Nt.KEYDOWN_DISMISS,function(t){27===t.which&&e._triggerBackdropTransition()}):this._isShown||p(this._element).off(Nt.KEYDOWN_DISMISS)},Tt._setResizeEvent=function(){var e=this;this._isShown?p(window).on(Nt.RESIZE,function(t){return e.handleUpdate(t)}):p(window).off(Nt.RESIZE)},Tt._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){p(document.body).removeClass(kt),t._resetAdjustments(),t._resetScrollbar(),p(t._element).trigger(Nt.HIDDEN)})},Tt._removeBackdrop=function(){this._backdrop&&(p(this._backdrop).remove(),this._backdrop=null)},Tt._showBackdrop=function(t){var e=this,i=p(this._element).hasClass(Pt)?Pt:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",i&&this._backdrop.classList.add(i),p(this._backdrop).appendTo(document.body),p(this._element).on(Nt.CLICK_DISMISS,function(t){e._ignoreBackdropClick?e._ignoreBackdropClick=!1:t.target===t.currentTarget&&e._triggerBackdropTransition()}),i&&g.reflow(this._backdrop),p(this._backdrop).addClass(Lt),!t)return;if(!i)return void t();var n=g.getTransitionDurationFromElement(this._backdrop);p(this._backdrop).one(g.TRANSITION_END,t).emulateTransitionEnd(n)}else if(!this._isShown&&this._backdrop){p(this._backdrop).removeClass(Lt);var s=function(){e._removeBackdrop(),t&&t()};if(p(this._element).hasClass(Pt)){var o=g.getTransitionDurationFromElement(this._backdrop);p(this._backdrop).one(g.TRANSITION_END,s).emulateTransitionEnd(o)}else s()}else t&&t()},Tt._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},Tt._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},Tt._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},Tt._setScrollbar=function(){var s=this;if(this._isBodyOverflowing){var t=[].slice.call(document.querySelectorAll(Ht)),e=[].slice.call(document.querySelectorAll(Mt));p(t).each(function(t,e){var i=e.style.paddingRight,n=p(e).css("padding-right");p(e).data("padding-right",i).css("padding-right",parseFloat(n)+s._scrollbarWidth+"px")}),p(e).each(function(t,e){var i=e.style.marginRight,n=p(e).css("margin-right");p(e).data("margin-right",i).css("margin-right",parseFloat(n)-s._scrollbarWidth+"px")});var i=document.body.style.paddingRight,n=p(document.body).css("padding-right");p(document.body).data("padding-right",i).css("padding-right",parseFloat(n)+this._scrollbarWidth+"px")}p(document.body).addClass(kt)},Tt._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(Ht));p(t).each(function(t,e){var i=p(e).data("padding-right");p(e).removeData("padding-right"),e.style.paddingRight=i||""});var e=[].slice.call(document.querySelectorAll(Mt));p(e).each(function(t,e){var i=p(e).data("margin-right");void 0!==i&&p(e).css("margin-right",i).removeData("margin-right")});var i=p(document.body).data("padding-right");p(document.body).removeData("padding-right"),document.body.style.paddingRight=i||""},Tt._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},$t._jQueryInterface=function(i,n){return this.each(function(){var t=p(this).data(Dt),e=r({},It,{},p(this).data(),{},"object"==typeof i&&i?i:{});if(t||(t=new $t(this,e),p(this).data(Dt,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i](n)}else e.show&&t.show(n)})},o($t,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return It}}]),$t);function $t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}p(document).on(Nt.CLICK_DATA_API,'[data-toggle="modal"]',function(t){var e,i=this,n=g.getSelectorFromElement(this);n&&(e=document.querySelector(n));var s=p(e).data(Dt)?"toggle":r({},p(e).data(),{},p(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var o=p(e).one(Nt.SHOW,function(t){t.isDefaultPrevented()||o.one(Nt.HIDDEN,function(){p(i).is(":visible")&&i.focus()})});zt._jQueryInterface.call(p(e),s,this)}),p.fn[St]=zt._jQueryInterface,p.fn[St].Constructor=zt,p.fn[St].noConflict=function(){return p.fn[St]=At,zt._jQueryInterface};var Rt=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],Wt=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,Ft=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function Bt(t,o,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var i=(new window.DOMParser).parseFromString(t,"text/html"),r=Object.keys(o),a=[].slice.call(i.body.querySelectorAll("*")),n=function(t){var e=a[t],i=e.nodeName.toLowerCase();if(-1===r.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e),"continue";var n=[].slice.call(e.attributes),s=[].concat(o["*"]||[],o[i]||[]);n.forEach(function(t){!function(t,e){var i=t.nodeName.toLowerCase();if(-1!==e.indexOf(i))return-1===Rt.indexOf(i)||Boolean(t.nodeValue.match(Wt)||t.nodeValue.match(Ft));for(var n=e.filter(function(t){return t instanceof RegExp}),s=0,o=n.length;s<o;s++)if(i.match(n[s]))return 1}(t,s)&&e.removeAttribute(t.nodeName)})},s=0,l=a.length;s<l;s++)n(s);return i.body.innerHTML}var Ut,qt="tooltip",Qt="bs.tooltip",Kt="."+Qt,Vt=p.fn[qt],Yt="bs-tooltip",Xt=new RegExp("(^|\\s)"+Yt+"\\S+","g"),Zt=["sanitize","whiteList","sanitizeFn"],Gt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Jt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},te={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},ee="show",ie={HIDE:"hide"+Kt,HIDDEN:"hidden"+Kt,SHOW:"show"+Kt,SHOWN:"shown"+Kt,INSERTED:"inserted"+Kt,CLICK:"click"+Kt,FOCUSIN:"focusin"+Kt,FOCUSOUT:"focusout"+Kt,MOUSEENTER:"mouseenter"+Kt,MOUSELEAVE:"mouseleave"+Kt},ne="fade",se="show",oe="hover",re="focus",ae=((Ut=le.prototype).enable=function(){this._isEnabled=!0},Ut.disable=function(){this._isEnabled=!1},Ut.toggleEnabled=function(){this._isEnabled=!this._isEnabled},Ut.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,i=p(t.currentTarget).data(e);i||(i=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(e,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(p(this.getTipElement()).hasClass(se))return void this._leave(null,this);this._enter(null,this)}},Ut.dispose=function(){clearTimeout(this._timeout),p.removeData(this.element,this.constructor.DATA_KEY),p(this.element).off(this.constructor.EVENT_KEY),p(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&p(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},Ut.show=function(){var e=this;if("none"===p(this.element).css("display"))throw new Error("Please use show on visible elements");var t=p.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){p(this.element).trigger(t);var i=g.findShadowRoot(this.element),n=p.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!n)return;var s=this.getTipElement(),o=g.getUID(this.constructor.NAME);s.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&p(s).addClass(ne);var r="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,a=this._getAttachment(r);this.addAttachmentClass(a);var l=this._getContainer();p(s).data(this.constructor.DATA_KEY,this),p.contains(this.element.ownerDocument.documentElement,this.tip)||p(s).appendTo(l),p(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new u(this.element,s,this._getPopperConfig(a)),p(s).addClass(se),"ontouchstart"in document.documentElement&&p(document.body).children().on("mouseover",null,p.noop);var h=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,p(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)};if(p(this.tip).hasClass(ne)){var c=g.getTransitionDurationFromElement(this.tip);p(this.tip).one(g.TRANSITION_END,h).emulateTransitionEnd(c)}else h()}},Ut.hide=function(t){function e(){i._hoverState!==ee&&n.parentNode&&n.parentNode.removeChild(n),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),p(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),t&&t()}var i=this,n=this.getTipElement(),s=p.Event(this.constructor.Event.HIDE);if(p(this.element).trigger(s),!s.isDefaultPrevented()){if(p(n).removeClass(se),"ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),this._activeTrigger.click=!1,this._activeTrigger[re]=!1,this._activeTrigger[oe]=!1,p(this.tip).hasClass(ne)){var o=g.getTransitionDurationFromElement(n);p(n).one(g.TRANSITION_END,e).emulateTransitionEnd(o)}else e();this._hoverState=""}},Ut.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},Ut.isWithContent=function(){return Boolean(this.getTitle())},Ut.addAttachmentClass=function(t){p(this.getTipElement()).addClass(Yt+"-"+t)},Ut.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},Ut.setContent=function(){var t=this.getTipElement();this.setElementContent(p(t.querySelectorAll(".tooltip-inner")),this.getTitle()),p(t).removeClass(ne+" "+se)},Ut.setElementContent=function(t,e){"object"!=typeof e||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Bt(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?p(e).parent().is(t)||t.empty().append(e):t.text(p(e).text())},Ut.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},Ut._getPopperConfig=function(t){var e=this;return r({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}},{},this.config.popperConfig)},Ut._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=r({},t.offsets,{},e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},Ut._getContainer=function(){return!1===this.config.container?document.body:g.isElement(this.config.container)?p(this.config.container):p(document).find(this.config.container)},Ut._getAttachment=function(t){return Jt[t.toUpperCase()]},Ut._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(t){if("click"===t)p(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(t){return n.toggle(t)});else if("manual"!==t){var e=t===oe?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,i=t===oe?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT;p(n.element).on(e,n.config.selector,function(t){return n._enter(t)}).on(i,n.config.selector,function(t){return n._leave(t)})}}),this._hideModalHandler=function(){n.element&&n.hide()},p(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=r({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},Ut._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},Ut._enter=function(t,e){var i=this.constructor.DATA_KEY;(e=e||p(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusin"===t.type?re:oe]=!0),p(e.getTipElement()).hasClass(se)||e._hoverState===ee?e._hoverState=ee:(clearTimeout(e._timeout),e._hoverState=ee,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===ee&&e.show()},e.config.delay.show):e.show())},Ut._leave=function(t,e){var i=this.constructor.DATA_KEY;(e=e||p(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusout"===t.type?re:oe]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){"out"===e._hoverState&&e.hide()},e.config.delay.hide):e.hide())},Ut._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},Ut._getConfig=function(t){var e=p(this.element).data();return Object.keys(e).forEach(function(t){-1!==Zt.indexOf(t)&&delete e[t]}),"number"==typeof(t=r({},this.constructor.Default,{},e,{},"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),g.typeCheckConfig(qt,t,this.constructor.DefaultType),t.sanitize&&(t.template=Bt(t.template,t.whiteList,t.sanitizeFn)),t},Ut._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},Ut._cleanTipClass=function(){var t=p(this.getTipElement()),e=t.attr("class").match(Xt);null!==e&&e.length&&t.removeClass(e.join(""))},Ut._handlePopperPlacementChange=function(t){var e=t.instance;this.tip=e.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},Ut._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(p(t).removeClass(ne),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},le._jQueryInterface=function(i){return this.each(function(){var t=p(this).data(Qt),e="object"==typeof i&&i;if((t||!/dispose|hide/.test(i))&&(t||(t=new le(this,e),p(this).data(Qt,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},o(le,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return te}},{key:"NAME",get:function(){return qt}},{key:"DATA_KEY",get:function(){return Qt}},{key:"Event",get:function(){return ie}},{key:"EVENT_KEY",get:function(){return Kt}},{key:"DefaultType",get:function(){return Gt}}]),le);function le(t,e){if(void 0===u)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}p.fn[qt]=ae._jQueryInterface,p.fn[qt].Constructor=ae,p.fn[qt].noConflict=function(){return p.fn[qt]=Vt,ae._jQueryInterface};var he="popover",ce="bs.popover",ue="."+ce,de=p.fn[he],fe="bs-popover",pe=new RegExp("(^|\\s)"+fe+"\\S+","g"),ge=r({},ae.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),me=r({},ae.DefaultType,{content:"(string|element|function)"}),_e={HIDE:"hide"+ue,HIDDEN:"hidden"+ue,SHOW:"show"+ue,SHOWN:"shown"+ue,INSERTED:"inserted"+ue,CLICK:"click"+ue,FOCUSIN:"focusin"+ue,FOCUSOUT:"focusout"+ue,MOUSEENTER:"mouseenter"+ue,MOUSELEAVE:"mouseleave"+ue},ve=function(t){function n(){return t.apply(this,arguments)||this}var e,i;i=t,(e=n).prototype=Object.create(i.prototype),(e.prototype.constructor=e).__proto__=i;var s=n.prototype;return s.isWithContent=function(){return this.getTitle()||this._getContent()},s.addAttachmentClass=function(t){p(this.getTipElement()).addClass(fe+"-"+t)},s.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},s.setContent=function(){var t=p(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(".popover-body"),e),t.removeClass("fade show")},s._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},s._cleanTipClass=function(){var t=p(this.getTipElement()),e=t.attr("class").match(pe);null!==e&&0<e.length&&t.removeClass(e.join(""))},n._jQueryInterface=function(i){return this.each(function(){var t=p(this).data(ce),e="object"==typeof i?i:null;if((t||!/dispose|hide/.test(i))&&(t||(t=new n(this,e),p(this).data(ce,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},o(n,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return ge}},{key:"NAME",get:function(){return he}},{key:"DATA_KEY",get:function(){return ce}},{key:"Event",get:function(){return _e}},{key:"EVENT_KEY",get:function(){return ue}},{key:"DefaultType",get:function(){return me}}]),n}(ae);p.fn[he]=ve._jQueryInterface,p.fn[he].Constructor=ve,p.fn[he].noConflict=function(){return p.fn[he]=de,ve._jQueryInterface};var ye,we="scrollspy",be="bs.scrollspy",Ee="."+be,Ce=p.fn[we],Te={offset:10,method:"auto",target:""},Se={offset:"number",method:"string",target:"(string|element)"},De={ACTIVATE:"activate"+Ee,SCROLL:"scroll"+Ee,LOAD_DATA_API:"load"+Ee+".data-api"},xe="active",Ae=".nav, .list-group",Ie=".nav-link",Oe=".list-group-item",Ne="position",ke=((ye=Pe.prototype).refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":Ne,s="auto"===this._config.method?t:this._config.method,o=s===Ne?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,i=g.getSelectorFromElement(t);if(i&&(e=document.querySelector(i)),e){var n=e.getBoundingClientRect();if(n.width||n.height)return[p(e)[s]().top+o,i]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},ye.dispose=function(){p.removeData(this._element,be),p(this._scrollElement).off(Ee),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},ye._getConfig=function(t){if("string"!=typeof(t=r({},Te,{},"object"==typeof t&&t?t:{})).target){var e=p(t.target).attr("id");e||(e=g.getUID(we),p(t.target).attr("id",e)),t.target="#"+e}return g.typeCheckConfig(we,t,Se),t},ye._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},ye._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},ye._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},ye._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),i=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),i<=t){var n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var s=this._offsets.length;s--;)this._activeTarget!==this._targets[s]&&t>=this._offsets[s]&&(void 0===this._offsets[s+1]||t<this._offsets[s+1])&&this._activate(this._targets[s])}},ye._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),i=p([].slice.call(document.querySelectorAll(t.join(","))));i.hasClass("dropdown-item")?(i.closest(".dropdown").find(".dropdown-toggle").addClass(xe),i.addClass(xe)):(i.addClass(xe),i.parents(Ae).prev(Ie+", "+Oe).addClass(xe),i.parents(Ae).prev(".nav-item").children(Ie).addClass(xe)),p(this._scrollElement).trigger(De.ACTIVATE,{relatedTarget:e})},ye._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains(xe)}).forEach(function(t){return t.classList.remove(xe)})},Pe._jQueryInterface=function(e){return this.each(function(){var t=p(this).data(be);if(t||(t=new Pe(this,"object"==typeof e&&e),p(this).data(be,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},o(Pe,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Te}}]),Pe);function Pe(t,e){var i=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+Ie+","+this._config.target+" "+Oe+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,p(this._scrollElement).on(De.SCROLL,function(t){return i._process(t)}),this.refresh(),this._process()}p(window).on(De.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),e=t.length;e--;){var i=p(t[e]);ke._jQueryInterface.call(i,i.data())}}),p.fn[we]=ke._jQueryInterface,p.fn[we].Constructor=ke,p.fn[we].noConflict=function(){return p.fn[we]=Ce,ke._jQueryInterface};var Le,je="bs.tab",He="."+je,Me=p.fn.tab,ze={HIDE:"hide"+He,HIDDEN:"hidden"+He,SHOW:"show"+He,SHOWN:"shown"+He,CLICK_DATA_API:"click"+He+".data-api"},$e="active",Re=".active",We="> li > .active",Fe=((Le=Be.prototype).show=function(){var i=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&p(this._element).hasClass($e)||p(this._element).hasClass("disabled"))){var t,n,e=p(this._element).closest(".nav, .list-group")[0],s=g.getSelectorFromElement(this._element);if(e){var o="UL"===e.nodeName||"OL"===e.nodeName?We:Re;n=(n=p.makeArray(p(e).find(o)))[n.length-1]}var r=p.Event(ze.HIDE,{relatedTarget:this._element}),a=p.Event(ze.SHOW,{relatedTarget:n});if(n&&p(n).trigger(r),p(this._element).trigger(a),!a.isDefaultPrevented()&&!r.isDefaultPrevented()){s&&(t=document.querySelector(s)),this._activate(this._element,e);var l=function(){var t=p.Event(ze.HIDDEN,{relatedTarget:i._element}),e=p.Event(ze.SHOWN,{relatedTarget:n});p(n).trigger(t),p(i._element).trigger(e)};t?this._activate(t,t.parentNode,l):l()}}},Le.dispose=function(){p.removeData(this._element,je),this._element=null},Le._activate=function(t,e,i){function n(){return s._transitionComplete(t,o,i)}var s=this,o=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?p(e).children(Re):p(e).find(We))[0],r=i&&o&&p(o).hasClass("fade");if(o&&r){var a=g.getTransitionDurationFromElement(o);p(o).removeClass("show").one(g.TRANSITION_END,n).emulateTransitionEnd(a)}else n()},Le._transitionComplete=function(t,e,i){if(e){p(e).removeClass($e);var n=p(e.parentNode).find("> .dropdown-menu .active")[0];n&&p(n).removeClass($e),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}if(p(t).addClass($e),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),g.reflow(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&p(t.parentNode).hasClass("dropdown-menu")){var s=p(t).closest(".dropdown")[0];if(s){var o=[].slice.call(s.querySelectorAll(".dropdown-toggle"));p(o).addClass($e)}t.setAttribute("aria-expanded",!0)}i&&i()},Be._jQueryInterface=function(i){return this.each(function(){var t=p(this),e=t.data(je);if(e||(e=new Be(this),t.data(je,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},o(Be,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),Be);function Be(t){this._element=t}p(document).on(ze.CLICK_DATA_API,'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),Fe._jQueryInterface.call(p(this),"show")}),p.fn.tab=Fe._jQueryInterface,p.fn.tab.Constructor=Fe,p.fn.tab.noConflict=function(){return p.fn.tab=Me,Fe._jQueryInterface};var Ue,qe="toast",Qe="bs.toast",Ke="."+Qe,Ve=p.fn[qe],Ye={CLICK_DISMISS:"click.dismiss"+Ke,HIDE:"hide"+Ke,HIDDEN:"hidden"+Ke,SHOW:"show"+Ke,SHOWN:"shown"+Ke},Xe="show",Ze="showing",Ge={animation:"boolean",autohide:"boolean",delay:"number"},Je={animation:!0,autohide:!0,delay:500},ti=((Ue=ei.prototype).show=function(){var t=this,e=p.Event(Ye.SHOW);if(p(this._element).trigger(e),!e.isDefaultPrevented()){this._config.animation&&this._element.classList.add("fade");var i=function(){t._element.classList.remove(Ze),t._element.classList.add(Xe),p(t._element).trigger(Ye.SHOWN),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))};if(this._element.classList.remove("hide"),g.reflow(this._element),this._element.classList.add(Ze),this._config.animation){var n=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,i).emulateTransitionEnd(n)}else i()}},Ue.hide=function(){if(this._element.classList.contains(Xe)){var t=p.Event(Ye.HIDE);p(this._element).trigger(t),t.isDefaultPrevented()||this._close()}},Ue.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(Xe)&&this._element.classList.remove(Xe),p(this._element).off(Ye.CLICK_DISMISS),p.removeData(this._element,Qe),this._element=null,this._config=null},Ue._getConfig=function(t){return t=r({},Je,{},p(this._element).data(),{},"object"==typeof t&&t?t:{}),g.typeCheckConfig(qe,t,this.constructor.DefaultType),t},Ue._setListeners=function(){var t=this;p(this._element).on(Ye.CLICK_DISMISS,'[data-dismiss="toast"]',function(){return t.hide()})},Ue._close=function(){function t(){e._element.classList.add("hide"),p(e._element).trigger(Ye.HIDDEN)}var e=this;if(this._element.classList.remove(Xe),this._config.animation){var i=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,t).emulateTransitionEnd(i)}else t()},ei._jQueryInterface=function(i){return this.each(function(){var t=p(this),e=t.data(Qe);if(e||(e=new ei(this,"object"==typeof i&&i),t.data(Qe,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},o(ei,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"DefaultType",get:function(){return Ge}},{key:"Default",get:function(){return Je}}]),ei);function ei(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}p.fn[qe]=ti._jQueryInterface,p.fn[qe].Constructor=ti,p.fn[qe].noConflict=function(){return p.fn[qe]=Ve,ti._jQueryInterface},t.Alert=f,t.Button=A,t.Carousel=U,t.Collapse=ot,t.Dropdown=Et,t.Modal=zt,t.Popover=ve,t.Scrollspy=ke,t.Tab=Fe,t.Toast=ti,t.Tooltip=ae,t.Util=g,Object.defineProperty(t,"__esModule",{value:!0})}),function(h){"use strict";h.ajaxChimp={responses:{"We have sent you a confirmation email":0,"Please enter a value":1,"An email address must contain a single @":2,"The domain portion of the email address is invalid (the portion after the @: )":3,"The username portion of the email address is invalid (the portion before the @: )":4,"This email address looks fake or invalid. Please enter a real email address":5},translations:{en:null},init:function(t,e){h(t).ajaxChimp(e)}},h.fn.ajaxChimp=function(i){return h(this).each(function(t,e){var s=h(e),o=s.find("input[type=email]"),r=s.find("label[for="+o.attr("id")+"]"),a=h.extend({url:s.attr("action"),language:"en"},i),l=a.url.replace("/post?","/post-json?").concat("&c=?");s.attr("novalidate","true"),o.attr("name","EMAIL"),s.submit(function(){var n;var i={},t=s.serializeArray();h.each(t,function(t,e){i[e.name]=e.value}),h.ajax({url:l,data:i,success:function(e){if("success"===e.result)n="We have sent you a confirmation email",r.removeClass("error").addClass("valid"),o.removeClass("error").addClass("valid");else{o.removeClass("valid").addClass("error"),r.removeClass("valid").addClass("error");try{var t=e.msg.split(" - ",2);if(void 0===t[1])n=e.msg;else{var i=parseInt(t[0],10);n=i.toString()===t[0]?(t[0],t[1]):e.msg}}catch(t){n=e.msg}}"en"!==a.language&&void 0!==h.ajaxChimp.responses[n]&&h.ajaxChimp.translations&&h.ajaxChimp.translations[a.language]&&h.ajaxChimp.translations[a.language][h.ajaxChimp.responses[n]]&&(n=h.ajaxChimp.translations[a.language][h.ajaxChimp.responses[n]]),r.html(n),r.show(2e3),a.callback&&a.callback(e)},dataType:"jsonp",error:function(t,e){console.log("mailchimp ajax submit error: "+e)}});var e="Submitting...";return"en"!==a.language&&h.ajaxChimp.translations&&h.ajaxChimp.translations[a.language]&&h.ajaxChimp.translations[a.language].submit&&(e=h.ajaxChimp.translations[a.language].submit),r.html(e).show(2e3),!1})}),this}}(jQuery),function(l,i,s,a){function h(t,e){this.settings=null,this.options=l.extend({},h.Defaults,e),this.$element=l(t),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},l.each(["onResize","onThrottledResize"],l.proxy(function(t,e){this._handlers[e]=l.proxy(this[e],this)},this)),l.each(h.Plugins,l.proxy(function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)},this)),l.each(h.Workers,l.proxy(function(t,e){this._pipe.push({filter:e.filter,run:l.proxy(e.run,this)})},this)),this.setup(),this.initialize()}h.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:i,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},h.Width={Default:"default",Inner:"inner",Outer:"outer"},h.Type={Event:"event",State:"state"},h.Plugins={},h.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",i=!this.settings.autoWidth,n=this.settings.rtl,s={width:"auto","margin-left":n?e:"","margin-right":n?"":e};i||this.$stage.children().css(s),t.css=s}},{filter:["width","items","settings"],run:function(t){var e=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,i=null,n=this._items.length,s=!this.settings.autoWidth,o=[];for(t.items={merge:!1,width:e};n--;)i=this._mergers[n],i=this.settings.mergeFit&&Math.min(i,this.settings.items)||i,t.items.merge=1<i||t.items.merge,o[n]=s?e*i:this._items[n].width();this._widths=o}},{filter:["items","settings"],run:function(){var t=[],e=this._items,i=this.settings,n=Math.max(2*i.items,4),s=2*Math.ceil(e.length/2),o=i.loop&&e.length?i.rewind?n:Math.max(n,s):0,r="",a="";for(o/=2;0<o;)t.push(this.normalize(t.length/2,!0)),r+=e[t[t.length-1]][0].outerHTML,t.push(this.normalize(e.length-1-(t.length-1)/2,!0)),a=e[t[t.length-1]][0].outerHTML+a,--o;this._clones=t,l(r).addClass("cloned").appendTo(this.$stage),l(a).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t=this.settings.rtl?1:-1,e=this._clones.length+this._items.length,i=-1,n=0,s=0,o=[];++i<e;)n=o[i-1]||0,s=this._widths[this.relative(i)]+this.settings.margin,o.push(n+s*t);this._coordinates=o}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,i={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(i)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,i=!this.settings.autoWidth,n=this.$stage.children();if(i&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],n.eq(e).css(t.css);else i&&(t.css.width=t.items.width,n.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){var t,e,i,n,s=this.settings.rtl?1:-1,o=2*this.settings.stagePadding,r=this.coordinates(this.current())+o,a=r+this.width()*s,l=[];for(i=0,n=this._coordinates.length;i<n;i++)t=this._coordinates[i-1]||0,e=Math.abs(this._coordinates[i])+o*s,(this.op(t,"<=",r)&&this.op(t,">",a)||this.op(e,"<",r)&&this.op(e,">",a))&&l.push(i);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+l.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],h.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=l("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(l("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},h.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");if(t.length)return this._items=t.get().map(function(t){return l(t)}),this._mergers=this._items.map(function(){return 1}),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)},h.prototype.initialize=function(){var t,e,i;this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:a,i=this.$element.children(e).width(),t.length&&i<=0&&this.preloadAutoWidthImages(t));this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},h.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},h.prototype.setup=function(){var e=this.viewport(),t=this.options.responsive,i=-1,n=null;t?(l.each(t,function(t){t<=e&&i<t&&(i=Number(t))}),"function"==typeof(n=l.extend({},this.options,t[i])).stagePadding&&(n.stagePadding=n.stagePadding()),delete n.responsive,n.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):n=l.extend({},this.options),this.trigger("change",{property:{name:"settings",value:n}}),this._breakpoint=i,this.settings=n,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},h.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},h.prototype.prepare=function(t){var e=this.trigger("prepare",{content:t});return e.data||(e.data=l("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:e.data}),e.data},h.prototype.update=function(){for(var t=0,e=this._pipe.length,i=l.proxy(function(t){return this[t]},this._invalidated),n={};t<e;)(this._invalidated.all||0<l.grep(this._pipe[t].filter,i).length)&&this._pipe[t].run(n),t++;this._invalidated={},this.is("valid")||this.enter("valid")},h.prototype.width=function(t){switch(t=t||h.Width.Default){case h.Width.Inner:case h.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},h.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},h.prototype.onThrottledResize=function(){i.clearTimeout(this.resizeTimer),this.resizeTimer=i.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},h.prototype.onResize=function(){return!!this._items.length&&this._width!==this.$element.width()&&!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))},h.prototype.registerEventHandlers=function(){l.support.transition&&this.$stage.on(l.support.transition.end+".owl.core",l.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(i,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1})),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",l.proxy(this.onDragEnd,this)))},h.prototype.onDragStart=function(t){var e=null;3!==t.which&&(e=l.support.transform?{x:(e=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===e.length?12:4],y:e[16===e.length?13:5]}:(e=this.$stage.position(),{x:this.settings.rtl?e.left+this.$stage.width()-this.width()+this.settings.margin:e.left,y:e.top}),this.is("animating")&&(l.support.transform?this.animate(e.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=l(t.target),this._drag.stage.start=e,this._drag.stage.current=e,this._drag.pointer=this.pointer(t),l(s).on("mouseup.owl.core touchend.owl.core",l.proxy(this.onDragEnd,this)),l(s).one("mousemove.owl.core touchmove.owl.core",l.proxy(function(t){var e=this.difference(this._drag.pointer,this.pointer(t));l(s).on("mousemove.owl.core touchmove.owl.core",l.proxy(this.onDragMove,this)),Math.abs(e.x)<Math.abs(e.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))},h.prototype.onDragMove=function(t){var e=null,i=null,n=null,s=this.difference(this._drag.pointer,this.pointer(t)),o=this.difference(this._drag.stage.start,s);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-e,o.x=((o.x-e)%i+i)%i+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),n=this.settings.pullDrag?-1*s.x/5:0,o.x=Math.max(Math.min(o.x,e+n),i+n)),this._drag.stage.current=o,this.animate(o.x))},h.prototype.onDragEnd=function(t){var e=this.difference(this._drag.pointer,this.pointer(t)),i=this._drag.stage.current,n=0<e.x^this.settings.rtl?"left":"right";l(s).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==e.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(i.x,0!==e.x?n:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=n,(3<Math.abs(e.x)||300<(new Date).getTime()-this._drag.time)&&this._drag.target.one("click.owl.core",function(){return!1})),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},h.prototype.closest=function(i,n){var s=-1,o=this.width(),r=this.coordinates();return this.settings.freeDrag||l.each(r,l.proxy(function(t,e){return"left"===n&&e-30<i&&i<e+30?s=t:"right"===n&&e-o-30<i&&i<e-o+30?s=t+1:this.op(i,"<",e)&&this.op(i,">",r[t+1]!==a?r[t+1]:e-o)&&(s="left"===n?t+1:t),-1===s},this)),this.settings.loop||(this.op(i,">",r[this.minimum()])?s=i=this.minimum():this.op(i,"<",r[this.maximum()])&&(s=i=this.maximum())),s},h.prototype.animate=function(t){var e=0<this.speed();this.is("animating")&&this.onTransitionEnd(),e&&(this.enter("animating"),this.trigger("translate")),l.support.transform3d&&l.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):e?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,l.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})},h.prototype.is=function(t){return this._states.current[t]&&0<this._states.current[t]},h.prototype.current=function(t){if(t===a)return this._current;if(0===this._items.length)return a;if(t=this.normalize(t),this._current!==t){var e=this.trigger("change",{property:{name:"position",value:t}});e.data!==a&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current},h.prototype.invalidate=function(t){return"string"===l.type(t)&&(this._invalidated[t]=!0,this.is("valid")&&this.leave("valid")),l.map(this._invalidated,function(t,e){return e})},h.prototype.reset=function(t){(t=this.normalize(t))!==a&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},h.prototype.normalize=function(t,e){var i=this._items.length,n=e?0:this._clones.length;return!this.isNumeric(t)||i<1?t=a:(t<0||i+n<=t)&&(t=((t-n/2)%i+i)%i+n/2),t},h.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},h.prototype.maximum=function(t){var e,i,n,s=this.settings,o=this._coordinates.length;if(s.loop)o=this._clones.length/2+this._items.length-1;else if(s.autoWidth||s.merge){if(e=this._items.length)for(i=this._items[--e].width(),n=this.$element.width();e--&&!((i+=this._items[e].width()+this.settings.margin)>n););o=e+1}else o=s.center?this._items.length-1:this._items.length-s.items;return t&&(o-=this._clones.length/2),Math.max(o,0)},h.prototype.minimum=function(t){return t?0:this._clones.length/2},h.prototype.items=function(t){return t===a?this._items.slice():(t=this.normalize(t,!0),this._items[t])},h.prototype.mergers=function(t){return t===a?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},h.prototype.clones=function(i){function n(t){return t%2==0?s+t/2:e-(t+1)/2}var e=this._clones.length/2,s=e+this._items.length;return i===a?l.map(this._clones,function(t,e){return n(e)}):l.map(this._clones,function(t,e){return t===i?n(e):null})},h.prototype.speed=function(t){return t!==a&&(this._speed=t),this._speed},h.prototype.coordinates=function(t){var e,i=1,n=t-1;return t===a?l.map(this._coordinates,l.proxy(function(t,e){return this.coordinates(e)},this)):(this.settings.center?(this.settings.rtl&&(i=-1,n=t+1),e=this._coordinates[t],e+=(this.width()-e+(this._coordinates[n]||0))/2*i):e=this._coordinates[n]||0,e=Math.ceil(e))},h.prototype.duration=function(t,e,i){return 0===i?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(i||this.settings.smartSpeed)},h.prototype.to=function(t,e){var i=this.current(),n=null,s=t-this.relative(i),o=(0<s)-(s<0),r=this._items.length,a=this.minimum(),l=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(s)>r/2&&(s+=-1*o*r),(n=(((t=i+s)-a)%r+r)%r+a)!==t&&n-s<=l&&0<n-s&&(i=n-s,t=n,this.reset(i))):t=this.settings.rewind?(t%(l+=1)+l)%l:Math.max(a,Math.min(l,t)),this.speed(this.duration(i,t,e)),this.current(t),this.isVisible()&&this.update()},h.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},h.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},h.prototype.onTransitionEnd=function(t){if(t!==a&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},h.prototype.viewport=function(){var t;return this.options.responsiveBaseElement!==i?t=l(this.options.responsiveBaseElement).width():i.innerWidth?t=i.innerWidth:s.documentElement&&s.documentElement.clientWidth?t=s.documentElement.clientWidth:console.warn("Can not detect viewport width."),t},h.prototype.replace=function(t){this.$stage.empty(),this._items=[],t=t&&(t instanceof jQuery?t:l(t)),this.settings.nestedItemSelector&&(t=t.find("."+this.settings.nestedItemSelector)),t.filter(function(){return 1===this.nodeType}).each(l.proxy(function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(+e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},h.prototype.add=function(t,e){var i=this.relative(this._current);e=e===a?this._items.length:this.normalize(e,!0),t=t instanceof jQuery?t:l(t),this.trigger("add",{content:t,position:e}),t=this.prepare(t),0===this._items.length||e===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[e-1].after(t),this._items.push(t),this._mergers.push(+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[e].before(t),this._items.splice(e,0,t),this._mergers.splice(e,0,+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[i]&&this.reset(this._items[i].index()),this.invalidate("items"),this.trigger("added",{content:t,position:e})},h.prototype.remove=function(t){(t=this.normalize(t,!0))!==a&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},h.prototype.preloadAutoWidthImages=function(t){t.each(l.proxy(function(t,e){this.enter("pre-loading"),e=l(e),l(new Image).one("load",l.proxy(function(t){e.attr("src",t.target.src),e.css("opacity",1),this.leave("pre-loading"),this.is("pre-loading")||this.is("initializing")||this.refresh()},this)).attr("src",e.attr("src")||e.attr("data-src")||e.attr("data-src-retina"))},this))},h.prototype.destroy=function(){for(var t in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),l(s).off(".owl.core"),!1!==this.settings.responsive&&(i.clearTimeout(this.resizeTimer),this.off(i,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[t].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},h.prototype.op=function(t,e,i){var n=this.settings.rtl;switch(e){case"<":return n?i<t:t<i;case">":return n?t<i:i<t;case">=":return n?t<=i:i<=t;case"<=":return n?i<=t:t<=i}},h.prototype.on=function(t,e,i,n){t.addEventListener?t.addEventListener(e,i,n):t.attachEvent&&t.attachEvent("on"+e,i)},h.prototype.off=function(t,e,i,n){t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent&&t.detachEvent("on"+e,i)},h.prototype.trigger=function(t,e,i,n,s){var o={item:{count:this._items.length,index:this.current()}},r=l.camelCase(l.grep(["on",t,i],function(t){return t}).join("-").toLowerCase()),a=l.Event([t,"owl",i||"carousel"].join(".").toLowerCase(),l.extend({relatedTarget:this},o,e));return this._supress[t]||(l.each(this._plugins,function(t,e){e.onTrigger&&e.onTrigger(a)}),this.register({type:h.Type.Event,name:t}),this.$element.trigger(a),this.settings&&"function"==typeof this.settings[r]&&this.settings[r].call(this,a)),a},h.prototype.enter=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]===a&&(this._states.current[e]=0),this._states.current[e]++},this))},h.prototype.leave=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]--},this))},h.prototype.register=function(i){if(i.type===h.Type.Event){if(l.event.special[i.name]||(l.event.special[i.name]={}),!l.event.special[i.name].owl){var e=l.event.special[i.name]._default;l.event.special[i.name]._default=function(t){return!e||!e.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&-1<t.namespace.indexOf("owl"):e.apply(this,arguments)},l.event.special[i.name].owl=!0}}else i.type===h.Type.State&&(this._states.tags[i.name]?this._states.tags[i.name]=this._states.tags[i.name].concat(i.tags):this._states.tags[i.name]=i.tags,this._states.tags[i.name]=l.grep(this._states.tags[i.name],l.proxy(function(t,e){return l.inArray(t,this._states.tags[i.name])===e},this)))},h.prototype.suppress=function(t){l.each(t,l.proxy(function(t,e){this._supress[e]=!0},this))},h.prototype.release=function(t){l.each(t,l.proxy(function(t,e){delete this._supress[e]},this))},h.prototype.pointer=function(t){var e={x:null,y:null};return(t=(t=t.originalEvent||t||i.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(e.x=t.pageX,e.y=t.pageY):(e.x=t.clientX,e.y=t.clientY),e},h.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},h.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},l.fn.owlCarousel=function(e){var n=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=l(this),i=t.data("owl.carousel");i||(i=new h(this,"object"==typeof e&&e),t.data("owl.carousel",i),l.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,e){i.register({type:h.Type.Event,name:e}),i.$element.on(e+".owl.carousel.core",l.proxy(function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([e]),i[e].apply(this,[].slice.call(arguments,1)),this.release([e]))},i))})),"string"==typeof e&&"_"!==e.charAt(0)&&i[e].apply(i,n)})},l.fn.owlCarousel.Constructor=h}(window.Zepto||window.jQuery,window,document),function(e,i){var n=function(t){this._core=t,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":e.proxy(function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()},this)},this._core.options=e.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers)};n.Defaults={autoRefresh:!0,autoRefreshInterval:500},n.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=i.setInterval(e.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},n.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())},n.prototype.destroy=function(){var t,e;for(t in i.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},e.fn.owlCarousel.Constructor.Plugins.AutoRefresh=n}(window.Zepto||window.jQuery,window,document),function(a,o){var e=function(t){this._core=t,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":a.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var e=this._core.settings,i=e.center&&Math.ceil(e.items/2)||e.items,n=e.center&&-1*i||0,s=(t.property&&void 0!==t.property.value?t.property.value:this._core.current())+n,o=this._core.clones().length,r=a.proxy(function(t,e){this.load(e)},this);for(0<e.lazyLoadEager&&(i+=e.lazyLoadEager,e.loop&&(s-=e.lazyLoadEager,i++));n++<i;)this.load(o/2+this._core.relative(s)),o&&a.each(this._core.clones(this._core.relative(s)),r),s++}},this)},this._core.options=a.extend({},e.Defaults,this._core.options),this._core.$element.on(this._handlers)};e.Defaults={lazyLoad:!1,lazyLoadEager:0},e.prototype.load=function(t){var e=this._core.$stage.children().eq(t),i=e&&e.find(".owl-lazy");!i||-1<a.inArray(e.get(0),this._loaded)||(i.each(a.proxy(function(t,e){var i,n=a(e),s=1<o.devicePixelRatio&&n.attr("data-src-retina")||n.attr("data-src")||n.attr("data-srcset");this._core.trigger("load",{element:n,url:s},"lazy"),n.is("img")?n.one("load.owl.lazy",a.proxy(function(){n.css("opacity",1),this._core.trigger("loaded",{element:n,url:s},"lazy")},this)).attr("src",s):n.is("source")?n.one("load.owl.lazy",a.proxy(function(){this._core.trigger("loaded",{element:n,url:s},"lazy")},this)).attr("srcset",s):((i=new Image).onload=a.proxy(function(){n.css({"background-image":'url("'+s+'")',opacity:"1"}),this._core.trigger("loaded",{element:n,url:s},"lazy")},this),i.src=s)},this)),this._loaded.push(e.get(0)))},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=e}(window.Zepto||window.jQuery,window,document),function(r,i){var n=function(t){this._core=t,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()},this),"loaded.owl.lazy":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)},this._core.options=r.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var e=this;r(i).on("load",function(){e._core.settings.autoHeight&&e.update()}),r(i).resize(function(){e._core.settings.autoHeight&&(null!=e._intervalId&&clearTimeout(e._intervalId),e._intervalId=setTimeout(function(){e.update()},250))})};n.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},n.prototype.update=function(){var t=this._core._current,e=t+this._core.settings.items,i=this._core.settings.lazyLoad,n=this._core.$stage.children().toArray().slice(t,e),s=[],o=0;r.each(n,function(t,e){s.push(r(e).height())}),(o=Math.max.apply(null,s))<=1&&i&&this._previousHeight&&(o=this._previousHeight),this._previousHeight=o,this._core.$stage.parent().height(o).addClass(this._core.settings.autoHeightClass)},n.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},r.fn.owlCarousel.Constructor.Plugins.AutoHeight=n}(window.Zepto||window.jQuery,window,document),function(c,e){var i=function(t){this._core=t,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":c.proxy(function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":c.proxy(function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()},this),"refreshed.owl.carousel":c.proxy(function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":c.proxy(function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":c.proxy(function(t){if(t.namespace){var e=c(t.content).find(".owl-video");e.length&&(e.css("display","none"),this.fetch(e,c(t.content)))}},this)},this._core.options=c.extend({},i.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",c.proxy(function(t){this.play(t)},this))};i.Defaults={video:!1,videoHeight:!1,videoWidth:!1},i.prototype.fetch=function(t,e){var i=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",n=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),s=t.attr("data-width")||this._core.settings.videoWidth,o=t.attr("data-height")||this._core.settings.videoHeight,r=t.attr("href");if(!r)throw new Error("Missing video URL.");if(-1<(n=r.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu"))i="youtube";else if(-1<n[3].indexOf("vimeo"))i="vimeo";else{if(!(-1<n[3].indexOf("vzaar")))throw new Error("Video URL not supported.");i="vzaar"}n=n[6],this._videos[r]={type:i,id:n,width:s,height:o},e.attr("data-video",r),this.thumbnail(t,this._videos[r])},i.prototype.thumbnail=function(e,t){function i(t){n=h.lazyLoad?c("<div/>",{class:"owl-video-tn "+l,srcType:t}):c("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+t+")"}),e.after(n),e.after('<div class="owl-video-play-icon"></div>')}var n,s,o=t.width&&t.height?"width:"+t.width+"px;height:"+t.height+"px;":"",r=e.find("img"),a="src",l="",h=this._core.settings;if(e.wrap(c("<div/>",{class:"owl-video-wrapper",style:o})),this._core.settings.lazyLoad&&(a="data-src",l="owl-lazy"),r.length)return i(r.attr(a)),r.remove(),!1;"youtube"===t.type?(s="//img.youtube.com/vi/"+t.id+"/hqdefault.jpg",i(s)):"vimeo"===t.type?c.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){s=t[0].thumbnail_large,i(s)}}):"vzaar"===t.type&&c.ajax({type:"GET",url:"//vzaar.com/api/videos/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){s=t.framegrab_url,i(s)}})},i.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},i.prototype.play=function(t){var e,i=c(t.target).closest("."+this._core.settings.itemClass),n=this._videos[i.attr("data-video")],s=n.width||"100%",o=n.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),i=this._core.items(this._core.relative(i.index())),this._core.reset(i.index()),(e=c('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",o),e.attr("width",s),"youtube"===n.type?e.attr("src","//www.youtube.com/embed/"+n.id+"?autoplay=1&rel=0&v="+n.id):"vimeo"===n.type?e.attr("src","//player.vimeo.com/video/"+n.id+"?autoplay=1"):"vzaar"===n.type&&e.attr("src","//view.vzaar.com/"+n.id+"/player?autoplay=true"),c(e).wrap('<div class="owl-video-frame" />').insertAfter(i.find(".owl-video")),this._playing=i.addClass("owl-video-playing"))},i.prototype.isInFullScreen=function(){var t=e.fullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement;return t&&c(t).parent().hasClass("owl-video-frame")},i.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},c.fn.owlCarousel.Constructor.Plugins.Video=i}(window.Zepto||window.jQuery,(window,document)),function(r){var e=function(t){this.core=t,this.core.options=r.extend({},e.Defaults,this.core.options),this.swapping=!0,this.previous=void 0,this.next=void 0,this.handlers={"change.owl.carousel":r.proxy(function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":r.proxy(function(t){t.namespace&&(this.swapping="translated"==t.type)},this),"translate.owl.carousel":r.proxy(function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)};e.Defaults={animateOut:!1,animateIn:!1},e.prototype.swap=function(){if(1===this.core.settings.items&&r.support.animation&&r.support.transition){this.core.speed(0);var t,e=r.proxy(this.clear,this),i=this.core.$stage.children().eq(this.previous),n=this.core.$stage.children().eq(this.next),s=this.core.settings.animateIn,o=this.core.settings.animateOut;this.core.current()!==this.previous&&(o&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),i.one(r.support.animation.end,e).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(o)),s&&n.one(r.support.animation.end,e).addClass("animated owl-animated-in").addClass(s))}},e.prototype.clear=function(t){r(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},r.fn.owlCarousel.Constructor.Plugins.Animate=e}(window.Zepto||window.jQuery,(window,document)),function(n,s,e){var i=function(t){this._core=t,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":n.proxy(function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(t,e,i){t.namespace&&this.play(e,i)},this),"stop.owl.autoplay":n.proxy(function(t){t.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)},this._core.$element.on(this._handlers),this._core.options=n.extend({},i.Defaults,this._core.options)};i.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},i.prototype._next=function(t){this._call=s.setTimeout(n.proxy(this._next,this,t),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||e.hidden||this._core.next(t||this._core.settings.autoplaySpeed)},i.prototype.read=function(){return(new Date).getTime()-this._time},i.prototype.play=function(t,e){var i;this._core.is("rotating")||this._core.enter("rotating"),t=t||this._core.settings.autoplayTimeout,i=Math.min(this._time%(this._timeout||t),t),this._paused?(this._time=this.read(),this._paused=!1):s.clearTimeout(this._call),this._time+=this.read()%t-i,this._timeout=t,this._call=s.setTimeout(n.proxy(this._next,this,e),t-i)},i.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,s.clearTimeout(this._call),this._core.leave("rotating"))},i.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,s.clearTimeout(this._call))},i.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.autoplay=i}(window.Zepto||window.jQuery,window,document),function(o){"use strict";var e=function(t){this._core=t,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+o(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")},this),"added.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())},this),"remove.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&"position"==t.property.name&&this.draw()},this),"initialized.owl.carousel":o.proxy(function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)},this._core.options=o.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers)};e.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},e.prototype.initialize=function(){var t,i=this._core.settings;for(t in this._controls.$relative=(i.navContainer?o(i.navContainer):o("<div>").addClass(i.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=o("<"+i.navElement+">").addClass(i.navClass[0]).html(i.navText[0]).prependTo(this._controls.$relative).on("click",o.proxy(function(t){this.prev(i.navSpeed)},this)),this._controls.$next=o("<"+i.navElement+">").addClass(i.navClass[1]).html(i.navText[1]).appendTo(this._controls.$relative).on("click",o.proxy(function(t){this.next(i.navSpeed)},this)),i.dotsData||(this._templates=[o('<button role="button">').addClass(i.dotClass).append(o("<span>")).prop("outerHTML")]),this._controls.$absolute=(i.dotsContainer?o(i.dotsContainer):o("<div>").addClass(i.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",o.proxy(function(t){var e=o(t.target).parent().is(this._controls.$absolute)?o(t.target).index():o(t.target).parent().index();t.preventDefault(),this.to(e,i.dotsSpeed)},this)),this._overrides)this._core[t]=o.proxy(this[t],this)},e.prototype.destroy=function(){var t,e,i,n,s;for(t in s=this._core.settings,this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&s.navContainer?this._controls[e].html(""):this._controls[e].remove();for(n in this.overides)this._core[n]=this._overrides[n];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},e.prototype.update=function(){var t,e,i=this._core.clones().length/2,n=i+this._core.items().length,s=this._core.maximum(!0),o=this._core.settings,r=o.center||o.autoWidth||o.dotsData?1:o.dotsEach||o.items;if("page"!==o.slideBy&&(o.slideBy=Math.min(o.slideBy,o.items)),o.dots||"page"==o.slideBy)for(this._pages=[],t=i,e=0;t<n;t++){if(r<=e||0===e){if(this._pages.push({start:Math.min(s,t-i),end:t-i+r-1}),Math.min(s,t-i)===s)break;e=0,0}e+=this._core.mergers(this._core.relative(t))}},e.prototype.draw=function(){var t,e=this._core.settings,i=this._core.items().length<=e.items,n=this._core.relative(this._core.current()),s=e.loop||e.rewind;this._controls.$relative.toggleClass("disabled",!e.nav||i),e.nav&&(this._controls.$previous.toggleClass("disabled",!s&&n<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!s&&n>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!e.dots||i),e.dots&&(t=this._pages.length-this._controls.$absolute.children().length,e.dotsData&&0!=t?this._controls.$absolute.html(this._templates.join("")):0<t?this._controls.$absolute.append(new Array(1+t).join(this._templates[0])):t<0&&this._controls.$absolute.children().slice(t).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(o.inArray(this.current(),this._pages)).addClass("active"))},e.prototype.onTrigger=function(t){var e=this._core.settings;t.page={index:o.inArray(this.current(),this._pages),count:this._pages.length,size:e&&(e.center||e.autoWidth||e.dotsData?1:e.dotsEach||e.items)}},e.prototype.current=function(){var i=this._core.relative(this._core.current());return o.grep(this._pages,o.proxy(function(t,e){return t.start<=i&&t.end>=i},this)).pop()},e.prototype.getPosition=function(t){var e,i,n=this._core.settings;return"page"==n.slideBy?(e=o.inArray(this.current(),this._pages),i=this._pages.length,t?++e:--e,e=this._pages[(e%i+i)%i].start):(e=this._core.relative(this._core.current()),i=this._core.items().length,t?e+=n.slideBy:e-=n.slideBy),e},e.prototype.next=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)},e.prototype.prev=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)},e.prototype.to=function(t,e,i){var n;!i&&this._pages.length?(n=this._pages.length,o.proxy(this._overrides.to,this._core)(this._pages[(t%n+n)%n].start,e)):o.proxy(this._overrides.to,this._core)(t,e)},o.fn.owlCarousel.Constructor.Plugins.Navigation=e}(window.Zepto||window.jQuery,(window,document)),function(n,s){"use strict";var e=function(t){this._core=t,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":n.proxy(function(t){t.namespace&&"URLHash"===this._core.settings.startPosition&&n(s).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var e=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!e)return;this._hashes[e]=t.content}},this),"changed.owl.carousel":n.proxy(function(t){if(t.namespace&&"position"===t.property.name){var i=this._core.items(this._core.relative(this._core.current())),e=n.map(this._hashes,function(t,e){return t===i?e:null}).join();if(!e||s.location.hash.slice(1)===e)return;s.location.hash=e}},this)},this._core.options=n.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers),n(s).on("hashchange.owl.navigation",n.proxy(function(t){var e=s.location.hash.substring(1),i=this._core.$stage.children(),n=this._hashes[e]&&i.index(this._hashes[e]);void 0!==n&&n!==this._core.current()&&this._core.to(this._core.relative(n),!1,!0)},this))};e.Defaults={URLhashListener:!1},e.prototype.destroy=function(){var t,e;for(t in n(s).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.Hash=e}(window.Zepto||window.jQuery,window,document),function(s,o){function e(t,i){var n=!1,e=t.charAt(0).toUpperCase()+t.slice(1);return s.each((t+" "+a.join(e+" ")+e).split(" "),function(t,e){if(r[e]!==o)return n=!i||e,!1}),n}function t(t){return e(t,!0)}var r=s("<support>").get(0).style,a="Webkit Moz O ms".split(" "),i={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},n=function(){return!!e("transform")},l=function(){return!!e("perspective")},h=function(){return!!e("animation")};!function(){return!!e("transition")}()||(s.support.transition=new String(t("transition")),s.support.transition.end=i.transition.end[s.support.transition]),h()&&(s.support.animation=new String(t("animation")),s.support.animation.end=i.animation.end[s.support.animation]),n()&&(s.support.transform=new String(t("transform")),s.support.transform3d=l())}(window.Zepto||window.jQuery,(window,void document)),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,function(){"use strict";function o(t){return t&&"[object Function]"==={}.toString.call(t)}function v(t,e){if(1!==t.nodeType)return[];var i=window.getComputedStyle(t,null);return e?i[e]:i}function y(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function w(t){if(!t||-1!==["HTML","BODY","#document"].indexOf(t.nodeName))return window.document.body;var e=v(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/(auto|scroll)/.test(i+s+n)?t:w(y(t))}function b(t){var e=t&&t.offsetParent,i=e&&e.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TD","TABLE"].indexOf(e.nodeName)&&"static"===v(e,"position")?b(e):e:window.document.documentElement}function c(t){return null===t.parentNode?t:c(t.parentNode)}function E(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return window.document.documentElement;var i=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,n=i?t:e,s=i?e:t,o=document.createRange();o.setStart(n,0),o.setEnd(s,0);var r,a,l=o.commonAncestorContainer;if(t!==l&&e!==l||n.contains(s))return"BODY"===(a=(r=l).nodeName)||"HTML"!==a&&b(r.firstElementChild)!==r?b(l):l;var h=c(t);return h.host?E(h.host,e):E(t,c(e).host)}function C(t,e){var i="top"===(1<arguments.length&&void 0!==e?e:"top")?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"!==n&&"HTML"!==n)return t[i];var s=window.document.documentElement;return(window.document.scrollingElement||s)[i]}function u(t,e){var i="x"===e?"Left":"Top",n="Left"==i?"Right":"Bottom";return+t["border"+i+"Width"].split("px")[0]+ +t["border"+n+"Width"].split("px")[0]}function n(t,e,i,n){return L(e["offset"+t],i["client"+t],i["offset"+t],B()?i["offset"+t]+n["margin"+("Height"===t?"Top":"Left")]+n["margin"+("Height"===t?"Bottom":"Right")]:0)}function T(){var t=window.document.body,e=window.document.documentElement,i=B()&&window.getComputedStyle(e);return{height:n("Height",t,e,i),width:n("Width",t,e,i)}}function S(t){return q({},t,{right:t.left+t.width,bottom:t.top+t.height})}function D(t){var e={};if(B())try{e=t.getBoundingClientRect();var i=C(t,"top"),n=C(t,"left");e.top+=i,e.left+=n,e.bottom+=i,e.right+=n}catch(t){}else e=t.getBoundingClientRect();var s={left:e.left,top:e.top,width:e.right-e.left,height:e.bottom-e.top},o="HTML"===t.nodeName?T():{},r=o.width||t.clientWidth||s.right-s.left,a=o.height||t.clientHeight||s.bottom-s.top,l=t.offsetWidth-r,h=t.offsetHeight-a;if(l||h){var c=v(t);l-=u(c,"x"),h-=u(c,"y"),s.width-=l,s.height-=h}return S(s)}function x(t,e){var i=B(),n="HTML"===e.nodeName,s=D(t),o=D(e),r=w(t),a=v(e),l=+a.borderTopWidth.split("px")[0],h=+a.borderLeftWidth.split("px")[0],c=S({top:s.top-o.top-l,left:s.left-o.left-h,width:s.width,height:s.height});if(c.marginTop=0,c.marginLeft=0,!i&&n){var u=+a.marginTop.split("px")[0],d=+a.marginLeft.split("px")[0];c.top-=l-u,c.bottom-=l-u,c.left-=h-d,c.right-=h-d,c.marginTop=u,c.marginLeft=d}return(i?e.contains(r):e===r&&"BODY"!==r.nodeName)&&(c=function(t,e,i){var n=2<arguments.length&&void 0!==i&&i,s=C(e,"top"),o=C(e,"left"),r=n?-1:1;return t.top+=s*r,t.bottom+=s*r,t.left+=o*r,t.right+=o*r,t}(c,e)),c}function f(t,e,i,n){var s,o,r,a,l,h,c,u={top:0,left:0},d=E(t,e);if("viewport"===n)s=d,o=window.document.documentElement,r=x(s,o),a=L(o.clientWidth,window.innerWidth||0),l=L(o.clientHeight,window.innerHeight||0),h=C(o),c=C(o,"left"),u=S({top:h-r.top+r.marginTop,left:c-r.left+r.marginLeft,width:a,height:l});else{var f;"scrollParent"===n?"BODY"===(f=w(y(t))).nodeName&&(f=window.document.documentElement):f="window"===n?window.document.documentElement:n;var p=x(f,d);if("HTML"!==f.nodeName||function t(e){var i=e.nodeName;return"BODY"!==i&&"HTML"!==i&&("fixed"===v(e,"position")||t(y(e)))}(d))u=p;else{var g=T(),m=g.height,_=g.width;u.top+=p.top-p.marginTop,u.bottom=m+p.top,u.left+=p.left-p.marginLeft,u.right=_+p.left}}return u.left+=i,u.top+=i,u.right-=i,u.bottom-=i,u}function a(t,e,n,i,s,o){var r=5<arguments.length&&void 0!==o?o:0;if(-1===t.indexOf("auto"))return t;var a=f(n,i,r,s),l={top:{width:a.width,height:e.top-a.top},right:{width:a.right-e.right,height:a.height},bottom:{width:a.width,height:a.bottom-e.bottom},left:{width:e.left-a.left,height:a.height}},h=Object.keys(l).map(function(t){return q({key:t},l[t],{area:(e=l[t]).width*e.height});var e}).sort(function(t,e){return e.area-t.area}),c=h.filter(function(t){var e=t.width,i=t.height;return e>=n.clientWidth&&i>=n.clientHeight}),u=0<c.length?c[0].key:h[0].key,d=t.split("-")[1];return u+(d?"-"+d:"")}function l(t,e,i){return x(i,E(e,i))}function p(t){var e=window.getComputedStyle(t),i=parseFloat(e.marginTop)+parseFloat(e.marginBottom),n=parseFloat(e.marginLeft)+parseFloat(e.marginRight);return{width:t.offsetWidth+n,height:t.offsetHeight+i}}function A(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function I(t,e,i){i=i.split("-")[0];var n=p(t),s={width:n.width,height:n.height},o=-1!==["right","left"].indexOf(i),r=o?"top":"left",a=o?"left":"top",l=o?"height":"width",h=o?"width":"height";return s[r]=e[r]+e[l]/2-n[l]/2,s[a]=i===a?e[a]-n[h]:e[A(a)],s}function O(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function N(t,i,e){return(void 0===e?t:t.slice(0,function(t,e,i){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===i});var n=O(t,function(t){return t[e]===i});return t.indexOf(n)}(t,"name",e))).forEach(function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var e=t.function||t.fn;t.enabled&&o(e)&&(i.offsets.popper=S(i.offsets.popper),i.offsets.reference=S(i.offsets.reference),i=e(i,t))}),i}function t(t,i){return t.some(function(t){var e=t.name;return t.enabled&&e===i})}function k(t){for(var e=[!1,"ms","Webkit","Moz","O"],i=t.charAt(0).toUpperCase()+t.slice(1),n=0;n<e.length-1;n++){var s=e[n],o=s?""+s+i:t;if(void 0!==window.document.body.style[o])return o}return null}function e(t,e,i,n){i.updateBound=n,window.addEventListener("resize",i.updateBound,{passive:!0});var s=w(t);return function t(e,i,n,s){var o="BODY"===e.nodeName,r=o?window:e;r.addEventListener(i,n,{passive:!0}),o||t(w(r.parentNode),i,n,s),s.push(r)}(s,"scroll",i.updateBound,i.scrollParents),i.scrollElement=s,i.eventsEnabled=!0,i}function i(){var e;this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=(this.reference,e=this.state,window.removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function d(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function h(i,n){Object.keys(n).forEach(function(t){var e="";-1!==["width","height","top","right","bottom","left"].indexOf(t)&&d(n[t])&&(e="px"),i.style[t]=n[t]+e})}function g(t,e,i){var n=O(t,function(t){return t.name===e}),s=!!n&&t.some(function(t){return t.name===i&&t.enabled&&t.order<n.order});if(!s){var o="`"+e+"`";console.warn("`"+i+"` modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return s}function s(t,e){var i=1<arguments.length&&void 0!==e&&e,n=K.indexOf(t),s=K.slice(n+1).concat(K.slice(0,n));return i?s.reverse():s}function m(t,s,o,e){var r=[0,0],a=-1!==["right","left"].indexOf(e),i=t.split(/(\+|\-)/).map(function(t){return t.trim()}),n=i.indexOf(O(i,function(t){return-1!==t.search(/,|\s/)}));i[n]&&-1===i[n].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,h=-1===n?[i]:[i.slice(0,n).concat([i[n].split(l)[0]]),[i[n].split(l)[1]].concat(i.slice(n+1))];return(h=h.map(function(t,e){var i=(1===e?!a:a)?"height":"width",n=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,n=!0,t):n?(t[t.length-1]+=e,n=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,i,n){var s,o=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],a=o[2];if(!r)return t;if(0!==a.indexOf("%"))return"vh"!==a&&"vw"!==a?r:("vh"===a?L(document.documentElement.clientHeight,window.innerHeight||0):L(document.documentElement.clientWidth,window.innerWidth||0))/100*r;switch(a){case"%p":s=i;break;case"%":case"%r":default:s=n}return S(s)[e]/100*r}(t,i,s,o)})})).forEach(function(i,n){i.forEach(function(t,e){d(t)&&(r[n]+=t*("-"===i[e-1]?-1:1))})}),r}for(var _=Math.min,P=Math.floor,L=Math.max,r=["native code","[object MutationObserverConstructor]"],j="undefined"!=typeof window,H=["Edge","Trident","Firefox"],M=0,z=0;z<H.length;z+=1)if(j&&0<=navigator.userAgent.indexOf(H[z])){M=1;break}function $(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var R,W,F=j&&(W=window.MutationObserver,r.some(function(t){return-1<(W||"").toString().indexOf(t)}))?function(t){var e=!1,i=0,n=document.createElement("span");return new MutationObserver(function(){t(),e=!1}).observe(n,{attributes:!0}),function(){e||(e=!0,n.setAttribute("x-index",i),++i)}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},M))}},B=function(){return null==R&&(R=-1!==navigator.appVersion.indexOf("MSIE 10")),R},U=function(t,e,i){return e&&J(t.prototype,e),i&&J(t,i),t},q=Object.assign||function(t){for(var e,i=1;i<arguments.length;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},Q=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],K=Q.slice(3),V="flip",Y="clockwise",X="counterclockwise",Z=(U(G,[{key:"update",value:function(){return function(){if(!this.state.isDestroyed){var t={instance:this,styles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=l(this.state,this.popper,this.reference),t.placement=a(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.offsets.popper=I(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position="absolute",t=N(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,t(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.left="",this.popper.style.position="",this.popper.style.top="",this.popper.style[k("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=e(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return i.call(this)}}]),G);function G(t,e){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,G),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=F(this.update.bind(this)),this.options=q({},G.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t.jquery?t[0]:t,this.popper=e.jquery?e[0]:e,this.options.modifiers={},Object.keys(q({},G.Defaults.modifiers,n.modifiers)).forEach(function(t){i.options.modifiers[t]=q({},G.Defaults.modifiers[t]||{},n.modifiers?n.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return q({name:t},i.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(t){t.enabled&&o(t.onLoad)&&t.onLoad(i.reference,i.popper,i.options,t,i.state)}),this.update();var s=this.options.eventsEnabled;s&&this.enableEventListeners(),this.state.eventsEnabled=s}function J(t,e){for(var i,n=0;n<e.length;n++)(i=e[n]).enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}return Z.Utils=("undefined"==typeof window?global:window).PopperUtils,Z.placements=Q,Z.Defaults={placement:"bottom",eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,i=e.split("-")[0],n=e.split("-")[1];if(n){var s=t.offsets,o=s.reference,r=s.popper,a=-1!==["bottom","top"].indexOf(i),l=a?"left":"top",h=a?"width":"height",c={start:$({},l,o[l]),end:$({},l,o[l]+o[h]-r[h])};t.offsets.popper=q({},r,c[n])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var i,n=e.offset,s=t.placement,o=t.offsets,r=o.popper,a=o.reference,l=s.split("-")[0];return i=d(+n)?[+n,0]:m(n,r,a,l),"left"===l?(r.top+=i[0],r.left-=i[1]):"right"===l?(r.top+=i[0],r.left+=i[1]):"top"===l?(r.left+=i[0],r.top-=i[1]):"bottom"===l&&(r.left+=i[0],r.top+=i[1]),t.popper=r,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,n){var e=n.boundariesElement||b(t.instance.popper);t.instance.reference===e&&(e=b(e));var s=f(t.instance.popper,t.instance.reference,n.padding,e);n.boundaries=s;var i=n.priority,o=t.offsets.popper,r={primary:function(t){var e=o[t];return o[t]<s[t]&&!n.escapeWithReference&&(e=L(o[t],s[t])),$({},t,e)},secondary:function(t){var e="right"===t?"left":"top",i=o[e];return o[t]>s[t]&&!n.escapeWithReference&&(i=_(o[e],s[t]-("right"===t?o.width:o.height))),$({},e,i)}};return i.forEach(function(t){var e=-1===["left","top"].indexOf(t)?"secondary":"primary";o=q({},o,r[e](t))}),t.offsets.popper=o,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,i=e.popper,n=e.reference,s=t.placement.split("-")[0],o=P,r=-1!==["top","bottom"].indexOf(s),a=r?"right":"bottom",l=r?"left":"top",h=r?"width":"height";return i[a]<o(n[l])&&(t.offsets.popper[l]=o(n[l])-i[h]),i[l]>o(n[a])&&(t.offsets.popper[l]=o(n[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){if(!g(t.instance.modifiers,"arrow","keepTogether"))return t;var i=e.element;if("string"==typeof i){if(!(i=t.instance.popper.querySelector(i)))return t}else if(!t.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var n=t.placement.split("-")[0],s=t.offsets,o=s.popper,r=s.reference,a=-1!==["left","right"].indexOf(n),l=a?"height":"width",h=a?"top":"left",c=a?"left":"top",u=a?"bottom":"right",d=p(i)[l];r[u]-d<o[h]&&(t.offsets.popper[h]-=o[h]-(r[u]-d)),r[h]+d>o[u]&&(t.offsets.popper[h]+=r[h]+d-o[u]);var f=r[h]+r[l]/2-d/2-S(t.offsets.popper)[h];return f=L(_(o[l]-d,f),0),t.arrowElement=i,t.offsets.arrow={},t.offsets.arrow[h]=Math.round(f),t.offsets.arrow[c]="",t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(p,g){if(t(p.instance.modifiers,"inner"))return p;if(p.flipped&&p.placement===p.originalPlacement)return p;var m=f(p.instance.popper,p.instance.reference,g.padding,g.boundariesElement),_=p.placement.split("-")[0],v=A(_),y=p.placement.split("-")[1]||"",w=[];switch(g.behavior){case V:w=[_,v];break;case Y:w=s(_);break;case X:w=s(_,!0);break;default:w=g.behavior}return w.forEach(function(t,e){if(_!==t||w.length===e+1)return p;_=p.placement.split("-")[0],v=A(_);var i,n=p.offsets.popper,s=p.offsets.reference,o=P,r="left"===_&&o(n.right)>o(s.left)||"right"===_&&o(n.left)<o(s.right)||"top"===_&&o(n.bottom)>o(s.top)||"bottom"===_&&o(n.top)<o(s.bottom),a=o(n.left)<o(m.left),l=o(n.right)>o(m.right),h=o(n.top)<o(m.top),c=o(n.bottom)>o(m.bottom),u="left"===_&&a||"right"===_&&l||"top"===_&&h||"bottom"===_&&c,d=-1!==["top","bottom"].indexOf(_),f=!!g.flipVariations&&(d&&"start"===y&&a||d&&"end"===y&&l||!d&&"start"===y&&h||!d&&"end"===y&&c);(r||u||f)&&(p.flipped=!0,(r||u)&&(_=w[e+1]),f&&(y="end"===(i=y)?"start":"start"===i?"end":i),p.placement=_+(y?"-"+y:""),p.offsets.popper=q({},p.offsets.popper,I(p.instance.popper,p.offsets.reference,p.placement)),p=N(p.instance.modifiers,p,"flip"))}),p},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,i=e.split("-")[0],n=t.offsets,s=n.popper,o=n.reference,r=-1!==["left","right"].indexOf(i),a=-1===["top","left"].indexOf(i);return s[r?"left":"top"]=o[e]-(a?s[r?"width":"height"]:0),t.placement=A(e),t.offsets.popper=S(s),t}},hide:{order:800,enabled:!0,fn:function(t){if(!g(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,i=O(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<i.top||e.left>i.right||e.top>i.bottom||e.right<i.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var i=e.x,n=e.y,s=t.offsets.popper,o=O(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var r,a,l=void 0===o?e.gpuAcceleration:o,h=D(b(t.instance.popper)),c={position:s.position},u={left:P(s.left),top:P(s.top),bottom:P(s.bottom),right:P(s.right)},d="bottom"===i?"top":"bottom",f="right"===n?"left":"right",p=k("transform");if(a="bottom"==d?-h.height+u.bottom:u.top,r="right"==f?-h.width+u.right:u.left,l&&p)c[p]="translate3d("+r+"px, "+a+"px, 0)",c[d]=0,c[f]=0,c.willChange="transform";else{var g="bottom"==d?-1:1,m="right"==f?-1:1;c[d]=a*g,c[f]=r*m,c.willChange=d+", "+f}var _={"x-placement":t.placement};return t.attributes=q({},_,t.attributes),t.styles=q({},c,t.styles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){return h(t.instance.popper,t.styles),e=t.instance.popper,i=t.attributes,Object.keys(i).forEach(function(t){!1===i[t]?e.removeAttribute(t):e.setAttribute(t,i[t])}),t.offsets.arrow&&h(t.arrowElement,t.offsets.arrow),t;var e,i},onLoad:function(t,e,i,n,s){var o=l(0,e,t),r=a(i.placement,o,e,t,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return e.setAttribute("x-placement",r),h(e,{position:"absolute"}),i},gpuAcceleration:void 0}}},Z}),function(t,e){if("function"==typeof define&&define.amd)define(["module","exports"],e);else if("undefined"!=typeof exports)e(module,exports);else{var i={exports:{}};e(i,i.exports),t.WOW=i.exports}}(this,function(t,e){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){return 0<=e.indexOf(t)}function n(t,e,i){null!=t.addEventListener?t.addEventListener(e,i,!1):null!=t.attachEvent?t.attachEvent("on"+e,i):t[e]=i}function o(t,e,i){null!=t.removeEventListener?t.removeEventListener(e,i,!1):null!=t.detachEvent?t.detachEvent("on"+e,i):delete t[e]}Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e,i){return e&&p(t.prototype,e),i&&p(t,i),t},a=window.WeakMap||window.MozWeakMap||(r(f,[{key:"get",value:function(t){for(var e=0;e<this.keys.length;e++)if(this.keys[e]===t)return this.values[e]}},{key:"set",value:function(t,e){for(var i=0;i<this.keys.length;i++)if(this.keys[i]===t)return this.values[i]=e,this;return this.keys.push(t),this.values.push(e),this}}]),f),l=window.MutationObserver||window.WebkitMutationObserver||window.MozMutationObserver||(r(d,[{key:"observe",value:function(){}}]),d.notSupported=!0,d),h=window.getComputedStyle||function(i){var n=/(\-([a-z]){1})/g;return{getPropertyValue:function(t){"float"===t&&(t="styleFloat"),n.test(t)&&t.replace(n,function(t,e){return e.toUpperCase()});var e=i.currentStyle;return(null!=e?e[t]:void 0)||null}}},c=(r(u,[{key:"init",value:function(){this.element=window.document.documentElement,s(document.readyState,["interactive","complete"])?this.start():n(document,"DOMContentLoaded",this.start),this.finished=[]}},{key:"start",value:function(){var o=this;if(this.stopped=!1,this.boxes=[].slice.call(this.element.querySelectorAll("."+this.config.boxClass)),this.all=this.boxes.slice(0),this.boxes.length)if(this.disabled())this.resetStyle();else for(var t=0;t<this.boxes.length;t++){var e=this.boxes[t];this.applyStyle(e,!0)}this.disabled()||(n(this.config.scrollContainer||window,"scroll",this.scrollHandler),n(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live&&new l(function(t){for(var e=0;e<t.length;e++)for(var i=t[e],n=0;n<i.addedNodes.length;n++){var s=i.addedNodes[n];o.doSync(s)}}).observe(document.body,{childList:!0,subtree:!0})}},{key:"stop",value:function(){this.stopped=!0,o(this.config.scrollContainer||window,"scroll",this.scrollHandler),o(window,"resize",this.scrollHandler),null!=this.interval&&clearInterval(this.interval)}},{key:"sync",value:function(){l.notSupported&&this.doSync(this.element)}},{key:"doSync",value:function(t){if(null!=t||(t=this.element),1===t.nodeType)for(var e=(t=t.parentNode||t).querySelectorAll("."+this.config.boxClass),i=0;i<e.length;i++){var n=e[i];s(n,this.all)||(this.boxes.push(n),this.all.push(n),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(n,!0),this.scrolled=!0)}}},{key:"show",value:function(t){return this.applyStyle(t),t.className=t.className+" "+this.config.animateClass,null!=this.config.callback&&this.config.callback(t),e=t,i=this.wowEvent,null!=e.dispatchEvent?e.dispatchEvent(i):i in(null!=e)?e[i]():"on"+i in(null!=e)&&e["on"+i](),this.config.resetAnimation&&(n(t,"animationend",this.resetAnimation),n(t,"oanimationend",this.resetAnimation),n(t,"webkitAnimationEnd",this.resetAnimation),n(t,"MSAnimationEnd",this.resetAnimation)),t;var e,i}},{key:"applyStyle",value:function(t,e){var i=this,n=t.getAttribute("data-wow-duration"),s=t.getAttribute("data-wow-delay"),o=t.getAttribute("data-wow-iteration");return this.animate(function(){return i.customStyle(t,e,n,s,o)})}},{key:"resetStyle",value:function(){for(var t=0;t<this.boxes.length;t++)this.boxes[t].style.visibility="visible"}},{key:"resetAnimation",value:function(t){if(0<=t.type.toLowerCase().indexOf("animationend")){var e=t.target||t.srcElement;e.className=e.className.replace(this.config.animateClass,"").trim()}}},{key:"customStyle",value:function(t,e,i,n,s){return e&&this.cacheAnimationName(t),t.style.visibility=e?"hidden":"visible",i&&this.vendorSet(t.style,{animationDuration:i}),n&&this.vendorSet(t.style,{animationDelay:n}),s&&this.vendorSet(t.style,{animationIterationCount:s}),this.vendorSet(t.style,{animationName:e?"none":this.cachedAnimationName(t)}),t}},{key:"vendorSet",value:function(t,e){for(var i in e)if(e.hasOwnProperty(i)){var n=e[i];t[""+i]=n;for(var s=0;s<this.vendors.length;s++)t[""+this.vendors[s]+i.charAt(0).toUpperCase()+i.substr(1)]=n}}},{key:"vendorCSS",value:function(t,e){for(var i=h(t),n=i.getPropertyCSSValue(e),s=0;s<this.vendors.length;s++){var o=this.vendors[s];n=n||i.getPropertyCSSValue("-"+o+"-"+e)}return n}},{key:"animationName",value:function(e){var i=void 0;try{i=this.vendorCSS(e,"animation-name").cssText}catch(t){i=h(e).getPropertyValue("animation-name")}return"none"===i?"":i}},{key:"cacheAnimationName",value:function(t){return this.animationNameCache.set(t,this.animationName(t))}},{key:"cachedAnimationName",value:function(t){return this.animationNameCache.get(t)}},{key:"scrollHandler",value:function(){this.scrolled=!0}},{key:"scrollCallback",value:function(){if(this.scrolled){this.scrolled=!1;for(var t=[],e=0;e<this.boxes.length;e++){var i=this.boxes[e];if(i){if(this.isVisible(i)){this.show(i);continue}t.push(i)}}this.boxes=t,this.boxes.length||this.config.live||this.stop()}}},{key:"offsetTop",value:function(t){for(;void 0===t.offsetTop;)t=t.parentNode;for(var e=t.offsetTop;t.offsetParent;)e+=(t=t.offsetParent).offsetTop;return e}},{key:"isVisible",value:function(t){var e=t.getAttribute("data-wow-offset")||this.config.offset,i=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset,n=i+Math.min(this.element.clientHeight,"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight)-e,s=this.offsetTop(t),o=s+t.clientHeight;return s<=n&&i<=o}},{key:"disabled",value:function(){return!this.config.mobile&&(t=navigator.userAgent,/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t));var t}}]),u);function u(){var t=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];i(this,u),this.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null,resetAnimation:!0},this.animate="requestAnimationFrame"in window?function(t){return window.requestAnimationFrame(t)}:function(t){return t()},this.vendors=["moz","webkit"],this.start=this.start.bind(this),this.resetAnimation=this.resetAnimation.bind(this),this.scrollHandler=this.scrollHandler.bind(this),this.scrollCallback=this.scrollCallback.bind(this),this.scrolled=!0,this.config=function(t,e){for(var i in e)if(null==t[i]){var n=e[i];t[i]=n}return t}(t,this.defaults),null!=t.scrollContainer&&(this.config.scrollContainer=document.querySelector(t.scrollContainer)),this.animationNameCache=new a,this.wowEvent=function(t,e,i,n){var s=!(arguments.length<=1||void 0===e)&&e,o=!(arguments.length<=2||void 0===i)&&i,r=arguments.length<=3||void 0===n?null:n,a=void 0;return null!=document.createEvent?(a=document.createEvent("CustomEvent")).initCustomEvent(t,s,o,r):null!=document.createEventObject?(a=document.createEventObject()).eventType=t:a.eventName=t,a}(this.config.boxClass)}function d(){i(this,d),"undefined"!=typeof console&&null!==console&&(console.warn("MutationObserver is not supported by your browser."),console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content."))}function f(){i(this,f),this.keys=[],this.values=[]}function p(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}e.default=c,t.exports=e.default});