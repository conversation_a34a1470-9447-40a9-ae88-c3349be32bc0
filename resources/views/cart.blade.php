<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
	@section('title','Cart')

	<!-- StyleSheet -->
	@include('./partials/head')

	<style>
		.couponarea{
			text-transform:uppercase;
			background-color: #F6F7FB;
		}
		.totalpay{
			color: red;
			font-size: 16px;
			font-weight: 500;
		}
		.scrolling-wrapper{
			display: flex;
			justify-content: center;
		}
		.text-productname{
			padding-top: 20px;
			font-weight: 500;
		}
		.price-like{
			font-weight: 400;
			font-size: 16px;
			padding-top: 10px;
			/* color: black; */
		}

		.qty-desc.not-allowed,
		.qty-incr.not-allowed {
			position: relative;
			display: inline-block;
			cursor: not-allowed;
		}
		.qty-desc.not-allowed::after {
			content: "You've reached minimum quantity of this product";
			white-space: nowrap;
			visibility: hidden;
			background-color: #333;
			color: #fff;
			text-align: center;
			border-radius: 4px;
			padding: 5px;
			position: absolute;
			z-index: 1;
			bottom: 125%;
			left: 50%;
			transform: translateX(-50%);
			opacity: 0;
			transition: opacity 0.3s;
		}
		.qty-incr.not-allowed::after {
			content: "You've reached maximum quantity of this product";
			white-space: nowrap;
			visibility: hidden;
			background-color: #333;
			color: #fff;
			text-align: center;
			border-radius: 4px;
			padding: 5px;
			position: absolute;
			z-index: 1;
			bottom: 125%;
			left: 50%;
			transform: translateX(-50%);
			opacity: 0;
			transition: opacity 0.3s;
		}
		.qty-desc.not-allowed:hover::after,
		.qty-incr.not-allowed:hover::after {
			visibility: visible;
			opacity: 1;
		}
	</style>

</head>
<body class="js">

	<!-- Preloader -->
	<div class="preloader">
		<div class="preloader-inner">
			<div class="preloader-icon">
				<span></span>
				<span></span>
			</div>
		</div>
	</div>
	<!-- End Preloader -->

	<!-- Header -->
    	@include('./partials/header-menu')
	<!--/ End Header -->

	<!-- new Cart -->
		<div class="new-shopping-cart">
			<div class="container d-none d-lg-block">
				<div style="height: 50px"></div>
				<div style="font-size: 16px; font-weight: 600; padding-bottom: 25px;">
					Your Cart
				</div>
				<div class="row">
					<div class="col-12">
						@php
							// assign global route
							$urlShopGrid = "";
							$urlCheckout = "";
			
							if(isset($agentpid)){
								// $urlShopGrid = route('shop-gridMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
								$urlShopGrid = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
								$urlCheckout = route('checkoutMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
							} else{
								// $urlShopGrid = route('shop-gridMask', ['url_param'=>$url_param]);
								$urlShopGrid = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
								$urlCheckout = route('checkoutMask', ['url_param'=>$url_param]);
							}
						@endphp
						<!-- Shopping Summery -->
						<table class="table shopping-summery-x">
							<thead>
								<tr class="main-hading">
									<th style="font-weight: 400">Product</th>
									<th style="width:40%"></th>
									<th style="width:30%; font-weight: 400" class="text-center">Quantity</th>
									<th style="width:20%; font-weight: 400" class="text-center">Total</th>
								</tr>
							</thead>
						@if (session()->has('cart'))
							<?php $totalRow = 0; $total = 0; $totalPay = 0; $totalSaved = 0; $totalItem = 0; $jumlahunit = "";?>
							<tbody>
								@foreach (session('cart') as $id => $product)
	
									@php
									// dd(session('cart'));
										// $total += str_replace(",", "", $product['price']) * $product['quantity'];
										// $totalRow = str_replace(",", "", $product['price']) * $product['quantity'];

										if($product['hasdiscount'] === "Y"){
											$total += str_replace(",", "", $product['price']) * $product['quantity'];
											$totalRow = str_replace(",", "", $product['price']) * $product['quantity'];
										} else{
											$total += str_replace(",", "", $product['priceasal']) * $product['quantity'];
											$totalRow = str_replace(",", "", $product['priceasal']) * $product['quantity'];
										}
		
										$totalPay = $total;
										$totalItem = count(session('cart'));

										// assign route for product-detail inside cart
										$urlProductDetail = "";
						
										if(isset($agentpid)){
											$urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $product['id'], 'agent_pid' => $agentpid]);
										} else{
											$urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $product['id']]);
										}

										if($product['max_qty'] === "-100"){
											$jumlahunit = "Ready Stock";
										} else{
											$jumlahunit = $product['max_qty'];
										}

									@endphp
	
								<tr>
									<td style="text-align: center" class="imagex" data-title="Productx">
										<div style="background-color: rgb(238, 238, 238)">
											<a href="{{ $urlProductDetail }}"> <img style="height: 100px; max-width: 100px; object-fit: cover;" src="{{ config('minishop.product_image_url') . $product['photo'] }}" alt="#" onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';">
											</a>
										</div>
									</td>
									<td>
										<p style="padding-top: 10px" class="product-name"><a href="{{ $urlProductDetail }}">{{ $product['name'] }}</a></p>
										<p style="padding-top: 10px">Quantity: {{ $jumlahunit }}</p>
									</td>
									<td style="padding-top: 30px" class="qty" data-title="Quantity">
										<div class="input-group">
											<!-- button plus_minus -->
											<div>
												<button class="qty-desc px-lg-3" style="background-color: white" data-type="minus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}">
												<i style="font-size: 10px" class="ti-minus"></i>
												</button>
											</div>                  

											<input style="width: 20%; text-align:center; max-height: 42px" type="number" name="quant[{{$id}}]" class="input-number new-qty" data-min="1" data-max="500" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}" value="{{ $product['quantity'] }}">

											<div>
												<button class="qty-incr px-lg-3" style="background-color: white" data-type="plus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}">
												<i style="font-size: 10px" class="ti-plus"></i>
												</button>
											</div>
										
											<div onClick="event.preventDefault(); document.getElementById('delete-cart-{{ $product['id'] }}').submit();" style="padding-left: 20px" class="action" data-title="Remove">
												<a href="#" onClick="event.preventDefault();
												document.getElementById('delete-cart-{{ $product['id'] }}').submit();"><i style="color: red;" class="ti-trash remove-icon"></i></a>
												<form id="delete-cart-{{ $product['id'] }}" action="{{ route('cart.remove.id') }}"
													method="POST" style="display: none;">
													{{csrf_field()}}
													{{ method_field('DELETE') }}
	
													<input type="hidden" value="{{ $product['id'] }}" name="id">
												</form>
											</div>
										</div>
									</td>								
									<td style="text-align: center; padding-top: 30px; font-weight: 500">
										<label class="total_label" id="total_{{ $product['id'] }}">RM {{number_format($totalRow,2) }}</label>
									</td>
								</tr>
								@endforeach
	
							</tbody>
						@else
							<h2 style="text-align: center"> No items in cart </h2>
							&nbsp;
						@endif
	
						</table>
						<!--/ End Shopping Summery -->
					</div>
				</div>
	
				<div style="height: 50px"></div>
	
				<div style="display: flex; justify-content: space-between;">
					<div style="font-size: 16px; text-decoration: underline;">
						<a href="{{ $urlShopGrid }}">
							Continue shopping
						</a>
					</div>
					<!-- button checkout -->
					<div class="right">
						<div class="button5">
							<a href="{{ $urlCheckout }}" id="cart_total" class="btn" style="color: white; font-weight: 400">Checkout RM {{ number_format($total ?? 0, 2) }}</a>
						</div>
					</div>
				</div>
	
				<div style="padding-top: 80px; padding-bottom: 50px; text-align: center; font-size: 16px">
					You may also like
				</div>
	
				<div class="scrolling-wrapper">
					@foreach ($productLike as $item)

					@php
						$urlProductDetail = "";
		
						if(isset($agentpid)){
							$urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $item['id'], 'agent_pid' => $agentpid]);
						} else{
							$urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $item['id']]);
						}
					@endphp
	
					<div style="display: inline-block; margin-right: 5px; width: 300px;">
						 <a href="{{ $urlProductDetail }}">
							@if ($item['attachment'] == "")
								<img style="object-fit: cover; height: 350px; width: 300px;" class="img-fluid" src="/eShop/images/noimage.png" alt="" style="background-color:rgb(176, 176, 176); padding: 0.2px;">
							@else
								<img style="object-fit: cover; height: 350px; width: 300px;" src="https://corrad.visionice.net/bizapp/upload/product/{{ $item['attachment'] }}">
							@endif
	
							<a href="{{ $urlProductDetail }}">
								<div class="text-productname">
									<h6 class="name-onsale"><a href="{{ $urlProductDetail }}">{{ ucwords(strtolower($item['productname'])) }}</a></h6>
								</div>
								<div style="display: grid; align-content: center;">
									<a href="{{ $urlProductDetail }}">
										<p class="price-like">RM {{ $item['price'] }}</p>
									</a>
								</div>
							</a>
						 </a>
					</div>
	
					@endforeach
				</div>

				<div style="height: 150px"></div>
			</div>

			{{-- for mobile display --}}
			<div class="container d-lg-none d-flex flex-column">
				{{-- <div style="height: 10px"></div> --}}
				@if (session()->has('cart') && count(session('cart')) > 0)

					<div class="border-bottom p-3">
						<h5 class="text-end">Your cart | {{ $totalItem }} item</h5>
					</div>

					<div class="flex-fill py-3 position-relative" style="overflow: auto;">
						<div class="px-2">
							<div class="row mb-4 g-3 fs-md--1 pe-1">
								@foreach (session('cart') as $id => $product )

								@php
									$urlProductDetail = "";
									$jumlahunit = "";
					
									if(isset($agentpid)){
										$urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $product['id'], 'agent_pid' => $agentpid]);
									} else{
										$urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $product['id']]);
									}

									if($product['max_qty'] === "-100"){
											$jumlahunit = "Ready Stock";
									} else{
											$jumlahunit = $product['max_qty'];
									}
								@endphp

								<div class="col-3 pe-1 pe-md-2 ">
									<a href="{{ $urlProductDetail }}"><img src="{{ config('minishop.product_image_url') . $product['photo'] }}"
										class="w-100" alt="prod-img" onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';" /></a>
								</div>
								<div class="col-9 d-flex flex-column">
									<div class="d-flex gap-2">
										<div class="flex-fill mb-1">
											<a href="{{ $urlProductDetail }}"  class="fw-bolder pricePerItem">{{ $product['name'] }}</a>
										</div>

									</div>
									<a style="font-weight:400;">Quantity: {{ $jumlahunit }}</a>
									<br>

									<!-- button plus_minus -->
									<div class="flex-fill" style="margin-bottom: 10px;">
										<button class="qty-desc" style="width: 35px;" data-type="minus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}">
											<i style="font-size: 10px; font-weight:bold;" class="ti-minus no-highlight px-1 cursor-pointer hover-opacity-50"></i>
										</button>
										
										<input style="width:80px;border: none;" type="number" name="quant[{{$id}}]" class="input-number new-qty text-center" data-min="1" data-max="500" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}" value="{{$product['quantity']}}">

										<button class="qty-incr" style="width: 35px;" data-type="plus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}" data-price="{{ $product['priceasal'] }}">
											<i style="font-size: 10px; font-weight:bold;" class="ti-plus no-highlight px-1 cursor-pointer hover-opacity-50"></i>
										</button>
									</div>

									<div class="d-flex align-items-end justify-content-between">
										<div class="d-flex align-items-center justify-content-between pe-2">
												<div id="pricePerItem_{{ $product['id'] }}" >													
													RM {{ $product['price'] }} /unit
												</div>
										</div>
										<div class="text-end" style="width: 50px"> <a onClick="event.preventDefault();
											document.getElementById('delete-cart-{{ $product['id'] }}').submit();" href="#">Remove</a>
											<form id="delete-cart-{{ $product['id'] }}" action="{{ route('cart.remove.id') }}"
												method="POST" style="display: none;">
												{{csrf_field()}}
												{{ method_field('DELETE') }}

												<input type="hidden" value="{{ $product['id'] }}" name="id">
											</form>
										</div>
									</div>
								</div>

								<hr>
								@endforeach

							</div>
						</div>

					</div>

					<div style="height: 100px"></div>

					<a style="background-color: #ff4747" href="{{ $urlShopGrid }}" class="btn mb-0 py-3 w-100 text-white">Continue shopping</a>
					<div class="pb-2"></div>
					<!-- checkout button -->
					<a href="{{ $urlCheckout }}" id="mobile_cart_total" class="btn mb-0 py-3 w-100 text-white" style="background-color: #333333">Checkout RM {{ number_format($total ?? 0, 2) }}</a>
									
					<div class="pb-2"></div>

				@else
					<div style="position: inherit; width: 100%; padding-right: 25px; padding-left: 25px;">
						<div style="height: 150px"></div>
						<div class="tw-bold text-center">Your shopping cart is empty</div>
						&nbsp;
						<div>
							<a href="{{ $urlShopGrid }}" class="btn mb-0 py-3 w-100 text-white text-capitalize"> Shop What's New </a>
						</div>
						<div style="height: 220px"></div>
					</div>

				@endif
			</div>
		</div>
	<!-- end new Cart -->

<script>
	$(document).ready(function() {
		// Initially, disable minus buttons for elements with quantity 1
		var quantityInputs = $('.new-qty');

		// Loop through each input element
		quantityInputs.each(function() {
			var input = $(this);
			var productId = input.attr('data-id');
			var currentQuantity = parseInt(input.val());

			// Disable minus buttons if the current quantity is 1
			if (currentQuantity === 1) {
				$('.qty-desc[data-id="' + productId + '"]').prop('disabled', true).addClass('not-allowed');
			}
		});

		// Event listener for when user updates quantity using plus/minus button
		$('.qty-desc, .qty-incr').click(function (e) {
			e.preventDefault();
			var ele = $(this);
			var productId = ele.attr('data-id');
			var dataType = ele.attr('data-type');
			var field = ele.attr('data-field');
			var input = $('[name="' + field + '"]');

			var currentQuantity = parseInt(input.val());

			if (dataType === 'minus') {
				currentQuantity = currentQuantity - 1;

			} else if (dataType === 'plus') {
				currentQuantity = currentQuantity + 1;
			}

			// Call a function to handle the common logic and AJAX requests
			updateQuantityAndPrice(productId, currentQuantity, dataType, input, ele);
		});

		// Event listener for when user updates quantity directly on input field
		$('.new-qty').on('input', function () {
			var input = $(this);
			var productId = input.attr('data-id');
			var newQuantity = input.val(); // Get the new value as a string

			// Check if the input is empty or not a valid number
			if (newQuantity === '' || isNaN(newQuantity)) {
				return;

			} else if (parseInt(newQuantity) <= 0) {
				newQuantity = 1;
			}

			// Call a function to handle the common logic and AJAX requests
			updateQuantityAndPrice(productId, newQuantity, 'input', input, null);
		});

		function updateQuantityAndPrice(productId, newQuantity, dataType, input, ele) {
			// Send the AJAX request to update the quantity
			$.ajax({
				url: '{{ route('update.quantity') }}',
				method: 'POST',
				data: {
					_token: '{{ csrf_token() }}',
					productId: productId,
					newQuantity: newQuantity,
					dataType: dataType,
				},
				dataType: 'json',
				success: function (response) {
					var newQuantity = parseInt(response.newQuantity);
					var maxQuantity = parseInt(response.maxQuantity);
					console.log('response.maxQuantity ' + maxQuantity);

					// For ready stock products, limit the max quantity to 500 (change max value accordingly)
					if (maxQuantity === -100 && newQuantity > 500) {
						maxQuantity = 500;
						newQuantity = Math.min(newQuantity, maxQuantity);
					}
					
					// Update the quantity on the page with the new value
					input.val(newQuantity);

					// Disable/enable buttons and add/remove 'not-allowed' class based on product quantity
					var minusButtons = $('.qty-desc[data-id="' + productId + '"]');
					var plusButtons = $('.qty-incr[data-id="' + productId + '"]');

					if (newQuantity === 1) {
						minusButtons.prop('disabled', true).addClass('not-allowed');
					} else {
						minusButtons.prop('disabled', false).removeClass('not-allowed');				
					}

					if (newQuantity === maxQuantity || (maxQuantity === -100 && newQuantity === 500)) {
						console.log('plus disabled');
						plusButtons.prop('disabled', true).addClass('not-allowed');
					} else {
						console.log('plus enabled');
						plusButtons.prop('disabled', false).removeClass('not-allowed');
					}

					// to recalculate price if product has discount quantity
					$.ajax({
						url: '{{ route('update.recalculateprice') }}',
						method: 'POST',
						data: {
							_token: '{{ csrf_token() }}',
							productId: productId,
							newQuantity: response.newQuantity,
							pid: response.pid, // todox tukar ni
						},
						dataType: 'json',
						success: function (res) {
							// to update price with(out) discount
							var preDiscount = res.disquantity;
							console.log("preDiscount " + preDiscount);

							if(preDiscount != null || preDiscount != 0){
								// if ade discount, priceafterdiscount = priceasal - discount
								var price = parseFloat((ele || input).attr('data-price').replace(',', ''));
								var discountPrice = price - preDiscount;
									
							} else { 
								// if takde discount, price = priceasal
								var price = parseFloat((ele || input).attr('data-price').replace(',', ''));
								var discountPrice = price;
							}

							// then display price = priceafterdiscount * quantity
							var totalRow = (!isNaN(response.newQuantity) ? (discountPrice * response.newQuantity).toFixed(2) : '0.00');
							$('#total_' + productId).text('RM ' + totalRow.replace(/\d(?=(\d{3})+\.)/g, '$&,'));

							// price display for mobile view
							var pricePerItemSelector = '#pricePerItem_' + productId;
							$(pricePerItemSelector).text('RM ' + discountPrice.toFixed(2) + ' /unit');

							// Update the checkout total
							updateCheckoutTotal();
						},
						error: function (atcXhr, status, error) {
							// Handle error
						}
					});

					// Update the checkout total
					updateCheckoutTotal();
				},
				error: function (xhr, status, error) {
					// Handle error
				},
			});
		}

		function updateCheckoutTotal() {
			var total = 0;
			$('.total_label').each(function () {
				var amount = parseFloat(($(this).text().replace('RM ', '')).replace(',', ''));
				if (!isNaN(amount)) {
					total += amount;
				}
				console.log('total row '  + amount);
			});
			$('#cart_total').text('Checkout RM ' + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'));
			$('#mobile_cart_total').text('Checkout RM ' + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'));
		}
	});
</script>

	@include('./partials/footer-menu')

	<!-- /End Footer Area -->
    @include('./partials/footer')

</body>
</html>
