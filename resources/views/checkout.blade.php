<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
    	@section('title','Checkout')

	<!-- StyleSheet -->
	@include('./partials/head')

	<style>
		.dividerform{
			height: 10px;
		}
		.couponarea{
			border: 0;
			outline: none;
			text-transform:uppercase;
			background-color: #ffffff;
			padding: 13px;
			text-align: -webkit-center;
		}
		.formcoupon{
			text-align: -webkit-center;
		}
		.title-bank{
			margin-left: 0px;
			margin-top: 15px;
			margin-bottom: 10px;
			font-size: 15px;
			font-weight: 600;
		}
		.subtitle-bank{
			margin-left: 0px;
			font-size: 14px;
			font-weight: 400;
			color: red;
		}
		.subtitle-bank-1{
			margin-left: 0px;
			font-size: 14px;
			font-weight: 400;
			color: grey;
		}
		.img-bank{
			width: 30%;
			margin-left: 10px;
		}
		.desc-bank{
			margin-left: 0px;
			font-size: 14px;
			margin-top: 15px;
			margin-bottom: 20px;
			margin-right: 20px;
		}
		.checkout-formx{
			padding-top: 50px;
			padding-right: 50px;
			padding-bottom: 100px;
		}
		.order-detailsx{
			margin-top: 10px;
			margin-left: 30px;
			margin-right: 30px;
			padding: 40px 0 30px 0;
		}

		@media only screen and (max-width: 450px) {
		.couponarea{
			text-transform:uppercase;
			background-color: #ffffff;
			padding: 13px;
		}
		.checkout-formx{
			padding-top: 50px;
			padding-right: 0px;
			padding-bottom: 100px;
		}
		.order-detailsx{
			margin-top: 10px;
			margin-left: 30px;
			margin-right: 30px;
			padding: 40px 0 30px 0;
		}
		}

		@media only screen and (max-width: 767px) {
		.couponarea{
			text-transform:uppercase;
			background-color: #ffffff;
			padding: 13px;
		}
		.checkout-formx{
			padding-top: 50px;
			padding-right: 0px;
			padding-bottom: 30px;
		}
		.order-detailsx{
			margin-top: 10px;
			margin-left: 10px;
			margin-right: 10px;
			padding: 10px 0 30px 0;
		}
		}
		@media only screen and (min-width: 768px) {
		.couponarea{
			text-transform:uppercase;
			background-color: #ffffff;
			padding: 13px;
		}
		.checkout-formx{
			padding-top: 50px;
			padding-right: 50px;
			padding-left: 30px;
			padding-bottom: 50px;
		}
		.order-detailsx{
			margin-top: 10px;
			margin-left: 30px;
			margin-right: 30px;
			padding: 40px 0 30px 0;
		}
		}
	</style>


</head>
<body class="js">

		<!-- Header -->
			{{-- @include('./partials/header-menu') --}}
		<!--/ End Header -->

		<!-- Start Checkout -->
		<section class="shop checkout section">

				@php
					$urlCart = "";

					if(isset($agentpid)){
						$urlCart = route('cartMaskAgent',['url_param' => $url_param, 'agent_pid' => $agentpid]);
					}
					else{
						$urlCart = route('cartMask',['url_param' => $url_param]);
					}

					$discountAmount = 0.00;
					$totalPrice = $subtotalPrice;
					$totalAgentPrice = $subtotalAgentPrice;

					

				@endphp

				<form class="form" method="POST" action="{{ route('checkout.process',['url_param' => $url_param]) }}" enctype="multipart/form-data">
					@csrf

					@php
						$a = request()->cookie('kuponid');
						$minishop_minimumunit = $detailshop[0]['minishop_minimumunit'];
						$minishop_attachmentfile_wajib = $detailshop[0]['minishop_attachmentfile_wajib'];
						$minishop_kospenghantaran_wajib = $detailshop[0]['minishop_kospenghantaran_wajib'];
						$minishop_paymentmanual = $detailshop[0]['minishop_paymentmanual'];
						$minishop_paymentfpx = $detailshop[0]['minishop_paymentfpx'];

						$cleanurl_maskv2 = $url_param;
						$pid_maskv2 = $detailshop[0]['pid'];
					@endphp

					<div class="container">
						<div class="row">
							<form class="form" method="POST" action="{{ route('checkout.process',['url_param' => $url_param]) }}">
								{{-- @csrf --}}

							<!-- kiri -->
							<div class="col-lg-6 col-12">
								<div class="checkout-formx">
									<h2 style="font-size: 17px">Make Your Checkout Here</h2>
									<p class="pb-4" style="font-size: 15px">Please fill in the forms in order to proceed with checkout</p>
									
									<!-- autofill -->
									{{-- <a href="#" onclick="autofill()"><u class="text-muted">Autofill</u></a>
                                    <a href="#" onclick="clear_autofill()"><u class="text-muted">Clear</u></a> <i class="text-muted">*dev only</i> --}}

									<!-- Form -->
										<div class="row" id="formCheckout">

											<!-- first name -->
											<div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<input id="firstName" class="form-control" name="first_name" placeholder="First Name" required="required" type="text" value="{{ old('first_name')}}">
													<label>First Name <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div>

											<!-- last name -->
											<div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<input id="LastName" type="text" class="form-control" name="last_name" placeholder="Last Name" value="{{ old('last_name')}}" required="required">
													<label>Last Name <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div>

												<!-- email -->
												<div class="col-lg-6 col-md-6 col-12">
													<div class="form-floating">
														<input id="email" type="email" name="email" class="form-control" placeholder="Email" value="{{ old('email')}}" required="required">
														<label>Email Address <span>*</span></label>
													</div>
													<div class="dividerform"></div>
												</div>
	
												<!-- phone -->
												<div class="col-lg-6 col-md-6 col-12">
													<div class="form-floating">
														<input id="phone" name="hpno" class="form-control" placeholder="Phone" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" name="hpno" value="{{ old('hpno')}}" required="required">
														<label>Phone <span>*</span></label>
													</div>
													<div class="dividerform"></div>
												</div>

											<!-- address -->
											<div class="col-lg-12 col-md-12 col-12">
												<div class="form-floating">
													<input id="address1" type="text" name="address1" class="form-control" placeholder="Address Line 1" value="{{ old('address1')}}" required="required">
													<label>Address Line 1 <span>*</span></label>
												</div>
												<div class="dividerform"></div>

												<div class="form-floating">
													<input type="text" name="address2" class="form-control" placeholder="Address Line 2" value="{{ old('address2')}}">
													<label>Address Line 2 (Optional)</label>
												</div>
												<div class="dividerform"></div>
											</div>

											<!-- poskod -->
											{{-- <div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<input type="text" class="form-control" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" onchange="callPoskodApi(this.value)" id="postcode" name="postcode" placeholder="Poscode" value="{{ old('postcode')}}" maxlength="5" required="required">
													<label>Postcode <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div> --}}

											<!-- poskod new -->
											<div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<input type="text" class="form-control" name="postcode" id="postcode" placeholder="Postcode" oninput="this.value = this.value.replace(/[^a-zA-Z0-9.]/g, '').replace(/(\..*)\./g, '$1');" onchange="autoFill(this.value)" maxlength="6" required>
													<div class="invalid-feedback">
														Please enter valid postcode.
													</div>
													<label>Postcode <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div>

											<!-- state -->
											{{-- <div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<select class="form-select" name="state" id="state">
														<option value="Johor" {{old('state') === 'Johor' ? "selected" : ''}} selected="selected">Johor</option>
														<option value="Kedah" {{old('state') === 'Kedah' ? "selected" : ''}}>Kedah</option>
														<option value="Kelantan" {{old('state') === 'Kelantan' ? "selected" : ''}}>Kelantan</option>
														<option value="Melaka" {{old('state') === 'Melaka' ? "selected" : ''}}>Melaka</option>
														<option value="Negeri Sembilan" {{old('state') === 'Negeri Sembilan' ? "selected" : ''}}>Negeri Sembilan</option>
														<option value="Pahang" {{old('state') === 'Pahang' ? "selected" : ''}}>Pahang</option>
														<option value="Penang" {{old('state') === 'Penang' ? "selected" : ''}}>Penang</option>
														<option value="Perak" {{old('state') === 'Perak' ? "selected" : ''}}>Perak</option>
														<option value="Perlis" {{old('state') === 'Perlis' ? "selected" : ''}}>Perlis</option>
														<option value="Sabah" {{old('state') === 'Sabah' ? "selected" : ''}}>Sabah</option>
														<option value="Sarawak" {{old('state') === 'Sarawak' ? "selected" : ''}}>Sarawak</option>
														<option value="Selangor" {{old('state') === 'Selangor' ? "selected" : ''}}>Selangor</option>
														<option value="Terengganu" {{old('state') === 'Terengganu' ? "selected" : ''}}>Terengganu</option>
														<option value="W.P. Kuala Lumpur" {{old('state') === 'W.P. Kuala Lumpur' ? "selected" : ''}}>W.P. Kuala Lumpur</option>
														<option value="W.P. Labuan" {{old('state') === 'W.P. Labuan' ? "selected" : ''}}>W.P. Labuan</option>
														<option value="W.P. Putrajaya" {{old('state') === 'W.P. Putrajaya' ? "selected" : ''}}>W.P. Putrajaya</option>
													</select>
													<label>State/Territory<span>*</span></label>
													<div class="dividerform"></div>
												</div>
											</div> --}}

											<!-- state new -->
											<div class="col-lg-6 col-md-6 col-12">
												<div class="form-floating">
													<input type="text" class="form-control" name="state" id="state" placeholder="State" oninput="this.value = this.value.replace(/[^a-zA-Z0-9'/- \-]/s, '');">
													<input type="hidden" value="" id="state_code">
													<div class="invalid-feedback">
														Please enter valid state.														
													</div>
													<label>State/Territory<span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div>

											<!-- country -->
											{{-- <div class="col-lg-12 col-md-12 col-12">
												<div class="form-floating">
													<input id="country" type="text" name="country" class="form-control" placeholder="Country" value="{{ old('country')}}" required="required">
													<label>Country <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div> --}}

											<!-- country new -->
											<div class="col-lg-12 col-md-12 col-12">
												<div class="form-floating">
													<input type="text" class="form-control" name="country" id="country" placeholder="Country" oninput="this.value = this.value.replace(/[^a-zA-Z0-9'/- \-]/s, '');">
													<div class="invalid-feedback">
														Please enter valid country.														
													</div>
													<label>Country <span>*</span></label>
												</div>
												<div class="dividerform"></div>
											</div>							
											
											<!-- postage -->
											<div class="form-floating">
												<select class="form-select" name="postCost" id="postCost" onchange="updateSelectedValue()">
												  <option value="" selected>Please Select</option>
													@foreach ($postageCost as $pCost)
														<option value="{{$pCost['productid']}}" 
															@if (old('postCost') === $pCost['productid']) 
																selected 
															@endif 
																price="{{ $pCost['agentprice'] }}">{{ $pCost['productname'] }} - RM {{ $pCost['agentprice'] }}</option>
													@endforeach
													</select>

													<label style="padding-left: 25px">Postage <span>*</span></label>
												<div class="dividerform"></div>
											</div>
											
											<!-- delivery method -->
											<div class="form-floating">
												<select required="required" class="form-select" name="delivery_method" id="delivery-method">
													<option value="" selected="selected">Please Select</option>
													@foreach ($caraPenghantaran as $hantar)
														<option value="{{ $hantar['codevalue'] }}"> {{ $hantar['codename'] }} </option> 
													@endforeach

												</select>
												<label style="padding-left: 25px">Delivery Method <span>*</span></label>
											<div class="dividerform"></div>
											</div>


											<!-- file attachment -->
											<div class="col-lg-12 col-md-12 col-12">
												<div class="form-floating">
													<input style="height: 100px; padding: 50px 30px" type="file" id="proof" name="proof" class="form-control" placeholder="Upload Here" accept="image/png, image/jpeg, image/jpg">
													<label>File Attachment (if any)</label>
												</div>
												<div class="dividerform"></div>
											</div>

											<!-- note to seller -->
											<div class="col-lg-12 col-md-12 col-12">
												<div class="form-floating">
													{{-- <input type="text" class="form-control" placeholder="Note to seller" value="{{ old('note')}}"> --}}
													<textarea class="form-control" style="height: 100px" name="note" placeholder="Note to Seller / Transaction Detail" id="note" rows="4">{{ old('note')}}</textarea>
													<label for="NoteControlTextarea">Note to Seller / Transaction Detail (Optional)</label>
												</div>
											</div>

										</div>
									<!--/ End Form -->

									<!-- return to cart -->
									<div style="padding-top: 40px; padding-bottom: 20px;">
										<a href="{{ $urlCart }}">
											<i class="fa fa-angle-left" style="font-size: 18px; padding-right: 10px"></i> Return to Cart
										</a>
									</div>
									<hr>
								</div>
							</div>
							</form>

							<!-- kanan -->
							<div class="col-lg-6 col-12" style="background-color: #F3F3F3">
								<div class="order-detailsx">
									<div style="padding-bottom: 50px; font-size: 16px; font-weight: 600">
										Cart Totals
									</div>

									@if(session()->has('cart'))
										<?php 
											$totalRow = 0; 
											$total = 0; 
											$totalPay = 0; 
											$totalSaved = 0; 
											$totalItem = 0; 
											$totalDiscount = 0; 
											$bundleName = '';
										?>
										@foreach (session('cart') as $id => $product)

										<div class="row mb-4 g-3 product-row" style="padding-bottom: 0px"  data-productid="{{ $product['id'] }}">
										@php
											
											if(session('bundlePrice') === '' || session('bundlePrice') === '-1'){
												if($product['hasdiscount'] === "Y"){
													$total += str_replace(",", "", $product['price']) * $product['quantity'];
													$totalRow = str_replace(",", "", $product['price']) * $product['quantity'];
												} else{
													$total += str_replace(",", "", $product['priceasal']) * $product['quantity'];
													$totalRow = str_replace(",", "", $product['priceasal']) * $product['quantity'];
												}
											} else{
												$totalPrice = session('bundlePrice') ?? '';
												$bundleName = session('bundleName') ?? '';
												$totalRow = str_replace(",", "", $product['priceasal']) * $product['quantity'];
											}

											$totalDiscount = (float)$totalRow - $product['discount'];

											$totalQuantity = $product['quantity'];

											$totalPay = $total;
											$totalItem = count(session('cart'));

											// assign route for product-detail inside cart
											$urlProductDetail = "";

											if(isset($agentpid)){
												$urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $product['id'], 'agent_pid' => $agentpid]);
											} else{
												$urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $product['id']]);
											}
										@endphp

											<!-- image -->
											<div class="col-3" style="">
												<a href="{{ $urlProductDetail }}"><img style="height: 100px; width: 100px; object-fit: cover" src="{{ config('minishop.product_image_url') . $product['photo'] }}"
													class="w-100" alt="prod-img" onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';" /></a>
											</div>

											<!-- product name -->
											<div class="col-6" >
												<div style="display: flex; justify-content: space-between;">
													<a href="{{ $urlProductDetail }}" class="text-dark">{{ $product['name'] }}</a>
												</div>
												<div style="padding-top: 10px">{{ $totalQuantity }} x unit</div>
											</div>

											<!-- price -->
											{{-- <div class="col-3" style="text-align: end">
												@if($product['discount'] > 0)
													<a><del>RM {{ $totalRow }}</del></a>
													<p id="totalDiscount" style="font-weight:bold; color:#000000;">RM {{ $totalDiscount }}</p>
												@else
													<a>RM {{ $totalRow }}</a>
												@endif
											</div> --}}

											<!-- price test -->
											<div class="col-3" style="text-align: end">
												<a class="originalPrice">RM {{ number_format($totalRow, 2) }}</a>
												<p class="totalDiscount discounted-price" style="font-weight:bold; color:#000000; display: none;">{{ number_format($totalRow, 2) }}</p>
											</div>
											</div>

										@endforeach

									@else
										<h2 style="font-size: 17px">No items in your cart</h2>
										&nbsp;
									@endif
									</div>

									<!-- for coupon dialog-->
										{{-- @if (Session::has('error'))
											<div class="alert alert-danger mt-2 text-center">{{ Session::get('error') }}
											</div>

										@elseif (Session::has('successcoupon'))
											<div class="alert alert-success mt-2 text-center">{{ Session::get('successcoupon') }}
											</div>

										@endif --}}

									<!-- coupon dialog test -->
									<div id="coupon-message-container">
										@if (Session::has('error'))
											<div class="alert alert-danger mt-2 text-center">{{ Session::get('error') }}</div>
										@elseif (Session::has('successcoupon'))
											<div class="alert alert-success mt-2 text-center">{{ Session::get('successcoupon') }}</div>
										@endif
									</div>

									<!-- Coupon -->
									<div class="col-12 coupon">
                                        {{-- @if (request()->cookie('appliedCouponCode') !== null)
                                            <p>{{ request()->cookie('appliedCouponCode') }}</p>
                                        @endif --}}
                                        {{-- <form class="formcoupon" action="{{ route('cart.apply.discount',['url_param' => $url_param]) }}" method="POST">
                                            {{csrf_field()}}
                                            {{ method_field('POST') }}

											<div class="col-12">
												<div class="form-floating">
													<input class="couponarea col-lg-9 col-md-9 col-8" name="couponCode" placeholder="Coupon Code">
													<button class="btn couponbtn">Apply</button>
												</div>
											</div>
                                        </form> --}}

										<!-- discount coupon -->
										<div class="col-12">
											<div class="form-floating">
												<input id="couponCode" class="couponarea col-lg-9 col-md-9 col-8" name="couponCode" placeholder="Coupon Code">
												<button class="btn couponbtn">Apply</button>
											</div>
										</div>

                                    </div>


									<!-- Order Widget -->
									<div class="single-widget">
									<div class="content">
										@php

											$agent_pid = "";
											$bundleName = '';

											$kuponid = request()->cookie('appliedCouponCode');
												if($discountAmount > 0.00){
													$kuponid = "(" . strtoupper($kuponid) . ")";
												}
												else{
													$kuponid = "";
												}

												if(isset($agentpid)){
													$agent_pid = $agentpid;
												} else{
													$agent_pid = "";
												}

												if(session('bundlePrice') === '' || session('bundlePrice') === '-1'){
													// do nothing
												} else{
													$totalPrice = session('bundlePrice') ?? '';
													$bundleName = session('bundleName') ?? '';
													$totalRow = str_replace(",", "", $product['priceasal']) * $product['quantity'];
												}

										@endphp
											<ul>
												<li>Subtotal<span id="subtotalPrice">RM {{ number_format($subtotalPrice, 2) }}</span></li>
												<li id="discountCode">(-) Coupon Code {{ $kuponid }}<span id="discountAmount"></span></li>
												<li>(+) Shipping<span id="selectedValueField">RM 0.00</span></li>
												
												@if ($bundleName === '')
													<div>
														
													</div>

												@else
													<li style="color: red">You are eligible for bundle product : {{ $bundleName }}</li>
												@endif

												<li style="padding-top: 10px">Total<span style="font-weight: bold; font-size: 17px; color: black;" id="selectedTotalPrice">RM {{ number_format($totalPrice, 2) }}</span></li>
												{{-- <li class="last">Total agent<span style="font-weight: bold; font-size: 17px; color: red;" id="selectedTotalPrice">RM {{ number_format($totalAgentPrice, 2) }}</span></li> --}}
												<li ></span></li>
												<input type="hidden" value="{{ $totalPrice }}" id="totalPrice" name="totalPrice">
												<input type="hidden" value="{{ $totalWeight }}" name="totalWeight">
												<input type="hidden" value="{{ $bundleName }}" name="bundleName">

												<input type="hidden" value="{{ $cleanurl_maskv2 }}" name="cleanurl_maskv2">
												<input type="hidden" value="{{ $pid_maskv2 }}" name="pid_maskv2">
												<input type="hidden" value="{{ $agent_pid }}" name="agent_pid">
												<input type="hidden" value="FPX" name="pay">
												</ul>
											</div>
									</div>
									<!--/ End Order Widget -->


									<!-- Button Widget -->
									<div class="single-widget get-button">
										<div class="content">
											<div class="button" style="margin-top: 30px; margin-bottom: 50px">
												{{-- <a href="#" class="btn">proceed to checkout</a> --}}
												<button type="submit" id="submitButton" class="btn checkoutbtn" style="background-color:#000000">Proceed to Payment</button>
											</div>
										</div>
										<input type="hidden" value="{{ $a }}" name="id">
										<input type="hidden" value="{{ $minishop_attachmentfile_wajib }}" name="minishop_attachmentfile_wajib">
										<input type="hidden" value="{{ $minishop_kospenghantaran_wajib }}" name="minishop_kospenghantaran_wajib">
										<input type="hidden" value="{{ $minishop_minimumunit }}" name="minishop_minimumunit">
									</div>
									<!--/ End Button Widget -->
								</div>
							</div>

						</div>
					</div>
				</form>
		</section>
		<!--/ End Checkout -->

		<!-- Start Footer Area -->
        {{-- @include('./partials/footer-menu') --}}
		<!-- /End Footer Area -->
		{{-- @include('./partials/footer') --}}

		@if (Session::has('invalidurl'))
			<script type="text/javascript">
				window.alert('Invalid URL payment gateway');
			</script>
		@endif

		<script>
			// hide fpx payment if user choose CODK
		$(document).ready(function () {
			$('#delivery-method').on('change', function() {
				if ( this.value == 'CODK')
				{
					$("#pay_fpx_cc").hide();
					$("#pay_fpx_cc_lbl").hide();
				}
				else
				{
					$("#pay_fpx_cc").show();
					$("#pay_fpx_cc_lbl").show();
				}
			});
		});

		function updateSelectedValue() {
			// Get the selected option element
			var selectedOption = document.getElementById("postCost").selectedOptions[0];
			// Get the value of the agentprice attribute of the selected option
			var selectedValue = selectedOption.getAttribute("price");


			var num = selectedValue.split(',').join('');
			// Convert the selected value to a number
			selectedValue = Number(Math.round(parseFloat(num + 'e' + 2)) + 'e-' + 2).toFixed(2);

			// Set the value of the shipping field
			document.getElementById("selectedValueField").innerHTML = 'RM ' + selectedValue;

			var total = 0;
			var bundlePrice = {!! json_encode($getBundlex) !!};

			// if tiada bundle
			if(bundlePrice === '' || bundlePrice === '-1'){
				$('.totalDiscount').each(function () {
					var amount = parseFloat(($(this).text().replace('RM ', '')).replace(',',''));
					total += amount;
				});
			} else{
				// ada bundle
				total = parseFloat(bundlePrice.replace(',', ''));
			}

			// Perform the calculation
			var result = Number(selectedValue) + total;
			// set the value to total price field
			// result.toLocaleString('us',{minimumFractionDigits: 2, maximumFractionDigits: 2});
			var toDecimal = result.toFixed(2);
			// console.log(toDecimal);
			document.getElementById("selectedTotalPrice").innerHTML = 'RM ' + toDecimal;
			document.getElementById("totalPrice").value = toDecimal;
			// set totalPrice value to be sent to CheckoutController
			$('#totalPrice').val(toDecimal);

		}

		document.querySelector(".checkoutbtn").addEventListener('click', myfunction);
		var loader = document.querySelector(".preloader");
		loader.style.display = "none";

		function myfunction() {
		    loader.style.display = "block";
		}

		function callPoskodApi (poskod)
        {
            var url = '{{ route("getPoskod",[":poskod"]) }}';
            url = url.replace(':poskod', poskod);

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function (data, textStatus, xhr){
                    document.querySelector('#state').value = data['state_name'];
                },
                // error: function (xhr, textStatus, errorThrown){
                //     document.getElementById('postcode').value = '';
                //     alert('Sila masukkan poskod yang sah !');
                //     document.getElementById('postcode').focus();
                //     document.querySelector('#state').value = "Johor";
                //     $('#state').niceSelect('update');
                // },
            });
        }
		</script>

		<script>
			function autoFill (poskod)
        {
            var url = '{{ route("getPoskod",[":poskod"]) }}';
            url = url.replace(':poskod', poskod);

            let inputCity = $('#city');
            let inputState = $('#state');
            let inputStateCode = $('#state_code');
            let inputCountry = $('#country');

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: function (data, textStatus, xhr){

                    // Input City autofill
                    inputCity.val(data['bandar']);

                    // Input State autofill
                    inputState.val(data['state']);

                    // Input Country autofill
                    inputCountry.val(data['country']);

                    if(data['postal'] !== null)
                    {
                        // Set state_code input type hidden
                        if (data['postal']['state_code'] === null || data['postal']['state_code'] === "")
                        {
                            // code = data['postal']['country_code'];
                            inputStateCode.val(data['postal']['country_code']);
                        }
                        else
                        {
                            inputStateCode.val(data['postal']['state_code']);
                            // code = data['postal']['state_code'];
                        }
                    }
                    else
                    {
                        inputCity.val("");
                        inputState.val("");
                        inputCountry.val("");
                        inputStateCode.val("");
                    }
                    //postagePopulate();
                },
                error: function () {
                    inputCity.val("");
                    inputState.val("");
                    inputCountry.val("");
                    inputStateCode.val("");
                }
            });

            // enable select dropdown
            $('#postage').prop('disabled', false);
            $('#postage_mobile').prop('disabled', false);
        }
		</script>

		<script>
			const deliveryMethod = document.getElementById('delivery-method');
			const proceedButton = document.getElementById('submitButton');

			deliveryMethod.addEventListener('change', function() {
			const selectedValue = deliveryMethod.value;

			if (selectedValue === 'CODK') {
				proceedButton.textContent = 'Proceed';
			} else {
				proceedButton.textContent = 'Proceed to Payment';
			}
			});
		</script>

		<script>
		$(document).ready(function () {
			$('.couponbtn').click(function (e) {
				e.preventDefault();
				var couponCode = $('#couponCode').val();

				$.ajax({
					url: '{{ route('cart.apply.coupon',['url_param' => $url_param]) }}',
					method: 'POST',
					data: {
						_token: '{{ csrf_token() }}',
						couponCode: couponCode,
					},
					dataType: 'json',
					success: function (response) {
						if (response.success) {
							$('#coupon-message-container').html('<div class="alert alert-success mt-2 text-center">' + response.success + '</div>');

							var cartData = response.cart;
							var discountCode = response.discountCode;
							var discountAmount = parseFloat(response.totalSaved);
							var bundlePrice = response.bundlePrice;

							if(discountCode === ''){
								$('#discountCode').text('(-) Coupon Code');
							} else{
								$('#discountCode').text('(-) Coupon Code (' + discountCode.toUpperCase() + ')');
							}

							$('#discountAmount').text('RM ' + discountAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&')).hide();

							for (const id in cartData) {
								if (cartData.hasOwnProperty(id)) {
									const element = cartData[id];
									var discount = parseFloat(element['discount']);
									var price = parseFloat(element['price']);

									var totalRow = price * parseInt(element['quantity']);
									var discountedPrice = totalRow - discount;

									var productRow = $('.product-row[data-productid="' + id + '"]');
									var totalDiscount = productRow.find('.totalDiscount');
									var originalPrice = productRow.find('.originalPrice');

									if (discount > 0) {

										if (discountedPrice < 0) {
											discountedPrice = 0;
										}

										totalDiscount.text('RM ' + discountedPrice.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')).show();
										originalPrice.css('text-decoration', 'line-through');

										// Store discounted price in localStorage
										// localStorage.setItem('discountedPrice_' + id, discountedPrice.toFixed(2));

										var total = 0;
										var subamount = 0;
										var shipping = parseFloat($('#selectedValueField').text().replace('RM ', '').replace(',', ''));

										if(bundlePrice === '' || bundlePrice === '-1'){
											$('.totalDiscount').each(function () {
												var amount = parseFloat(($(this).text().replace('RM ', '')).replace(',',''));
												total += amount;
											});
										} else{
											$('.totalDiscount').each(function () {
												var amount = parseFloat(($(this).text().replace('RM ', '')).replace(',',''));
												subamount += amount;
											});

											$('#subtotalPrice').text('RM ' + subamount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'));
											total = parseFloat(bundlePrice.replace(',', ''));
										}

										total += shipping;
										$('#selectedTotalPrice').text('RM ' + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'));

										// set totalPrice value to be sent to CheckoutController
										$('#totalPrice').val(total);
									}
								}
							}
							// updateTotalAmount(cartData);

						} else if (response.error) {
							$('#coupon-message-container').html('<div class="alert alert-danger mt-2 text-center">' + response.error + '</div>');
						}
					},
					error: function (xhr, status, error) {
						// Handle error
					}
				});
			});

			// $('.product-row').each(function () {
			// 	var productId = $(this).data('productid');

			// 	Get discounted price in localStorage
			// 	var storedDiscountedPrice = localStorage.getItem('discountedPrice_' + productId);

			// 	if (storedDiscountedPrice !== null) {
			// 		var totalDiscount = $(this).find('.totalDiscount');
			// 		var originalPrice = $(this).find('.originalPrice');

			// 		totalDiscount.text('RM ' + storedDiscountedPrice).show();
			// 		originalPrice.css('text-decoration', 'line-through');
			// 	}
			// });


			// function updateTotalAmount() {
			// 	var subtotal = parseFloat($('#subtotalPrice').text().replace('RM ', '').replace(',', ''));
			// 	var discount = parseFloat($('#discountAmount').text().replace('RM ', '').replace(',', ''));
			// 	var shipping = parseFloat($('#selectedValueField').text().replace('RM ', '').replace(',', ''));

			// 	var total = 0;
			// 	if(discount > 0){
			// 		$('.totalDiscount').each(function () {
			// 			var amount = parseFloat(($(this).text().replace('RM ', '')).replace(',',''));
			// 			console.log(amount);
			// 			total += amount;
			// 		});

			// 		total += shipping;
			// 		$('#selectedTotalPrice').text('RM ' + total.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,'));
			// 	}
        	// }
		});
		</script>
		
		<script>
				function autofill ()
			{
				$('#firstName').val('Bizappshop');
				$('#LastName').val('Tester');
				$('#address1').val('Bizapp Ventures Sdn. Bhd.');
				$('#email').val('<EMAIL>');
				$('#phone').val('010123456789');
			}

			function clear_autofill ()
			{
				$('#firstName').val('');
				$('#LastName').val('');
				$('#address1').val('');
				$('#email').val('');
				$('#phone').val('');
			}
		</script>
</body>
</html>
