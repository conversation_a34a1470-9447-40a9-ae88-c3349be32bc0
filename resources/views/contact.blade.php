<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
  	@section('title', 'Contact')
	<!-- Favicon -->

	<!-- StyleSheet -->
    @include('./partials/head')


</head>
<body class="js">

    @include('./partials/header-menu')

	<!-- Start Contact -->
	<section id="contact-us" class="contact-us section">
		<div class="container">
				<div class="contact-head" style="margin-top: 50px; margin-bottom: 150px">
					<div class="row">
						<div class="col-lg-8 col-12">
							<div class="form-main">
								<div class="title">
									<h3>Terms & Conditions</h3>
								</div>

								<div>
									@if($getTermCondition[0]['minishop_termandcondition'] == "")
									- No terms & condition set by the seller -
									@else
									{!! nl2br(e($getTermCondition[0]['minishop_termandcondition'])) !!}
									@endif
								</div>

							</div>
						</div>


						<!-- nohp/address/email section -->
						<div class="col-lg-4 col-12">
							<div class="single-head">
								<div class="single-info">
									<i class="fa fa-phone"></i>
									<h4 class="title">Call us Now:</h4>
									<ul>
										<li>{{ $getProfile[0]['nohp'] }}</li>
									</ul>
								</div>
								<div class="single-info">
									<i class="fa fa-envelope-open"></i>
									<h4 class="title">Email:</h4>
									<ul>
										<li><a href="mailto:{{ $getProfile[0]['emel'] }}">{{ $getProfile[0]['emel'] }}</a></li>
									</ul>
								</div>
								<div class="single-info">
									<i class="fa fa-location-arrow"></i>
									<h4 class="title">Our Address:</h4>
									<ul>
										<?php
											$negeri;

											if($getProfile[0]['negeri'] == "1"){
												$negeri = "Johor";
											}
											elseif($getProfile[0]['negeri'] == "2"){
												$negeri = "Kedah";
											}
											elseif($getProfile[0]['negeri'] == "3"){
												$negeri = "Kelantan";
											}
											elseif($getProfile[0]['negeri'] == "4"){
												$negeri = "Melaka";
											}
											elseif($getProfile[0]['negeri'] == "5"){
												$negeri = "Negeri Sembilan";
											}
											elseif($getProfile[0]['negeri'] == "6"){
												$negeri = "Pahang";
											}
											elseif($getProfile[0]['negeri'] == "7"){
												$negeri = "Pulau Pinang";
											}
											elseif($getProfile[0]['negeri'] == "8"){
												$negeri = "Perak";
											}
											elseif($getProfile[0]['negeri'] == "9"){
												$negeri = "Perlis";
											}
											elseif($getProfile[0]['negeri'] == "10"){
												$negeri = "Selangor";
											}

											elseif($getProfile[0]['negeri'] == "11"){
												$negeri = "Terangganu";
											}
											elseif($getProfile[0]['negeri'] == "12"){
												$negeri = "Sabah";
											}
											elseif($getProfile[0]['negeri'] == "13"){
												$negeri = "Sarawak";
											}
											elseif($getProfile[0]['negeri'] == "14"){
												$negeri = "Wilayah Persekutuan Kuala Lumpur";
											}
											elseif($getProfile[0]['negeri'] == "15"){
												$negeri = "Wilayah Persekutuan Labuan";
											}
											elseif($getProfile[0]['negeri'] == "16"){
												$negeri = "Wilayah Persekutuan Putrajaya";
											}
											else{
												$negeri = "";
											}

										?>

										<li style="text-transform: capitalize">{{ strtolower($getProfile[0]['alamat1'] . ' ' . $getProfile[0]['alamat2'] . ' ' . $getProfile[0]['alamat3'] . ' ' . $getProfile[0]['poskod'] . ' ' . $negeri) }}</li>
										
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
	</section>
	<!--/ End Contact -->

	<!-- Map Section -->
	{{-- <div class="map-section">
		<div id="myMap"></div>
	</div> --}}
	<!--/ End Map Section -->


    @include('./partials/footer-menu')
	@include('./partials/footer')

	<!-- Jquery -->
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.0.js"></script>
	<script src="js/jquery-ui.min.js"></script>
	<!-- Popper JS -->
	<script src="js/popper.min.js"></script>
	<!-- Bootstrap JS -->
	<script src="js/bootstrap.min.js"></script>
	<!-- Color JS -->
	<script src="js/colors.js"></script>
	<!-- Slicknav JS -->
	<script src="js/slicknav.min.js"></script>
	<!-- Owl Carousel JS -->
	<script src="js/owl-carousel.js"></script>
	<!-- Magnific Popup JS -->
	<script src="js/magnific-popup.js"></script>
	<!-- Fancybox JS -->
	<script src="js/facnybox.min.js"></script>
	<!-- Waypoints JS -->
	<script src="js/waypoints.min.js"></script>
	<!-- Jquery Counterup JS -->
	<script src="js/jquery-counterup.min.js"></script>
	<!-- Countdown JS -->
	<script src="js/finalcountdown.min.js"></script>
	<!-- Nice Select JS -->
	<script src="js/nicesellect.js"></script>
	<!-- Ytplayer JS -->
	<script src="js/ytplayer.min.js"></script>
	<!-- Flex Slider JS -->
	<script src="js/flex-slider.js"></script>
	<!-- ScrollUp JS -->
	<script src="js/scrollup.js"></script>
	<!-- Onepage Nav JS -->
	<script src="js/onepage-nav.min.js"></script>
	<!-- Easing JS -->
	<script src="js/easing.js"></script>
	<!-- Google Map JS -->
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDnhgNBg6jrSuqhTeKKEFDWI0_5fZLx0vM"></script>
	<script src="js/gmap.min.js"></script>
	<script src="js/map-script.js"></script>
	<!-- Active JS -->
	<script src="js/active.js"></script>
</body>
</html>
