<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
	@section('title','Directory')


	{{-- @include('./partials/head') --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
</head>
<body class="js">

	<!-- Preloader -->
	<div class="preloader">
		<div class="preloader-inner">
			<div class="preloader-icon">
				<span></span>
				<span></span>
			</div>
		</div>
	</div>
	<!-- End Preloader -->

    <div class="container mt-lg-5">
        <div class="row justify-content-center">
            <div class="col-8">
                <div class="alert alert-success alert-dismissible" role="alert" id="successAlert" style="display: none;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                    </svg>
                    &nbsp;&nbsp;
                    Kemaskini url_param <b>BERJAYA!</b>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div class="alert alert-danger alert-dismissible" role="alert" id="failAlert" style="display: none;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-circle-fill" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
                    </svg>
                    &nbsp;&nbsp;
                    Kemaskini url_param <b>TIDAK BERJAYA!</b>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-8">
                <table class="table table-bordered table-striped" id="tableDirectory">
                    <thead style="font-weight: bold;">
                        <tr>
                            <td>action</td>
                            <td>url_param</td>
                            <td>cleanurl</td>
                            <td>pid</td>
                            <td>created_at</td>
                        </tr>
                    </thead>
                    <tbody id="tableBodyDirectory">
                        @forelse ($url_mask as $um)
                            <tr>
                                <td class="text-center col-auto">
                                    <div data-value="edit-{{ $um->id }}" style="display:block;">
                                        <a href="#" onclick="editUrl({{ $um->id }})" class="text-dark" data-toggle="tooltip" data-placement="bottom" title="Edit URL">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                                                <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                                                <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
                                            </svg>
                                        </a>
                                    </div>
                                    <div data-value="submit-{{ $um->id }}" style="display:none;">
                                        <a href="#" onclick="hideSubmit({{ $um->id }})" class="text-danger" data-toggle="tooltip" data-placement="bottom" title="Cancel">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-x-square" viewBox="0 0 16 16">
                                                <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
                                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                                            </svg>
                                        </a>
                                        &nbsp; &nbsp;
                                        <a href="#" onclick="submitClick({{ $um->id }})" class="text-success" data-toggle="tooltip" data-placement="bottom" title="Submit">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-check-square" viewBox="0 0 16 16">
                                                <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
                                                <path d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"/>
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <a href={{ route('homeMask', ['url_param'=>$um->url_param]) }} data-value="href-{{ $um->id }}" target="_blank" id="hrefUrl" class="text-dark" style="display:block;"><b><u>{{ $um->url_param }}</u></b></a>
                                    <input type="text" value="{{ $um->url_param }}" class="form-control" data-value="input-{{ $um->id }}" data-cleanurl="{{ $um->cleanurl }}" id="inputUrl" style="display:none;">
                                </td>
                                <td>{{ $um->cleanurl }}</td>
                                <td>{{ $um->pid }}</td>
                                <td class="text-center">{{ date('d/m/Y', strtotime($um->created_at)) }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5">NO DATA</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
	<!-- /End Footer Area -->

    {{-- @include('./partials/footer')
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script> --}}


    <!-- Start Modal Login -->
    {{-- <div class="modal modal-dialog modal-dialog-centered" id="exampleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Pengesahan Pengguna</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="#">
                    <div class="modal-body">
                        <div class="row justify-content-center">
                            <div class="col-8">
                                <div class="form-group">
                                    <label class="pb-1">Domain <span class="text-danger">*</span></label>
                                    <input class="form-control" type="text" name="domain" placeholder="" required>
                                </div> <br>
                                <div class="form-group">
                                    <label class="pb-1">ID Pengguna <span class="text-danger">*</span></label>
                                    <input class="form-control" type="text" name="id_pengguna" placeholder="" required>
                                </div> <br>
                                <div class="form-group">
                                    <label class="pb-1">Katalaluan <span class="text-danger">*</span></label>
                                    <input class="form-control" type="text" name="katalaluan" placeholder="" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div> --}}
    <!-- End Modal Login -->

    <script>
        $( document ).ready(function() {
            // $('#exampleModal').modal('show');
            // populateDirectory();
        });

        function editUrl(id)
        {
            // Change a href to input box for edit url_param
            document.querySelector("[data-value=href-"+id+"]").style.display = "none";
            document.querySelector("[data-value=input-"+id+"]").style.display = "block";

            // Set div edit & submit display none & block respectively
            document.querySelector("div[data-value=edit-"+id+"]").style.display = "none";
            document.querySelector("div[data-value=submit-"+id+"]").style.display = "block";
        }

        function hideSubmit(id)
        {
            // Change input box to href if user cancel to edit
            document.querySelector("[data-value=href-"+id+"]").style.display = "block";
            document.querySelector("[data-value=input-"+id+"]").style.display = "none";

            // Set div edit & submit display block & none respectively
            document.querySelector("div[data-value=edit-"+id+"]").style.display = "block";
            document.querySelector("div[data-value=submit-"+id+"]").style.display = "none";
        }

        function submitClick(id)
        {
            let inputUrl = document.querySelector("[data-value=input-"+id+"]").value;
            let cleanurl = document.querySelector("[data-value=input-"+id+"]").getAttribute("data-cleanurl");
            // alert(cleanurl);

            $.ajax({
                url: "{{ route('directoryEdit') }}",
                type:"POST",
                data:{
                    "_token": "{{ csrf_token() }}",
                    id:id,
                    inputUrl:inputUrl,
                    cleanurl:cleanurl,
                },
                success:function(response){
                    // alert(response);
                    var url = '{{ route("homeMask", ":slug") }}';
                    url = url.replace(':slug', inputUrl);
                    document.querySelector("[data-value=href-"+id+"]").innerHTML = '<b><u>'+inputUrl+'</u></b>';
                    document.querySelector("[data-value=href-"+id+"]").href = url;
                    hideSubmit(id);
                    $('#successAlert').show();
                },
                error: function(response) {
                    $('#failAlert').show();
                    console.log("Error");
                    console.log(response);
                },
            });
        }

        function populateDirectory()
        {
            $("#tableBodyDirectory").html('');
            var directory = {!! json_encode($url_mask) !!};
            strhtml = '';

            if(directory.length > 0)
            {
                for(var i=0; i<directory.length; i++)
                {
                    var url = '{{ route("homeMask", [":url_param"]) }}';
                    url = url.replace(':url_param', directory[i]['url_param']);

                    strhtml = strhtml.concat('<tr>'+
                                                '<td class="text-center col-auto">'+
                                                    '<div data-value="edit-'+ directory[i]['id'] +'" style="display:block;">'+
                                                        '<div data-value="edit-'+ directory[i]['id'] +'" style="display:block;">'+
                                                            '<a href="#" onclick="editUrl('+ directory[i]['id'] +')" class="text-dark" data-toggle="tooltip" data-placement="bottom" title="Edit URL">'+
                                                                '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">'+
                                                                    '<path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>'+
                                                                    '<path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>'+
                                                                '</svg>'+
                                                            '</a>'+
                                                        '</div>'+
                                                    '<div data-value="submit-'+ directory[i]['id'] +'" style="display:none;">'+
                                                        '<a href="#" onclick="hideSubmit('+ directory[i]['id'] +')" class="text-danger" data-toggle="tooltip" data-placement="bottom" title="Cancel">'+
                                                            '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-x-square" viewBox="0 0 16 16">'+
                                                                '<path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>'+
                                                                '<path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>'+
                                                            '</svg>'+
                                                        '</a>'+
                                                        '&nbsp; &nbsp;'+
                                                        '<a href="#" onclick="submitClick('+ directory[i]['id'] +')" class="text-success" data-toggle="tooltip" data-placement="bottom" title="Submit">'+
                                                            '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-check-square" viewBox="0 0 16 16">'+
                                                                '<path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>'+
                                                                '<path d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.235.235 0 0 1 .02-.022z"/>'+
                                                            '</svg>'+
                                                        '</a>'+
                                                    '</div>'+
                                                '</td>'+
                                                '<td>'+
                                                    '<a href="'+ url +'" data-value="href-'+ directory[i]['id'] +'" target="_blank" id="hrefUrl" class="text-dark" style="display:block;"><b><u>'+ directory[i]['url_param'] +'</u></b></a>'+
                                                    '<input type="text" value="'+ directory[i]['url_param'] +'" class="form-control" data-value="input-'+ directory[i]['id'] +'" data-cleanurl="'+ directory[i]['cleanurl'] +'" id="inputUrl" style="display:none;">'+
                                                '</td>'+
                                                '<td>'+ directory[i]['cleanurl'] +'</td>'+
                                                '<td>'+ directory[i]['pid'] +'</td>'+
                                                '<td class="text-center">'+ directory[i]['created_at'] +'</td>'+
                                            '</tr>');
                }
            }
            else
            {
                strhtml = '<tr><td colspan="5">NO DATA</td></tr>';
            }

            $("#tableBodyDirectory").append(strhtml);
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>

</body>
</html>
