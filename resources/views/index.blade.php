<!DOCTYPE html>
<html lang="zxx">
    <!-- Meta Pixel Code -->
<script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '698684605065109');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=698684605065109&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
    
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
	@section('title','Home')


	@include('./partials/head')
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <style>
        /* test afiq */
		/* .btn-shop-now {
			position: relative;
			font-weight: 500;
			font-size:14px;
			color: #fff;
			background: #f6f7fb;
			display: inline-block;
			-webkit-transition: all 0.4s ease;
			-moz-transition: all 0.4s ease;
			transition: all 0.4s ease;
			z-index: 5;
			display: inline-block;
			padding: 13px 32px;
			border-radius: 80px;
			text-transform:uppercase;
        }
		.btn-shop-now:hover{
			color:#fff;
			background:#F00423;
		}
		.shop-now{
			color: #333;
		}
		.shop-now:hover{
			color:#fff;
		} */

        .slick-slide {
            margin: 0px 20px;
        }

        /* .slick-slide img {
            width: 30%;
        } */

        .slick-prev:before,
        .slick-next:before {
            color: black;
        }

        .slick-slide {
            transition: all ease-in-out .3s;
            opacity: 1;
        }

        .modal-confirm {
            color: #434e65;
            width: 525px;
        }
        .modal-confirm .modal-content {
            padding: 20px;
            font-size: 16px;
            border-radius: 5px;
            border: none;
            margin: 20px;
        }
        .modal-confirm .modal-header {
            background: #47c9a2;
            border-bottom: none;
            position: relative;
            text-align: center;
            margin: -20px -20px 0;
            border-radius: 5px 5px 0 0;
            padding: 35px;
        }
        .modal-confirm .modal-header-error {
            background: #fb5252;
            border-bottom: none;
            position: relative;
            text-align: center;
            margin: -20px -20px 0;
            border-radius: 5px 5px 0 0;
            padding: 35px;
        }
        .modal-confirm h4 {
            text-align: center;
            font-size: 36px;
            margin: 10px 0;
        }
        .modal-confirm .form-control, .modal-confirm .btn {
            min-height: 40px;
            border-radius: 3px;
        }
        .modal-confirm .close {
            position: absolute;
            top: 15px;
            right: 15px;
            color: black;
            text-shadow: none;
            opacity: 0.5;
        }
        .modal-confirm .close:hover {
            opacity: 0.8;
        }
        .modal-confirm .icon-box {
            color: #fff;
            width: 85px;
            height: 85px;
            display: inline-block;
            border-radius: 50%;
            z-index: 9;
            border: 5px solid #fff;
            padding: 15px;
            text-align: center;
        }
        .modal-confirm .icon-box i {
            font-size: 54px;
            margin: -4px 0 0 -4px;
        }
        .modal-confirm.modal-dialog {
            margin-top: 80px;
        }
        .modal-confirm .btn, .modal-confirm .btn:active {
            color: #fff;
            border-radius: 4px;
            background: #f6f7fb !important;
            text-decoration: none;
            transition: all 0.4s;
            line-height: normal;
            border-radius: 30px;
            margin-top: 30px;
            padding: 6px 50px;
            border: none;
        }
        .modal-confirm .btn:hover, .modal-confirm .btn:focus {
            background: #eda645 !important;
            outline: none;
        }
        .modal-confirm .btn span {
            margin: 1px 3px 0;
            float: left;
        }
        .modal-confirm .btn i {
            margin-left: 1px;
            font-size: 20px;
            float: right;
        }
        .form {
            width: 500px;
            height: 300px;
            background-color: #fff;
            margin: 0 auto;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
        }
        .single-product .product-img {
            position: relative;
            overflow: hidden;
            cursor:pointer;
            height: 250px;
            background-size: cover;
            background-position: center;
            object-fit: cover;
        }
        .single-product .product-img:hover {
            opacity: .7;
        }
        .moreproduct:hover{
            color: #F00423;
        }
        .reveal{
            position: relative;
            transform: translateY(50px);
            opacity: 0;
            transition: 1s all ease;
        }

        .reveal.active{
            transform: translateY(0);
            opacity: 1;
        }

        .grid-container{
            margin: 20px auto;
            /* width: fit-content; */
            justify-content: center;
            background-color:#fff;
            display: grid;
            /* grid-template-columns: 300px 300px; */
            grid-template-columns: auto auto;
            grid-column-gap: 10px;
            grid-row-gap: 10px;
            .img-collection {
                height: 600px;
            }
        }
        .flex-container-collection{
            display: flex;
            justify-content: space-between;
            padding: 50px 10px 0px 10px;
        }
        .flex-container-onsale{
            display: flex;
            justify-content: space-between;
            padding: 10px 100px 0px 100px;
        }
        .flex-container-bestseller{
            display: flex;
            justify-content: space-between;
            padding: 80px 100px 0px 100px;
        }
        .stacked{
            display: grid;
        }
        .stacked > * {
            grid-column: 1 / 1;
            grid-row: 1 / 1;
        }


        .scrolling-wrapper {
            overflow-x: scroll;
            /* overflow-y: hidden; */
            white-space: nowrap;
            position: relative;
            scroll-behavior: smooth;
            padding-left: 20px;
        }
        .scrolling-bestseller {
            overflow-x: scroll;
            /* overflow-y: hidden; */
            white-space: nowrap;
            position: relative;
            scroll-behavior: smooth;
            padding-left: 20px;
        }
        .scrolling-wrapper-flexbox {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            scroll-behavior: smooth;
        }
        .scrolling-wrapper, .scrolling-wrapper-flexbox {
            margin-bottom: 20px;
            width: 100%;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            &::-webkit-scrollbar {
                display: none;
            }
        }
        /* Hide the scrollbar */
        .scrolling-wrapper::-webkit-scrollbar, .scrolling-bestseller::-webkit-scrollbar {
            width: 0.5em;
            background-color: transparent;
        }
        .scrolling-wrapper::-webkit-scrollbar-thumb, .scrolling-bestsellerr::-webkit-scrollbar-thumb {
            background-color: transparent;
        }

        .scrolling-wrapper::-webkit-scrollbar-track, .scrolling-bestseller::-webkit-scrollbar-track {
            background-color: transparent;
        }

        /* product name for on sale and best seller */
        .name-onsale{
            /* padding-top: 15px;
            padding-right: 20px;
            font-size: 16px;
            font-weight: bold;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden; */
        }
        .text-container-onsale {
            padding-top: 20px;
            font-size: 16px;
            font-weight: bold;
            max-height: 3.6em; 
            overflow: hidden;
            position: relative;
            line-height: 1.2em; 
        }
        .price-onsale{
            padding-top: 15px; 
            font-size: 15px;
        }
        .text-overlay-collection{
            position: absolute;
            font-size: 18px;
            bottom: 20%;
            left: 10%;
            right: 20%;
            color: white;
        }
        .overlay-shop-now{
            position: absolute;
            font-size: 16px;
            bottom: 10%;
            left: 10%;
            color: white;
            background-color: #727272;
            padding: 10px;
        }
        .img-bestseller{
            display: inline-block; width: 400px; margin-right: 5px;
        }
        .img-onsale{
            display: inline-block; margin-right: 5px; width: 400px;
        }
        .zoom-container {
            position: relative;
            overflow: hidden;
            width: 400px; 
            height: 450px; 
        }
        .zoom-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .zoom-container:hover img {
            transform: scale(1.4);
        }



        .zoom-container-collection {
            position: relative;
            overflow: hidden;
        }
        .zoom-container-collection img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .zoom-container-collection:hover img {
            transform: scale(1.4);
        }

        @media only screen and (min-width: 768px) {
            .a1{
                text-align: center; font-size: 100px; height: 80px; margin-top: 25px; color: red; padding: 20px;
            }
            .img-bestseller{
                display: inline-block; width: 400px; margin-right: 5px;
            }
            .img-onsale{
                display: inline-block; margin-right: 5px; width: 400px;
            }
        }

        @media only screen and (max-width: 767px) {
            .a1{
                text-align: center; font-size: 24px; height: 50px; color: red; margin-top: 10px;
            }
            .flex-container-onsale{
                display: flex;
                justify-content: space-between;
                padding: 10px 20px 0px 20px;
                scroll-behavior: smooth;
            }
            .flex-container-bestseller{
                display: flex;
                justify-content: space-between;
                padding: 80px 20px 0px 20px;
                scroll-behavior: smooth;
            }
            .flex-container-collection{
                display: flex;
                justify-content: space-between;
                padding: 50px 20px 0px 20px;
            }
            .grid-container{
                justify-content: center;
                .img-collection {
                    height: 350px;
                }
            }
            .text-overlay-collection{
                position: absolute;
                font-size: 18px;
                bottom: 30%;
                left: 10%;
                right: 10%;
                color: white;
            }

            .img-bestseller{
                display: inline-block; width: 300px; margin-right: 5px;
            }
            .img-onsale{
                display: inline-block; margin-right: 5px; width: 300px;
            }
        }
        @media only screen and (max-width: 450px) {
            .a1{
                text-align: center; font-size: 24px; height: 40px; color: red; margin-top: 10px;
            }
            .flex-container-onsale{
                display: flex;
                justify-content: space-between;
                padding: 10px 10px 0px 10px;
                scroll-behavior: smooth;
            }
            .flex-container-bestseller{
                display: flex;
                justify-content: space-between;
                padding: 30px 10px 0px 10px;
                scroll-behavior: smooth;
            }
            .flex-container-collection{
                display: flex;
                justify-content: space-between;
                padding: 50px 10px 0px 10px;
            }
            .grid-container{
                justify-content: center;
                .img-collection {
                    height: 350px;
                }
            }
            .text-overlay-collection{
                position: absolute;
                font-size: 18px;
                bottom: 30%;
                left: 10%;
                right: 10%;
                color: white;
            }

            .img-bestseller{
                display: inline-block; width: 300px; margin-right: 5px;
            }
            .img-onsale{
                display: inline-block; margin-right: 5px; width: 300px;
            }
            /* product toast */
            .toastProd {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background-color: #C5FFE1;
                color: #333;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #01a14f;
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
            }
        }

        /* product toast */
        .toastProd {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #C5FFE1;
            color: #333;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #01a14f;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .toastProd.show {
            opacity: 1;
        }
    </style>

</head>
<body class="js">


	<!-- Header -->
	@include('./partials/header-menu')

	<!--/ End Header -->

	<div class="banner-img" style="background-position: center;">

        @if(isset($detailshop[0]['minishopbannerimage']) && $detailshop[0]['minishopbannerimage'] != "")
		    <img class="banner-img-url" src="https://corrad.visionice.net/bizapp/minishopbanner/{{ $detailshop[0]['minishopbannerimage']}}">

        @else
            <img class="banner-img-url" src="">

        @endif

	</div>

    {{-- happy hour section --}}
    <section class="happyhour">
        @if ($happyhour != [])
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-title" style="margin-top: 50px;">
                        {{-- <h2>Happy Hour !</h2> --}}
                    </div>
                </div>
            </div>

            <div class="inner-top">
                <div class="row">
                    <div class="col-lg-8 offset-lg-2 col-12">
                        <div class="inner">
                            <h2 style="color: #F00423; text-align: center; padding-bottom: 20px"> {{ isset($happyhour[0]['promotionname']) ? $happyhour[0]['promotionname'] : '' }} </h2>
                            <div style="text-align: center; font-size: 18px">
                                {{ isset($happyhour[0]['promotiondesc']) ? $happyhour[0]['promotiondesc'] : '' }}
                            </div>
                            <div id="countdown" style="text-align: center; margin: auto; margin-top: 20px;">
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div style="height: 20px"></div>

        </div>
        @else

        @endif
    </section>

    <!-- Start Best Seller  -->
    <section>
        <div class="flex-container-bestseller">
           <div style="font-weight: bold; font-size: 18px;">
               Best Sellers
           </div>
           
           <div style="font-size: 16px; text-decoration: underline;">

            @php
                $urlShopGrid = "";

                if(isset($agentpid)){
                    $urlShopGrid = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
                } else{
                    $urlShopGrid = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
                }
            @endphp

            <a href="{{ $urlShopGrid }}"> 
                Shop All
            </a>
                <button style="background-color: white; border: none" class="left" onclick="leftScroll()"><i style="font-size: 30px; padding-left: 10px; padding-right: 10px" class="fa fa-angle-left"></i></button>
                <button style="background-color: white; border: none" class="left" onclick="rightScroll()"><i style="font-size: 30px; padding-left: 10px; padding-right: 10px" class="fa fa-angle-right"></i></button>
            </div>
       </div>
       <div style="height: 30px"></div>
   
        <div class="scrolling-bestseller">
        @if ($topsaleproduct === [])

        <div style="text-align: center; padding-top:20px;">No Product Found</div>

        @else

            @foreach ($topsaleproduct as $item)

            @php
                $urlProductDetail = "";

                if(isset($agentpid)){
                    $urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $item['id'], 'agent_pid' => $agentpid]);
                } else{
                    $urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $item['id']]);
                }
            @endphp

        <div class="img-bestseller">
                <a href="{{ $urlProductDetail }}">
                    <div class="zoom-container">
                        @if ($item['attachment'] == "")
                            <img style="object-fit: cover; height: 450px; width: 400px;" class="img-fluid" src="/eShop/images/noimage.png" alt="" style="background-color:rgb(176, 176, 176); padding: 0.2px;">
                        @else
                            <img style="object-fit: cover; height: 450px; width: 400px;" src="https://corrad.visionice.net/bizapp/upload/product/{{ $item['attachment'] }}">
                        @endif
                    </div>
                <a href="{{ $urlProductDetail }}">
                    <div class="text-container-onsale">
                        <h6 class="name-onsale"><a href="{{ $urlProductDetail }}">{{ ucwords(strtolower($item['productname'])) }}</a></h6>
                    </div>
                    <div style="display: grid; align-content: center;">
                        <a href="{{ $urlProductDetail }}">
                            
                            @if ( $item['PROMOSIHAPPYHOUR'] === "Y")
                                <p class="price-onsale" style="color: red">RM {{ $item['promotionprice'] }} <s style="color: rgb(137, 137, 137)">RM {{ $item['price'] }}</s></p>

                            @else
                                <p class="price-onsale">RM {{ $item['price'] }}</p>

                            @endif

                        </a>
                    </div>
                </a>
                </a>
            </div>
            @endforeach

        @endif

       </div>
    </section>
       <!-- End Best Seller  -->

    <!-- New Collection -->
    <section>
        <div class="container">

            @if ($collectionList && count($collectionList) > 0)
                <div class="inner">
                    <div class="flex-container-collection">
                
                        <div style="font-weight: bold; font-size: 18px;">
                            Featured Collections
                        </div>
            
                        <div style="font-size: 16px; text-decoration: underline;">
                            <a href="{{ $urlShopGrid }}">
                                All Collections
                            </a>
                        </div>
                    </div>
                </div>
            @else
            
            @endif

            <div class="grid-container">
                
                    @if ($collectionList && count($collectionList) > 0)
                        @for ($i=0; $i< count($collectionList); $i++)
                        <!-- nak displaykan collection yang > 0 sahaja  -->
                        @php
                        $urlShopGridx = "";
        
                        if(isset($agentpid)){
                            $urlShopGridx = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => $collectionList[$i]['id'], 'agent_pid' => $agentpid]);
                        } else{
                            $urlShopGridx = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => $collectionList[$i]['id']]);
                        }
                        @endphp
                            @if ($collectionList[$i]['noofproducts'] > 0 )
                                <div class="zoom-container-collection">
                                    @if ($collectionList[$i]['attachment'] != "")
                                    <!-- collection ada image  -->
                                        <div class="col">
                                            <a href="{{ $urlShopGridx }}">
                                                <div class="overlay">
                                                    <img style="object-fit: cover" class="img-collection" class="fluid-img" style="align-content: center" src="https://corrad.visionice.net/bizapp/upload/product/{{ $collectionList[$i]['attachment'] }}" alt="#">
                                                    <div class="col">
                                                        <div class="text-overlay-collection">{{ ucwords(strtolower($collectionList[$i]['koleksi'])) }}</div>
                                                        <a href="{{ $urlShopGridx }}"><div class="overlay-shop-now">Shop Now</div> </a>
                                                        {{-- <a href="{{ $urlShopGridx }}"><div class="overlay-shop-now">Shop Now</div> </a> --}}
                                                    </div>
                                                </div>     
                                            </a>
                                        </div>

                                    <!-- collection tiada image  -->
                                    @else
                                        <div class="col">
                                            <a href="{{ $urlShopGridx }}">
                                                <div class="overlay">
                                                    <img style="object-fit: cover; width: 100%" class="img-collection" class="fluid-img" style="align-content: center" src="https://images.unsplash.com/photo-1586075010923-2dd4570fb338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8Z3JleXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=900&q=60" alt="#">
                                                    
                                                    <div class="col">
                                                        <div class="text-overlay-collection">{{ ucwords(strtolower($collectionList[$i]['koleksi'])) }}</div>
                                                        <a href="{{ $urlShopGridx }}"><div class="overlay-shop-now">Shop Now</div> </a>
                                                    </div>
                                                </div>     
                                            </a>
                                        </div>
                                    @endif
                                </div>
                                
                            @else
                            @endif
                        @endfor

                        <!-- tiada collection -->
                    @else
                        {{-- @php
                            $urlShopGridx = "";
            
                            if(isset($agentpid)){
                                $urlShopGridx = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
                            } else{
                                $urlShopGridx = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
                            }
                        @endphp
                        <div>
                            <div>
                                <a href="{{ $urlShopGridx }}">
                                    <div class="overlay">
                                        <img style="object-fit: cover; width: 100rem" class="img-collection" class="fluid-img" style="align-content: center" src="https://images.unsplash.com/photo-1586075010923-2dd4570fb338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8Z3JleXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=900&q=60" alt="#">
                                        
                                        <div class="col">
                                            <div class="text-overlay-collection">All Collection</div>
                                            <div class="overlay-shop-now">Shop Now</div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div> --}}
                    @endif
                
            </div>
        </div>
    </section>
    <!-- End New Collection -->


    <!-- Discount Code  -->
    <section class="shop-newsletter section">
        @php
            $pid_mask = (isset($getProfile[0]) && isset($getProfile[0]['STATUS']) && $getProfile[0]['STATUS'] == '1' && isset($getProfile[0]['pid'])) ? $getProfile[0]['pid'] : '';
            $displaydiscount = isset($detailshop[0]['minishop_displaylink_discount']) ? $detailshop[0]['minishop_displaylink_discount'] : '0';
        @endphp

        @if($displaydiscount == "1")
        <div style="background-color: #f3f3f3; padding-top: 50px; padding-bottom: 50px; margin-top: 100px; margin-bottom: 30px;">
            <div class="container">
                <div class="inner-top">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2 col-12">
                            <!-- Start Newsletter Inner -->
                            <div class="inner">
                                <h4 style="font-size: 20px;">Get Discount Code Here!</h4>
                                <p style="font-size: 15px;"> A special offer just for you, a mystery discount! Provide your email address to claim our latest coupon discount!</p>
                                <form class="newsletter-inner" action="{{ route('discountemail', ['url_param' => $url_param]) }}" method="post">
                                    <input name="_token" type="hidden" value="{{ csrf_token() }}">
                                    <input type="hidden" value="{{ $pid_mask }}" name="pid_mask">
    
                                    @error('province')
                                    <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                    <input name="emailclaimcoupon" id="emailclaimcoupon" placeholder="Your email address" required="" type="email" style="background-color: #f3f3f3">
                                    <button type="submit" class="btn" style="background-color: #f3f3f3"><i class="fa fa-long-arrow-right" style="color: black; font-size: 20px;"></i></button>
                                    <hr>
                                </form>
                            </div>
                            <!-- End Newsletter Inner -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @else
        @endif
    </section>
    <!-- End Discount Code -->

    <!-- Start On Sale  -->
    <section>
         <div class="flex-container-onsale">
            <div style="font-weight: bold; font-size: 18px;">
                On Sale
            </div>
        
            <div style="font-size: 16px; text-decoration: underline;">
            <a href="{{ $urlShopGrid }}">
                 More Products
            </a>
                <button style="background-color: white; border: none" class="left" onclick="leftScrollOnSale()"><i style="font-size: 30px; padding-left: 10px; padding-right: 10px" class="fa fa-angle-left"></i></button>
                <button style="background-color: white; border: none" class="left" onclick="rightScrollOnSale()"><i style="font-size: 30px; padding-left: 10px; padding-right: 10px" class="fa fa-angle-right"></i></button>
            </div>
        </div>
        <div style="height: 30px"></div>

        <div class="scrolling-wrapper">

            @if($productList === [])

            <div style="text-align: center;padding-top:20px;">No Product Found</div>

            @else

                @foreach ($productList as $item)

                @php
                    $urlProductDetail = "";

                    if(isset($agentpid)){
                        $urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $item['id'], 'agent_pid' => $agentpid]);
                    } else{
                        $urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $item['id']]);
                    }
                @endphp

                <div class="img-onsale">
                    <a href="{{ $urlProductDetail }}">
                        <div class="zoom-container">
                            @if ($item['attachment'] == "")
                                <img style="object-fit: cover; height: 450px; width: 400px;" class="img-fluid" src="/eShop/images/noimage.png" alt="" style="background-color:rgb(176, 176, 176); padding: 0.2px;">
                            @else
                                <img style="object-fit: cover; height: 450px; width: 400px;" src="https://corrad.visionice.net/bizapp/upload/product/{{ $item['attachment'] }}">
                            @endif
                        </div>

                        <a href="{{ $urlProductDetail }}">
                            <div class="text-container-onsale">
                                <h6 class="name-onsale"><a href="{{ $urlProductDetail }}">{{ ucwords(strtolower($item['productname'])) }}</a></h6>
                            </div>
                            <div style="display: grid; align-content: center;">
                                <a href="{{ $urlProductDetail }}">

                                    @if ( $item['PROMOSIHAPPYHOUR'] === "Y")
                                    <p class="price-onsale" style="color: red">RM {{ $item['promotionprice'] }} <s style="color: rgb(137, 137, 137)">RM {{ $item['price'] }}</s></p>

                                @else
                                    <p class="price-onsale">RM {{ $item['price'] }}</p>

                                @endif
                                </a>
                            </div>
                        </a>
                    </a>
                </div>

                @endforeach

            @endif

        </div>

    <div style="height: 70px"></div>
    </section>
    <!-- End On Sale  -->


    {{-- for success order --}}
	<div class="modal fade" id="saleSuccessModal" role="dialog" aria-labelledby="saleSuccessModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-confirm">
            <div class="modal-content">
                <div class="modal-header justify-content-center">
                    <div class="icon-box">
                        <i class="material-icons">&#xE876;</i>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <h5>Successful!</h5>
                    &nbsp;
                    <p>Your order has been submitted successfully. Please check your E-mail for order confirmation. Your item will be delivered as soon as possible by the respective seller.</p>
                    &nbsp;
                    <p> The tracking number will be sent to you through E-mail and SMS once your order has been processed. Thank you.</p>
                    <button class="btn btn-success" data-dismiss="modal"><span style="color: black">Okay</span></button>
                </div>
            </div>
        </div>
    </div>

    {{-- for success coupon claim --}}
    <div class="modal fade" id="successcoupon" role="dialog" aria-labelledby="saleSuccessModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-confirm">
            <div class="modal-content">
                <div class="modal-header justify-content-center">
                    <div class="icon-box">
                        <i class="material-icons">&#xE876;</i>
                    </div>
                    {{-- <button type="button" class="close" data-bs-dismiss="modal" aria-hidden="true" style="font-size: 30px;">&times;</button> --}}
                </div>
                <div class="modal-body text-center">
                    <h5>Thank you</h5>
                    &nbsp;
                    <p>We have sent your coupon discount. Please check your email inbox or spam folder.</p>
                    <button class="btn btn-success" data-dismiss="modal"><span style="color: black">Okay</span></button>
                </div>
            </div>
        </div>
    </div>

    {{-- for error if agent dont have Bizappay account --}}
    <div class="modal fade" id="erroragentnobizappay" role="dialog" aria-labelledby="saleSuccessModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-confirm">
            <div class="modal-content">
                <div class="modal-header-error justify-content-center">
                    <div class="icon-box">
                        <i class="fa fa-close"></i>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <h5>Error: Bizappshop not found</h5>
                    &nbsp;
                    <p>This seller doesn't update their payment method. Please contact the seller respectively.</p>
                    <button class="btn btn-success" data-dismiss="modal"><span style="color: black">Okay</span></button>
                </div>
            </div>
        </div>
    </div>

    {{-- for error if product not found --}}
    <div class="modal fade" id="errorproductnotfound" role="dialog" aria-labelledby="saleSuccessModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-confirm">
            <div class="modal-content">
                <div class="modal-header-error justify-content-center">
                    <div class="icon-box">
                        <i class="fa fa-close"></i>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <h5>Error: Product not found</h5>
                    &nbsp;
                    <p>The product you're looking for is no longer available</p>
                    <button class="btn btn-success" data-dismiss="modal"><span style="color: black">Okay</span></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast #1 (Product Sold) -->
    <div id="toast-container"></div>

	@if (Session::has('paymentDone'))
	<script type="text/javascript">
		$(window).load(function () {
			$('#saleSuccessModal').modal('show');
		});
	</script>
	@endif

    @if (Session::has('claimcoupon'))
	<script type="text/javascript">
		$(window).load(function () {
			$('#successcoupon').modal('show');
		});
	</script>
	@endif

    @if (Session::has('agentnobizappay'))
	<script type="text/javascript">
		$(window).load(function () {
			$('#erroragentnobizappay').modal('show');
		});
	</script>
	@endif
    
    @if (Session::has('productnotfound'))
	<script type="text/javascript">
		$(window).load(function () {
			$('#errorproductnotfound').modal('show');
		});
	</script>
	@endif

    <script>
        function leftScroll() {
            const left = document.querySelector(".scrolling-bestseller");
            left.scrollBy(-200, 0);
        }
        function rightScroll() {
            const right = document.querySelector(".scrolling-bestseller");
            right.scrollBy(200, 0);
        }
    </script>

    <script>
        function leftScrollOnSale() {
            const left = document.querySelector(".scrolling-wrapper");
            left.scrollBy(-200, 0);
        }
        function rightScrollOnSale() {
            const right = document.querySelector(".scrolling-wrapper");
            right.scrollBy(200, 0);
        }
    </script>

    <script>

        window.addEventListener('DOMContentLoaded', function() {
        var container = document.querySelector('.scrolling-wrapper');
        var list = document.querySelector('.scroll-list');
        var containerWidth = container.offsetWidth;
        var listWidth = list.offsetWidth;
        var scrollOffset = (listWidth - containerWidth) / 2;
        container.scrollLeft = scrollOffset;
        });

        // hide middle-inner nav on index
        document.getElementById("middle-inner").style.display = "none";

        // make sure page will always be on top to perform js
        window.onbeforeunload = function () {
              window.scrollTo(0, 0);
        }

        // function to hide navbar on top, only show after scroll
        $(document).scroll(function() {
            if ($(this).scrollTop() < 200) {
                document.getElementById("header-inner").style.display = "none";

            } else {
                document.getElementById("header-inner").style.display = "block";
            }
        });


        $(document).ready(function(){
			// $('#saleSuccessModal').modal('show'); // display modal on load for testing

            $('#saleSuccessModal').modal().on('click', '[data-dismiss="modal"]', function() {
                $('#saleSuccessModal').modal('hide');
            });

            $('#successcoupon').modal().on('click', '[data-dismiss="modal"]', function() {
                $('#successcoupon').modal('hide');
            });

            $('#erroragentnobizappay').modal().on('click', '[data-dismiss="modal"]', function() {
                // $('#erroragentnobizappay').modal('hide');
                // window.location.href = "https://www.bizapp.com.my";
            });


            $('.autoplay').slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                autoplay: true,
                autoplaySpeed: 3000,
                responsive: [
                    {
                        breakpoint: 991,
                        settings: {
                            slidesToShow: 3,
                        }
                    },
                    {
                        breakpoint: 767,
                        settings: {
                            slidesToShow: 1,
                        }
                    }
                ]
            });
        });

        // for scrolldown animation
        // source: https://codepen.io/alvarotrigo/pen/PoKamZy
        function reveal() {
        var reveals = document.querySelectorAll(".reveal");

        for (var i = 0; i < reveals.length; i++) {
            var windowHeight = window.innerHeight;
            var elementTop = reveals[i].getBoundingClientRect().top;
            var elementVisible = 150;

            if (elementTop < windowHeight - elementVisible) {
            reveals[i].classList.add("active");
            } else {
            reveals[i].classList.remove("active");
            }
          }
        }

        window.addEventListener("scroll", reveal);

        // ------------ TIMER COUNTDOWN ------------

        var happyHour = {!! json_encode($happyhour) !!};

        // Update the count down every 1 second
        var x = setInterval(function() {
            if(happyHour.length > 0)
            {
                // Set the date we're counting down to
                var happyHourEnd = happyHour[0] && happyHour[0]['enddatetime'] ? happyHour[0]['enddatetime'] : null;
                var countDownDate = new Date(happyHourEnd).getTime();
                // Get today's date and time
                var now = new Date().getTime();

                // Find the distance between now and the count down date
                var distance = countDownDate - now;

                // Time calculations for days, hours, minutes and seconds
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                // Output the result in an element with id="demo"
                strHtml =
                '<div>' +
                    '<div style="display: flex; justify-content: space-around;">' +

                        '<div class="column" style="">' +
                            '<div class="a1">' +
                                days +
                            '</div>' +

                            '<div style="font-size: 20px; color: black; padding-bottom: 10px; padding-right: 8px; padding-left: 8px;">Days' +
                            '</div>' +
                        '</div>' +

                        '<div class="column" style="">' +
                            '<div class="a1">' +
                                hours +
                            '</div>' +

                            '<div style="font-size: 20px; color: black; padding-bottom: 10px; padding-right: 8px; padding-left: 8px;">Hours' +
                            '</div>' +
                        '</div>' +

                        '<div class="column" style="">' +
                            '<div class="a1">' +
                                minutes +
                            '</div>' +

                            '<div style="font-size: 20px; color: black; padding-bottom: 10px; padding-right: 8px; padding-left: 8px;">Minutes' +
                            '</div>' +
                        '</div>' +

                        '<div class="column" style="">' +
                            '<div class="a1">' +
                                seconds +
                            '</div>' +

                            '<div style="font-size: 20px; color: black; padding-bottom: 10px; padding-right: 8px; padding-left: 8px;">Seconds' +
                            '</div>' +
                        '</div>' +

                    '</div>' +
                '</div>';
                document.getElementById("countdown").innerHTML = strHtml;

                // If the count down is over, write some text
                if (distance < 0)
                {
                    clearInterval(x);
                    // document.getElementById("countdown").innerHTML = "EXPIRED";
                    // Hide the countdown element
                    document.getElementById("countdown").style.display = "none";
                }
                {
                    // Show the countdown element
                    document.getElementById("countdown").style.display = "block";
                }
            }
        }, 1000);

    </script>

    <script>

        // to calculate time
        function formatDateToRelativeTime(dateString) {
            const inputDate = parseCustomDateString(dateString);

            if (!inputDate || isNaN(inputDate)) {
                return "Invalid date format";
            }

            const now = new Date();
            const timeDifference = now - inputDate;

            // Define time intervals in milliseconds
            const minute = 60 * 1000;
            const hour = 60 * minute;
            const day = 24 * hour;
            const week = 7 * day;
            const month = 30 * day;
            const year = 365 * day;

            // Determine the appropriate time unit and value
            if (timeDifference < minute) {
                return "Just now";
            } else if (timeDifference < hour) {
                const minutesAgo = Math.floor(timeDifference / minute);
                return `${minutesAgo} minit${minutesAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < day) {
                const hoursAgo = Math.floor(timeDifference / hour);
                return `${hoursAgo} jam${hoursAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < week) {
                const daysAgo = Math.floor(timeDifference / day);
                return `${daysAgo} hari${daysAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < month) {
                const weeksAgo = Math.floor(timeDifference / week);
                return `${weeksAgo} minggu${weeksAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < year) {
                const monthsAgo = Math.floor(timeDifference / month);
                return `${monthsAgo} bulan${monthsAgo > 1 ? '' : ''} yang lalu`;
            } else {
                const yearsAgo = Math.floor(timeDifference / year);
                return `${yearsAgo} tahun${yearsAgo > 1 ? '' : ''} yang lalu`;
            }
        }

        function parseCustomDateString(dateString) {
            const parts = dateString.match(/(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2}) (AM|PM)/);

            if (!parts) {
                // Handle invalid date format
                return null;
            }

            let [, day, month, year, hours, minutes, ampm] = parts;

            day = parseInt(day, 10);
            month = parseInt(month, 10) - 1; // Months are 0-based in JavaScript (0 = January)
            year = parseInt(year, 10);
            hours = parseInt(hours, 10);
            minutes = parseInt(minutes, 10);

            if (ampm === "PM" && hours < 12) {
                hours += 12; // Convert to 24-hour format
            }

            const date = new Date(year, month, day, hours, minutes);
            return date;
        }


        // ------------ PRODUCT SOLD (TOAST)------------
        var productList = {!! json_encode($recordList) !!};

        // Filter the array to exclude items with 'trackingno' equal to 'CBS'
            var productList = productList.filter(function(product) {
            return product.trackingno !== 'CBS' && product.productid !== "-100";
        });

        // Function to display the toast message
        function showToast(message, index) {
            var toast = document.createElement('div');
            toast.classList.add('toastProd');

            var toastText = document.createElement('span');
            toastText.innerText = message;
            toast.appendChild(toastText);

            var container = document.getElementById('toast-container');
            container.appendChild(toast);

            // Show the toast element for 5 seconds
            setTimeout(function() {
                toast.classList.add('show');
            }, index * 8000 + 3000); // Delay the display of each toast message (5s display + 3s delay)

            // Remove the toast element after 8 seconds
            setTimeout(function() {
                toast.classList.remove('show');
                setTimeout(function() {
                    container.removeChild(toast);
                }, 300);
            }, (index + 1) * 8000); // Delay the removal of each toast message (5s display + 3s delay)

            // Check if the last product is reached
            if (index === productList.length - 1) {
                setTimeout(function() {
                    var nextIndex = (index + 1) % productList.length;
                    var nextProductName = productList[nextIndex]['customername'] + ' telah membeli ' + productList[nextIndex]['productname'];
                    showToast(nextProductName, nextIndex);
                }, (index + 1) * 8000 + 3000); // Delay the next toast message by 3 seconds after the last toast message
            } else {
                // Display the next toast message
                var nextIndex = index + 1;
                var nextProductName = productList[nextIndex]['customername'] + ' telah membeli ' + productList[nextIndex]['productname'];
                // showToast(nextProductName, nextIndex);
            }
        }

        // Loop through the product list and display the toast messages
        for (var i = 0; i < productList.length; i++) {

            var relativeTime = formatDateToRelativeTime(productList[i]['transactiondate']);
            // convert customer name
            var custName = productList[i]['customername'];
            var word = custName.split(' ');
            var firstName = word[0];
            var finalcustName = firstName.charAt(0) + firstName.slice(1).toLowerCase();

            var productName = finalcustName + ' telah membeli ' + productList[i]['productname'] + '\n'  + relativeTime;
            // console.log("Product id: " + productList[i]['id']);
            showToast(productName, i);
        }
        </script>

    @include('./partials/footer-menu')

	<!-- /End Footer Area -->

    @include('./partials/footer')
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

</body>
</html>
