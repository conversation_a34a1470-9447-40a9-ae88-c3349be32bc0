<!DOCTYPE html>
<html lang="en">
<head>

<link href="{{ asset('LandingPage/assets/css/vendor.css') }}" rel="stylesheet"/>
        <link href="{{ asset('LandingPage/assets/css/style.css') }}" rel="stylesheet"/>

        <link href="{{ asset('eShop/images/bizappfavicon.ico') }}" rel="icon"/>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Maintenance Page</title>
<style>
  body, html {
    height: 100%;
    margin: 0;
    font-family: Arial, sans-serif;
    background: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .maintenance-container {
    background: white;
    border-radius: 10px;
    padding: 40px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
  }
  .maintenance-logo {
    width: 300px;
    height: 100px;
    background: url("{{ asset('LandingPage/assets/images/logo/logo_bizapp_dark.png') }}") no-repeat center center; /* Replace with your gear icon image path */
    background-size: contain;
    margin: 0 auto;
  }
  .maintenance-title {
    font-size: 24px;
    color: #333;
    margin: 20px 0;
  }
  .maintenance-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }
  .maintenance-button {
    display: inline-block;
    padding: 10px 20px;
    margin: 0 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-size: 16px;
    cursor: pointer;
  }
  .maintenance-button:hover {
    background: #0056b3;
  }
  .maintenance-button.reload {
    background: #28a745;
  }
  .maintenance-button.reload:hover {
    background: #1e7e34;
  }
</style>
</head>
<body>

<div class="maintenance-container">
  <div class="maintenance-logo"></div>
  <h1 class="maintenance-title">Our store is temporarily under maintenance mode.</h1>
  <p class="maintenance-message">During maintenance mode, our store services will be inaccessible. Please contant <strong>HQNAME</strong> for any enquiry.</p>
  <a href="mailto:<EMAIL>" class="maintenance-button">Contact HQ</a>
  <button onclick="location.reload();" class="maintenance-button reload">Reload</button>
</div>

</body>
</html>