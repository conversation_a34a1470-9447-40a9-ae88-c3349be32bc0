<!-- Start Footer Area -->
{{-- Debug: Check if required variables exist --}}
@php
    $getProfile = $getProfile ?? [];
    $url_param = $url_param ?? '';
    $agentpid = $agentpid ?? null;
@endphp

{{-- Temporary Debug Section - Remove after testing --}}
@if(config('app.debug'))
<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; font-size: 12px;">
    <strong>Debug Info:</strong><br>
    Profile Count: {{ is_array($getProfile) ? count($getProfile) : 'Not Array' }}<br>
    @if(isset($getProfile[0]))
        Profile STATUS: {{ $getProfile[0]['STATUS'] ?? 'N/A' }}<br>
        Profile PID: {{ $getProfile[0]['pid'] ?? 'N/A' }}<br>
        Has Address: {{ isset($getProfile[0]['alamat1']) ? 'Yes' : 'No' }}<br>
    @else
        Profile[0]: Not Set<br>
    @endif
    URL Param: {{ $url_param }}<br>
</div>
@endif

<style>
    .footer-title{
        color: white;
        font-size: 17px;
        font-weight: 400;
    }
    .sub-footer-title{
        font-size: 15px;
        font-weight: 300;
        color: green;
    }
    .sub-footer-socmed{
        font-size: 15px;
        font-weight: 300;
        color: white;
    }
    .sub-footer-socmed:hover{
        font-size: 15px;
        font-weight: 300;
        color: red;
    }
    .sub-footer-title:hover{
        font-size: 15px;
        font-weight: 300;
        color: red;
    }
    .sub-footer-location{
        font-size: 15px;
        font-weight: 300;
        color: grey;
    }

    .flex-container{
        display: flex;
        justify-content: space-between;
    }
</style>

<footer class="footer">
        <!-- Footer Top -->

        <!-- End Footer Top -->
        <div class="copyright">
            <div class="container">
                <div class="inner">

                    <div class="flex-container">
                        <!-- col kiri -->
                        <div >
                            <div style="margin-left: 10px; margin-bottom: 15px;">
                                <div class="col-12">
                                    <div>
                                        <span class="footer-title">Location</span>
                                    </div>
                                    <div class="sub-footer-location pt-2">
                                        @if(isset($getProfile[0]) && isset($getProfile[0]['STATUS']) && $getProfile[0]['STATUS'] == '1')
                                            {{ (isset($getProfile[0]['alamat1']) ? $getProfile[0]['alamat1'] : '') . " " . (isset($getProfile[0]['alamat2']) ? $getProfile[0]['alamat2'] : '') . " " . (isset($getProfile[0]['alamat3']) ? $getProfile[0]['alamat3'] : '') }}
                                        @endif
                                    </div>
                                    <div class="sub-footer-location">
                                        @if(isset($getProfile[0]) && isset($getProfile[0]['STATUS']) && $getProfile[0]['STATUS'] == '1')
                                            @php
                                                $poskod = isset($getProfile[0]['poskod']) ? $getProfile[0]['poskod'] : '';
                                                $stateName = class_exists('\App\Helpers\BladeHelper') ? \App\Helpers\BladeHelper::getStateName($getProfile) : 'MALAYSIA';
                                            @endphp
                                            {{ $poskod . " " . $stateName }}
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- col kanan -->
                        <div >
                            <div>

                                @php
                                    $urlShopGrid = "#";
                                    $urlCheckout = "#";
                                    $urlContact = "#";

                                    if(isset($url_param)){
                                        if(isset($agentpid)){
                                            $urlShopGrid = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
                                            $urlCheckout = route('checkoutMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
                                            $urlContact = route('contactMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
                                        }
                                        else{
                                            $urlShopGrid = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
                                            $urlCheckout = route('checkoutMask', ['url_param'=>$url_param]);
                                            $urlContact = route('contactMask', ['url_param'=>$url_param]);
                                        }
                                    }
                                @endphp
                                <div class="col-12">
                                    <div class="pb-2">
                                        <span class="footer-title">Menu</span>
                                    </div>
                                    <a href="{{ $urlShopGrid }}" style="color:grey; padding-bottom: 20px;">Product</a> <br>
                                    <div style="height: 10px;"></div>
                                    <a href="{{ $urlCheckout }}" style="color:grey">Checkout</a> <br>
                                    <div style="height: 10px;"></div>
                                    <a href="{{ $urlContact }}" style="color:grey">Contact Us</a> <br>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        &nbsp;
                    </div>

                        <!-- col (social media) -->
                        <div class="col-lg-4 col-md-6 col-12">
                            <div style="margin-left: 10px;">
                                <div class="col-12">
                                    <div class="pb-2">
                                        <span class="footer-title">Social Media</span>
                                    </div>
                                    @if(isset($getProfile[0]) && isset($getProfile[0]['STATUS']) && $getProfile[0]['STATUS'] == '1')
                                        @if(isset($getProfile[0]['fbpersonal']) && $getProfile[0]['fbpersonal'] != '')
                                        <a href="https://www.facebook.com/{{ $getProfile[0]['fbpersonal'] }}", class="sub-footer-socmed" style="padding-right: 10px;">
                                            <i class="fa fa-facebook-f"></i>
                                            Facebook
                                        </a>
                                        @endif
                                        @if(isset($getProfile[0]['nohp']) && $getProfile[0]['nohp'] != '')
                                        <a href="https://wa.me/+6{{ $getProfile[0]['nohp'] }}", class="sub-footer-socmed" style="padding-right: 10px;">
                                            <i class="fa fa-whatsapp"></i>
                                            Whatsapp
                                        </a>
                                        @endif
                                        @if(isset($getProfile[0]['instagram']) && $getProfile[0]['instagram'] != '')
                                        <a href="https://www.instagram.com/{{ $getProfile[0]['instagram'] }}", class="sub-footer-socmed">
                                            <i class="fa fa-instagram"></i>
                                            Instagram
                                        </a>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>

                    <div class="row" style="margin-top: 15px; margin-left: 5px;">
                        <div class="col-lg-6 col-12">
                            <div class="left">
                                <p style="color: #5a5a5a">Copyright © 2023 <a style="color: #5a5a5a" href="https://www.bizapp.com.my" target="_blank">Bizapp Ventures Sdn. Bhd.</a>  -  All Rights Reserved.</p>
                                <p style="color: #151112">v2.1.2</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- /End Footer Area -->