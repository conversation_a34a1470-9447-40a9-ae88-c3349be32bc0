
@include('./partials/search-modal')
<script>
    // if(isset($_SESSION['successAtc'])){
    //     $("#offcanvasExample").modal("show");
    // }
    var idleMax = 5; // Logout after 5 minutes of IDLE
    var idleTime = 0;

    var idleInterval = setInterval("timerIncrement()", 60000);  // 1 minute interval    
    $( "body" ).mousemove(function( event ) {
        idleTime = 0; // reset to zero
    });

    // count minutes
    function timerIncrement() {
        idleTime = idleTime + 1;
        if (idleTime > idleMax) 
        {
            var url_param = {!! json_encode($url_param) !!};
            var agentpid = {!! json_encode($agentpid ?? "") !!};
            var agent_pid = {!! json_encode($agent_pid ?? "") !!};

            if(agentpid != ""){
                var url = '{{ route("homeMaskAgent",[":upa", ":slug"]) }}';
                url = url.replace(':slug', agentpid);
                url = url.replace(':upa', url_param);
            } else {
                var url = '{{ route("homeMask",[":upa"]) }}';
                url = url.replace(':upa', url_param);
            }
            window.location = url;
        }
    }

    function searchModal()
    {
        $('#searchModal').modal('show');
        $('#searchInput').focus();
    }

    function populateSearchProduct(koleksiid, sorttype, searchproduct)
    {
        $("#productSearch").html('');
        var productList = {!! json_encode($productList) !!};
        var url_param = {!! json_encode($url_param) !!};
        var tempProductList = [];
        var agentpid = {!! json_encode($agentpid ?? "") !!};
        var agent_pid = {!! json_encode($agent_pid ?? "") !!};

        if(searchproduct && searchproduct != null)
        {
            for(var i=0; i<productList.length; i++)
            {
                if(productList[i]["productname"].toLowerCase().match(searchproduct.toLowerCase()))
                {
                    tempProductList.push(productList[i]);
                }
            }
            productList = tempProductList;

            if(productList && productList.length > 0)
            {
                if (sorttype === "CHARLOWHIGH")
                {
                    productList.sort(function(a, b) {
                        return a['productname'] - b['productname'];
                    });
                }
                else if (sorttype === "CHARHIGHLOW")
                {
                    productList.sort(function(a, b) {
                        return a['productname'] - b['productname'];
                    }).reverse();
                }
                else if (sorttype === "AMOUNTLOWHIGH")
                {
                    productList.sort(function(a, b) {
                        return a['price'] - b['price'];
                    });
                }
                else if (sorttype === "AMOUNTHIGHLOW")
                {
                    productList.sort(function(a, b) {
                        return a['price'] - b['price'];
                    }).reverse();
                }
                else if (sorttype === "ENTRYOLDNEW")
                {
                    productList.sort(function(a, b) {
                        return a['datecreated'] - b['datecreated'];
                    });
                }
                else if (sorttype === "ENTRYNEWOLD")
                {
                    productList.sort(function(a, b) {
                        return a['datecreated'] - b['datecreated'];
                    }).reverse();
                }
                else
                {
                    productList.sort(function(a, b) {
                        return a['productname'] - b['productname'];
                    });
                }

                for(var i=0; i<4; i++)
                {
                    // check if is affiliate
                    if(agentpid != ""){
                        var url = '{{ route("product-detailsAgent", [":upa",":slug", ":slugx"]) }}';

                        url = url.replace(':slug', productList[i]["id"]);
                        url = url.replace(':upa', url_param);
                        url = url.replace(':slugx', agentpid);
                    } else {
                        var url = '{{ route("product-details", [":upa",":slug"]) }}';
                        
                        url = url.replace(':slug', productList[i]["id"]);
                        url = url.replace(':upa', url_param);
                    }

                    var urlCart = '{{ route("cartMask.store", ":slug") }}';
                    urlCart = urlCart.replace(':slug', url_param);

                    var imageSrc = '/eShop/images/noimage.png';
                    var imageSrcNoImg = 'https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';

                    if (productList[i]['attachment'] != '' ){
                        imageSrc = 'https://corrad.visionice.net/bizapp/uploadminishop/product/'+ productList[i]["attachment"] +'';
                    } else {
                        imageSrc = '/eShop/images/noimage.png';
                    }

                    // strhtml = '<div class="box">' +
                    //         '<a href="' + url + '">' +
                    //             '<img class="fluid-img" src="'+ imageSrc +'" onerror="this.onerror=null;this.src='+"'/eShop/images/noimage.png'"+'">' +
                    //         '</a>';

                    // // STOCK LABEL
                    // strhtml = strhtml.concat('<div class="mt-3">');
                    // if(productList[i]["hideActualQuantity"] === "Y")
                    // {
                    //     strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                    // }
                    // else if(productList[i]["statusstok"] === "Y")
                    // {
                    //     strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                    // }
                    // else if(productList[i]["bilstok"] > 0)
                    // {
                    //     strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #ffc700;">'+ productList[i]['bilstok'] +' UNIT AVAILABLE </span>');
                    // }
                    // else
                    // {
                    //     strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: red;"> OUT OF STOCK </span>');
                    // }
                    // strhtml = strhtml.concat('</div>');

                    // // PRODUCTNAME LABEL
                    // strhtml = strhtml.concat('<h3 class="product-name"><a style="font-weight: 600" >'+productList[i]["productname"]+'</a></h3>');

                    // // PRODUCT CATEGORY LABEL
                    // strhtml = strhtml.concat('<div class="mt-0 mb-4">');
                    // if (productList[i]["productcategorycodedesc"] === null)
                    // {

                    // }
                    // else
                    // {
                    //     strhtml = strhtml.concat('<i class="text-secondary" style="font-size: 12px">#'+ productList[i]["productcategorycodedesc"].replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();}) +'</i>');
                    // }
                    // strhtml = strhtml.concat('</div>');

                    // // PRICE LABEL
                    // if(productList[i]['PROMOSIHAPPYHOUR'] == "Y")
                    // {
                    //     if(Number(productList[i]['lesspercenthappyhour']) > 100)
                    //     {
                    //         strhtml = strhtml.concat('100% off');
                    //     }
                    //     if(Number(productList[i]['lesspercenthappyhour']) > 0)
                    //     {
                    //         strhtml = strhtml.concat(productList[i]["lesspercenthappyhour"] + ' % off');
                    //     }
                    // }
                    // else
                    // {
                    //     if(Number(productList[i]['diskaunprice']) <= Number(productList[i]['price']) && productList[i]['discounttype'] != "NO_DISCOUNT"){
                    //         if(Number(productList[i]['lesspercent']) > 100){
                    //             strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                    //                             '<span style="color: red; font-size: 14px;">100% with discount code </span>');
                    //         }
                    //         else if(Number(productList[i]['lesspercent']) > 0){
                    //             strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                    //                             '<span style="color: red; font-size: 14px;">' + productList[i]['lesspercent'] + '% with discount code </span>');
                    //         }
                    //     }
                    //     else{
                    //         strhtml = strhtml.concat('RM ' + productList[i]["price"]);
                    //     }
                    // }

                    // // SOLD ITEM INDICATOR
                    // if(productList[i]['itemsoldindicator'] == "1")
                    // {
                    //     strhtml = strhtml.concat('<div class="row col-auto justify-content-end pt-2 pb-0 mb-0" style="color: red; font-size: 15px; font-weight: 500;">'+ productList[i]['itemsold'] +' sold</div></div>');
                    // }
                    // $("#productSearch").append(strhtml);

                    strhtml = '<div class="col-xl-3 col-lg-3 col-md-6 col-6 px-1">' +
                                '<div class="rounded-2 shadow" style="outline-style: solid; outline-width: thin; outline-color: #ededed; margin-top: 10px;">' +
                                    '<div class="rounded-2" style="align-content: center; max-height: 200px; max-width: 100%">' +
                                        '<a href="' + url + '">' +
                                            '<img class="img-search" style="align-content: center; max-height: 150px; max-width: 100%; margin-left: auto; margin-right:auto; display: block;" src="'+ imageSrc +'" onerror="this.onerror=null;this.src='+"'/eShop/images/noimage.png'"+'">' +
                                        '</a>' +
                                    '</div>' +
                                '<br>' +
                                '<div style="padding-left: 15px; padding-bottom: 20px; padding-right: 15px;">' +
                                    // STOCK LABEL
                                    '<div class="row justify-content-start">' +
                                        '<div class="col-auto">';
                                            if(productList[i]["hideActualQuantity"] === "Y")
                                            {
                                                strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                                            }
                                            else if(productList[i]["statusstok"] === "Y")
                                            {
                                                strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                                            }
                                            else if(productList[i]["bilstok"] > 0)
                                            {
                                                strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: #ffc700;">'+ productList[i]['bilstok'] +' UNIT AVAILABLE </span>');
                                            }
                                            else
                                            {
                                                strhtml = strhtml.concat('<span class="product-statusdesc1" style="background-color: red;"> OUT OF STOCK </span>');
                                            }
                                            strhtml = strhtml.concat('</div>' +
                                    '</div>');

                                    // PRODUCTNAME LABEL
                                    strhtml = strhtml.concat('<div class="row justify-content-start">' +
                                        '<div class="col-auto">' +
                                            '<h3 class="product-name"><a style="font-weight: 600" >' + productList[i]["productname"] + '</a></h3>' +
                                            '&nbsp;' +
                                        '</div>' +
                                    '</div>');
                                    
                                    // PRODUCT CATEGORY LABEL
                                    // strhtml = strhtml.concat('<div class="row justify-content-start">' +
                                    //     '<div class="col-auto">');
                                    //     if (productList[i]["productcategorycodedesc"] === null)
                                    //     {

                                    //     }
                                    //     else
                                    //     {
                                    //         strhtml = strhtml.concat('<i class="text-secondary" style="font-size: 12px">#'+ productList[i]["productcategorycodedesc"].replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();}) +'</i>');
                                    //     }
                                    //     strhtml = strhtml.concat('</div>' +
                                    // '</div>');

                                    // PRICE LABEL
                                    strhtml = strhtml.concat('<div class="row justify-content-start">' +
                                        '<div class="col-auto">');
                                            if(productList[i]['PROMOSIHAPPYHOUR'] == "Y")
                                            {
                                                if(Number(productList[i]['lesspercenthappyhour']) > 100)
                                                {
                                                    strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '<s> RM ' + productList[i]["price"] + '</s> <br>' +
                                                        '<span style="color: red; font-size: 14px;"> 100% off Happy Hour </span>');
                                                }
                                                if(Number(productList[i]['lesspercenthappyhour']) > 0)
                                                {
                                                    strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '<s> RM ' + productList[i]["price"] + '</s> <br>' +
                                                        '<span style="color: red; font-size: 14px;">' + productList[i]['lesspercenthappyhour'] + '% off Happy Hour</span>');
                                                }
                                                if(Number(productList[i]['lesspercenthappyhour']) === 0){
                                                    strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>');
                                                }
                                            }
                                            else
                                            {
                                                if(Number(productList[i]['diskaunprice']) <= Number(productList[i]['price']) && productList[i]['discounttype'] != "NO_DISCOUNT"){
                                                    if(Number(productList[i]['lesspercent']) > 100){
                                                        strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                                                        '<span style="color: red; font-size: 14px;">100% with discount code </span>');
                                                    }
                                                    else if(Number(productList[i]['lesspercent']) > 0){
                                                        strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                                                        '<span style="color: red; font-size: 14px;">' + productList[i]['lesspercent'] + '% with discount code </span>');
                                                    }
                                                }
                                                else{
                                                    strhtml = strhtml.concat('RM ' + productList[i]["price"]);
                                                }
                                            }
                                    strhtml = strhtml.concat('</div>' +
                                    '</div>');

                                    // SOLD ITEM INDICATOR
                                    // strhtml = strhtml.concat('<div class="row col-auto justify-content-end pt-2 pb-0 mb-0" style="color: red; font-size: 15px; font-weight: 500;">' +
                                    //     '<div class="col-auto">');
                                    //         if(productList[i]['itemsoldindicator'] == "1")
                                    //         {
                                    //             strhtml = strhtml.concat(productList[i]['itemsold'] +' sold');
                                    //         }
                        //             strhtml = strhtml.concat('</div>' +
                        //             '</div>' +

                        //             '<div class="row justify-content-center pt-2">' +
                        //                 '<div class="col-auto">' +
                        //                     '<a href="' + url + '" class="btn-shop-now shop-now">Buy Now</a>' +
                        //                 '</div>' +
                        //             '</div>' +
                        //         '</div>' +
                        //     '</div>' +
                        // '</div>');

                        $("#productSearch").append(strhtml);
                }

                var seemorehtml = '';
                $("#productSeeMore").html('');

                if(agentpid != ""){
                    var urlShopGrid = '{{ route("koleksi-to-grid-agent", [":upa",":slug", ":slugx"]) }}';
                    urlShopGrid = urlShopGrid.replace(':slugx', agentpid);
                    urlShopGrid = urlShopGrid.replace(':upa', url_param);
                    urlShopGrid = urlShopGrid.replace(':slug', 'all-collection');
                } else {
                    var urlShopGrid = '{{ route("koleksi-to-grid", [":slug", ":slugx"]) }}';
                    urlShopGrid = urlShopGrid.replace(':slug', url_param);
                    urlShopGrid = urlShopGrid.replace(':slugx', 'all-collection');
                }

                seemorehtml = '<div class="col-auto">' +
                                '<a href="' + urlShopGrid + '" style="font-weight:400;">See More Products <i class="ti-arrow-right"></i></a>' +
                            '</div>';

                $("#productSeeMore").append(seemorehtml);
                

            }
            else
            {
                strhtml ='<div class="row justify-content-center">'+
                                '<div class="col-auto">' +
                                    '<h3>No products</h3>' +
                                    '&nbsp;' +
                                '</div>' +
                        '</div>';

                $("#productSearch").append(strhtml);
            }
        }
        else
        {
            $("#productSearch").html('');
        }
    }

    function product_search()
    {
        var cleanurl = $("#cleanurl").val();
        var pid = $("#pid").val();

        var input = document.getElementById('searchInput').value;
        var searchproduct = input.toLowerCase();
        var koleksiid = 'all-collection';
        var sorttype = "CHARLOWHIGH";

        populateSearchProduct(koleksiid, sorttype, searchproduct);
    }
</script>


<!-- Popper JS -->
<script src="{{ asset('eShop/js/popper.min.js') }}"></script>
<!-- Bootstrap JS -->
<script src="{{ asset('eShop/js/bootstrap.min.js') }}"></script>
<!-- Slicknav JS -->
<script src="{{ asset('eShop/js/slicknav.min.js') }}"></script>
<!-- Owl Carousel JS -->
<script src="{{ asset('eShop/js/owl-carousel.js') }}"></script>
<!-- Magnific Popup JS -->
<script src="{{ asset('eShop/js/magnific-popup.js') }}"></script>
<!-- Waypoints JS -->
<script src="{{ asset('eShop/js/waypoints.min.js') }}"></script>
<!-- Countdown JS -->
<script src="{{ asset('eShop/js/finalcountdown.min.js') }}"></script>
<!-- Nice Select JS -->
<script src="{{ asset('eShop/js/nicesellect.js') }}"></script>
<!-- Flex Slider JS -->
<script src="{{ asset('eShop/js/flex-slider.js') }}"></script>
<!-- ScrollUp JS -->
<script src="{{ asset('eShop/js/scrollup.js') }}"></script>
<!-- Onepage Nav JS -->
<script src="{{ asset('eShop/js/onepage-nav.min.js') }}"></script>
<!-- Easing JS -->
<script src="{{ asset('eShop/js/easing.js') }}"></script>
<!-- Active JS -->
<script src="{{ asset('eShop/js/active.js') }}"></script>


<!-- Fancybox JS -->
<script src="{{ asset('eShop/js/facnybox.min.js') }}"></script>
<!-- Ytplayer JS -->
<script src="{{ asset('eShop/js/ytplayer.min.js') }}"></script>

