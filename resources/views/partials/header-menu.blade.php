<style>
    .btn-shop-now {
        position: relative;
        font-weight: 500;
        font-size:14px;
        color: #fff;
        background: #e7e7e9;
        display: inline-block;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transition: all 0.4s ease;
        z-index: 5;
        display: inline-block;
        padding: 13px 32px;
        border-radius: 80px;
        text-transform:uppercase;
    }
    .btn-shop-now:hover{
        color:#fff;
        background:#F00423;
    }
    .shop-now{
        color: #2b2424;
    }
    .shop-now:hover{
        color:#fff;
    }

    .product-statusdesc1{
        padding-right: 20px;
        padding-left: 20px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-weight: 500;
        font-size: 13px;
        border-radius: 30px;
        color: #ffffff;
    }

    /* @media (max-width: 450px) {
        .product-statusdesc1 {
            padding-left: 15px;
            padding-right: 15px;
            font-size: 11px;
        }
    }*/
    @media screen and (max-width: 767px) {
        .stock-label {
            padding-left: 15px;
            padding-right: 15px;
            white-space: nowrap;
            max-width: 100%;
            width: 100%;
            font-size: 11px;
        }
    }

    @media screen and (min-width: 768px) {
        .stock-label {
            white-space: nowrap;
            max-width: 100%;
            width: 100%;
        }
    }

    .product-name{
        font-size: 14px;
        line-height: 1.5;
        margin-top: 10px;
		font-weight: bold;
		color: black;
    }
    .product-price{
        font-size: 15px;
        font-weight: 600;
    }
    .product_categorydesc{
        font-size: 12px;
        text-transform: capitalize;
    }
    .row-price {
        display: flex;
        flex-direction: row;
        margin-top: 15px;
    }
    .product-img:hover{
        opacity: .7;
    }

    .container-product{
        width: 850px;
        margin: 30px auto;
        columns: 3;
        column-gap: 0px;
    }
    .box{
        width: 265px;
        padding: 15px;
        background-color: white;
        border: 1px solid rgb(228, 228, 228);
        margin-bottom: 10px;
        break-inside: avoid;
    }
    .box img{
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

	.boxhappy{
        width: 265px;
        padding: 15px;
        background-color: white;
        border: 1px solid rgb(255, 48, 48);
        margin-bottom: 10px;
        break-inside: avoid;
    }
    .boxhappy img{
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    /* @media only screen and (min-width: 820px) and (max-width: 991px){ */
    /* @media only screen and (min-width: 1025px){
        .container-product{
            width: 100%;
            margin: 30px auto;
            columns: 3;
            column-gap: 3px;
        }
        .box{
            width: 180px;
            padding: 20px;
            background-color: white;
            border: 1px solid rgb(228, 228, 228);
            margin-bottom: 10px;
            break-inside: avoid;
        }
		.boxhappy{
            width: 180px;
            padding: 20px;
            background-color: white;
            border: 1px solid rgb(255, 48, 48);
            margin-bottom: 10px;
            break-inside: avoid;
        }
    } */

    /* @media only screen and (max-width: 767px) { */
    /* @media only screen and (min-width: 481px) and (max-width: 1024px){
        .container-product{
            width: 100%;
            margin: 30px auto;
            columns: 2;
            column-gap: 0px;
        }
        .box{
            width: 180px;
            padding: 20px;
            background-color: white;
            border: 1px solid rgb(228, 228, 228);
            margin-bottom: 10px;
            break-inside: avoid;
        }
		.boxhappy{
            width: 180px;
            padding: 20px;
            background-color: white;
            border: 1px solid rgb(255, 48, 48);
            margin-bottom: 10px;
            break-inside: avoid;
        }

    } */
        /* @media only screen and (max-width: 450px) { */
    @media only screen and (min-width: 401px) and (max-width: 480px){
        .container-product{
            width: 100%;
            margin: 30px auto;
            columns: 2;
            column-gap: 0px;
        }
        .box{
            width: auto;
            padding: 10px;
            background-color: white;
            border: 1px solid rgb(228, 228, 228);
            margin-bottom: 10px;
            break-inside: avoid;
            margin-right: 10px;
        }
		.boxhappy{
            width: auto;
            padding: 10px;
            background-color: white;
            border: 1px solid rgb(255, 48, 48);
            margin-bottom: 10px;
            break-inside: avoid;
            margin-right: 10px;
        }
    }

    @media only screen and (max-width: 400px) {
        .container-product{
            width: 100%;
            margin: 30px auto;
            columns: 2;
            column-gap: 10px;
        }
        .box{
            width: auto;
            padding: 10px;
            background-color: white;
            border: 1px solid rgb(228, 228, 228);
            margin-bottom: 20px;
            break-inside: avoid;
            margin-right: 0px;
        }
        .box img{
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
		.boxhappy{
            width: auto;
            padding: 10px;
            background-color: white;
            border: 1px solid rgb(255, 48, 48);
            margin-bottom: 20px;
            break-inside: avoid;
            margin-right: 0px;
        }
        .boxhappy img{
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
    }
</style>
		<header class="header shop">

			@php
				$urlHome = "";
				$urlShopGrid = "";
				$urlCart = "";
				$urlCheckout = "";
				$urlContact = "";

				if(isset($agentpid)){
					$urlHome = route('homeMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
					$urlShopGrid = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
					// $urlShopGrid = route('shop-gridMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
					$urlCheckout = route('checkoutMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
					$urlContact = route('contactMaskAgent', ['url_param'=>$url_param, 'agent_pid' => $agentpid]);
					$urlCart = route('cartMaskAgent',['url_param' => $url_param, 'agent_pid' => $agentpid]);
				}
				else{
					$urlHome = route('homeMask', ['url_param'=>$url_param]);
					$urlShopGrid = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
					// $urlShopGrid = route('shop-gridMask', ['url_param'=>$url_param]);
					$urlCheckout = route('checkoutMask', ['url_param'=>$url_param]);
					$urlContact = route('contactMask', ['url_param'=>$url_param]);
					$urlCart = route('cartMask',['url_param' => $url_param]);
				}
			@endphp

			<div class="middle-inner" id="middle-inner">
				<div class="container">
					<div class="row">
						<div class="col-lg-2 col-md-2 col-12">
							<!-- Logo -->
							{{-- <div class="logo" style="margin-top: 20px">
								@if(isset($getProfile[0]['attachmentlogodashboard']) && $getProfile[0]['attachmentlogodashboard'] != "" && $getProfile[0]['attachmentlogodashboard'] != null)
								    <a href="{{ route('homeMask', ['url_param'=>$url_param]) }}"><img src="https://corrad.visionice.net/bizapp/upload/profile/{{ $getProfile[0]['attachmentlogodashboard'] }}" alt="" style="display:-webkit-inline-flex; height: 100px;"></a>
								@else
									<a href="{{ route('homeMask', ['url_param'=>$url_param]) }}"><img src="{{ asset('eShop/images/bizapp_logo_long.png') }}" alt="" style="display:-webkit-inline-flex"></a>
								@endif
							</div> --}}

								<!-- mobile view for icon search and -->
								<div class="search-top ms-1">

									<div class="sinlge-bar shopping">
										{{-- data-bs-toggle="offcanvas" href="#offcanvasExample" role="button" aria-controls="offcanvasExample" --}}
										<a class="single-icon" href="{{ $urlHome }}"><i class="fa fa-home" style="font-size: 20px; padding-right: 15px;"></i> </a>
									</div>

									<!-- Search Form -->
									<a href="#" class="single-icon font-weight-bold" onclick="searchModal()" style="font-size: 19px; padding-right: 15px;"><i class="ti-search"></i></a>
									<input type="hidden" id="cleanurl" value="{{ $url_mask }}">
									<input type="hidden" id="pid" value="{{ $url_mask }}">

									@if (session()->has('cart'))
										<?php $total = 0 ?>
									<div class="sinlge-bar shopping">
										{{-- data-bs-toggle="offcanvas" href="#offcanvasExample" role="button" aria-controls="offcanvasExample" --}}
										<a class="single-icon" href="{{ $urlCart }}"><i class="ti-bag"></i> <span style="color: #F00423; margin-right: 10px" class="total-count">{{ count(session('cart')) }}</span></a>
									</div>

									@else
									<div class="sinlge-bar shopping">
										<a href="{{ $urlCart }}" class="single-icon"><i class="ti-bag"></i> <span style="background-color: " class="total-count" style="color: #F00423">0</span></a>
										<!-- Shopping Item -->
										<div class="shopping-item">
											<p>No items in cart</p>
										</div>
										<!--/ End Shopping Item -->
									</div>
									@endif
								</div>

							<!-- menu for mobile -->
							<div class="mobile-nav"></div>
						</div>

					</div>
				</div>
			</div>


		<!-- Header Inner -->
		<!-- style baru -->
		<div class="header-inner d-none d-sm-block" id="header-inner">
			<div class="container">
				<div class="cat-nav-head">
					<div class="row">
						
						<div class="col-lg-3 col-md-2 col-sm-0 d-flex justify-content-center">
							<div class="all-category">
								<!-- image logo -->
								<div class="logo" style="max-width: 100%">
									@if(is_array($getProfile) && count($getProfile) > 0 && isset($getProfile[0]['STATUS']) && $getProfile[0]['STATUS'] == '1' && isset($getProfile[0]['attachmentlogodashboard']) && $getProfile[0]['attachmentlogodashboard'] != "" && $getProfile[0]['attachmentlogodashboard'] != null)
										<a href="{{ $urlHome }}"><img src="https://corrad.visionice.net/bizapp/upload/profile/{{ $getProfile[0]['attachmentlogodashboard'] }}" alt="Avatar" style="display:-webkit-inline-flex; height: 50px; margin: 30px;"></a>
									@else
										<a href="{{ $urlHome }}"><img src="{{ asset('eShop/images/bizapp_logo_long.png') }}" alt="" style="display:-webkit-inline-flex; height: 50px; margin-top: 30px"></a>
									@endif
								</div>
							</div>
						</div>

						<div class="col-lg-6 col-md-7 col-sm-0 d-flex justify-content-center">
							<!-- list menu -->
							<div class="menu-area">
								<!-- Main Menu -->
								<nav class="navbar navbar-expand-lg">
									<div class="navbar-collapse">
										<div class="nav-inner" style="margin-top: 25px">

											<ul class="nav main-menu menu navbar-nav">
												<li><a href="{{ $urlHome }}">Home</a></li>
												<li><a href="{{ $urlShopGrid }}">Product</a></li>
												<li><a href="{{ $urlCheckout }}">Checkout</a></li>
												<li><a href="{{ $urlContact }}">Contact Us</a></li>
											</ul>

										</div>
									</div>
								</nav>
								<!--/ End Main Menu -->
							</div>
						</div>

						<div class="col-lg-3 col-md-3 col-sm-12 d-flex justify-content-center">
							<div class="right-bar" style="margin-top: 15px">
								<!-- icon search -->
								<a href="#" class="single-icon font-weight-bold" onclick="searchModal()" style="font-size: 20px; padding-right: 15px;"><i class="ti-search"></i></a>
								<input type="hidden" id="cleanurl" value="{{ $url_mask }}">
                                <input type="hidden" id="pid" value="{{ $url_mask }}">

								<!-- icon cart bag -->
								@if (session()->has('cart'))
									<?php $total = 0 ?>
									<div class="sinlge-bar shopping">
										{{-- data-bs-toggle="offcanvas" href="#offcanvasExample" role="button"  --}}
										<a href="{{ $urlCart }}" class="single-icon" ><i class="ti-bag" style="font-size: 20px;"></i> <span style="color: white; margin-right: 0px" class="total-count">{{ count(session('cart')) }}</span></a>
									</div>

								@else
									<div class="sinlge-bar shopping">
										<a href="{{ $urlCart }}" class="single-icon"><i class="ti-bag" style="font-size: 20px;"></i> <span class="total-count" style="color: white">0</span></a>
										{{-- <div class="shopping-item">
											<p>No items in cart</p>
										</div> --}}
									</div>
								@endif
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>
		<!--/ End Header Inner -->

		<!-- Start offcanvas cart drawer side part  -->
		<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
				<div class="offcanvas-header">
				  <h5 class="offcanvas-title" id="offcanvasExampleLabel">Cart</h5>
				  <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
				</div>

					<div class="container py-1" style="overflow-y: auto;">
					  <div class="row d-flex justify-content-center my-1">
						<div class="col-md-12">
						  <div class="card mb-4" style="display:flex;">
							<div class="card-header py-3" >
								@if (session()->has('cart'))
									<?php $total = 0.00 ?>

							  <h6 class="mb-0">{{ count(session('cart')) }} ITEMS</h6>
							</div>

							<div class="card-body shopping-list">
							  <div class="row">
								@foreach (session('cart') as $id => $product)
								<?php
									$total += str_replace(",","",$product['price']) * $product['quantity'];
								?>
								@endforeach
							  </div>

							  {{-- body --}}
							  <div class="flex-fill py-3 position-relative" style="overflow: auto;">
								<div class="px-2">
									<div class="row mb-4 g-3 fs-md--1 pe-1">
										@foreach (session('cart') as $id => $product )

										<div class="col-3 pe-1 pe-md-2 ">
											<a href="{{ route('product-details',['productid' => $product['id'],'url_param' => $url_param]) }}"><img src="{{ config('minishop.product_image_url') . $product['photo'] }}"
												class="w-100" alt="prod-img" onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';" /></a>
										</div>
										<div class="col-9 d-flex flex-column">
											<div class="d-flex gap-2">
												<div class="flex-fill mb-1">
													<a href="{{ route('product-details',['productid' => $product['id'],'url_param' => $url_param]) }}" class="text-dark">{{ $product['name'] }}</a>
												</div>

											</div>

											<div class="flex-fill">
												<button type="button" class="btn-dark btn-number qty-desc" style="height: 35px; width: 35px;" data-type="minus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}">
													<span class="no-highlight px-1 cursor-pointer hover-opacity-50">-</span>
												</button>

												<input type="text" readonly name="quant[{{$id}}]" style="width:50px;border: none;" class="input-number new-qty text-center"  data-min="1" data-max="100" value="{{$product['quantity']}}">

												<button type="button" class="btn-dark qty-incr" style="height: 35px; width: 35px;" data-type="plus" data-field="quant[{{$id}}]" data-id="{{ $product['id'] }}">
													<span class="no-highlight px-1 cursor-pointer hover-opacity-50">+</span>
												</button>
											</div>

											<div class="d-flex align-items-end justify-content-between">
												<div class="d-flex align-items-center justify-content-between pe-2">
														<div class="fw-bolder">
																RM {{ $product['price'] }} /unit
														</div>
												</div>
												<div class="text-end"> <a onClick="event.preventDefault();
													document.getElementById('delete-cart-{{ $product['id'] }}').submit();" href="#">Remove</a>
													<form id="delete-cart-{{ $product['id'] }}" action="{{ route('cart.remove.id') }}"
													method="POST" style="display: none;">
													{{csrf_field()}}
													{{ method_field('DELETE') }}

													<input type="hidden" value="{{ $product['id'] }}" name="id">
													</form>
												</div>
											</div>
										</div>
										@endforeach

									</div>
								</div>
							  </div>


							  {{-- button view cart --}}
							  <div class="row">
									<div class="col-7"><h5>Subtotal</h5></div>
									  <div class="col-5"><h5>RM {{ number_format($total,2) }}</h5></div>
									  <div class="col-9 text-end pt-3"> <a href="{{ $urlCart }}" class="btn animate" style="color: #fff;">View Cart</a></div>
							  </div>
							</div>

							{{-- if no item in cart session --}}
							@else
								<div class="text-center">
									<h4>No items in cart</h4>
								</div>
							@endif
						  </div>

							</div>
						  </div>
						</div>


			  </div>
        <!--/ End offcanvas cart -->

		</header>
		{{-- <script>

		// copy script from cart blade
		$(document).ready(function () {
			$('.qty-desc').click(function(e){
			e.preventDefault();
			var ele = $(this);
			var productid = ele.attr("data-id");
           	// var currentRow=$(this).closest("tr");
			// var col1=currentRow.find("td:eq(2)").text();

			$.ajax({
				url: '{{ route('cart.decrease') }}',
				method:"POST",
				data:{
					_token: '{{csrf_token()}}',
					productId: productid
				},
				dataType: 'json',
				success:function(result){
					window.location.reload();
				}
			});
		});

		$('.qty-incr').click(function(e){
			e.preventDefault();
			var ele = $(this);
			var productid = ele.attr("data-id");

			$.ajax({
				url: '{{ route('cart.increase') }}',
				method:"POST",
				data:{
					_token: '{{csrf_token()}}',
					productId: productid
				},
				dataType: 'json',
				success:function(result){
					// console.log(result);
					window.location.reload();
				}
			});
		});
	});

		</script> --}}
