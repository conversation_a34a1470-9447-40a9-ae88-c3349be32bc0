<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta Tag -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name='copyright' content=''>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- Title Tag  -->
	@section('title', $productDetail['productname'])

	
	<!-- StyleSheet -->
	
	@include('./partials/head') 

	<style>
		.product-name1{
			color: #222222;
			font-size: 24px;
			font-weight: 600;
		}
		.productprice{
			font-weight: bold; 
			color: red;
			margin-top: 10px;
			font-size: 24px;
		}
		.desc-container{
			/* color: #8d8d8d;  */
			text-transform: capitalize; 
			font-weight: 400;
			overflow-y: scroll;
		}
		.cardwrapper{
    		max-width: 1100px;
   			margin: 0 auto;
		}
		.imgshowcase{
    		display: flex;
    		width: 100%;
    		transition: all 0.5s ease;
		}
		.imgshowcase img{
			min-width: 100%;
			max-height: 40rem;
		}
		.small-img-group{
			display: flex;
			justify-content: space-between;
		}
		.small-img-col{
			flex-basis: 24%;
			cursor: pointer;
		}
		.cardd{
			position: relative;
			display: flex;
			flex-direction: column;
			min-width: 0;
			word-wrap: break-word;
			background-color: #fff;
			background-clip: border-box;
			border: 1px solid rgba(0, 0, 0, 0.125);
			border-radius: 0.25rem;
		}
		.imgg:hover{
			opacity: .7;
		}
		.product-img:hover{
			opacity: .7;
		}
		.happyhoursection{
			background-color: #F64749;
			padding: 10px;
			margin-bottom: 10px;
		}
		.texthappyhour{
			color: white;
			font-size: 20px;
			font-weight: 600;
		}
		.flex-happyhour{
			display: flex; 
            justify-content: space-between;
		}
		@media only screen and (max-width: 768px) {
		.single-product .product-img {
			height: 200px;
		}
		}

		@media screen and (min-width: 992px){
		.card{
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 1.5rem;
		}
		.cardwrapper{
			height: fit-content;
			display: grid;
			justify-content: center;
			align-items: center;
		}
		.product-imgs{
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
		}
			
		}

		/* product toast */
        .toastProd {
            position: fixed;
            top: 100px;
            left: 30px;
            background-color: #C5FFE1;
            color: #333;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #01a14f;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .toastProd.show {
            opacity: 1;
        }
		
	</style>

	<!-- Clipboard.js -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.10/clipboard.min.js"></script>
</head>

<body class="js">
	
		<!-- Header -->
	
		@include('./partials/header-menu') 

		<!--/ End Header -->
		
		&nbsp;
			
		<section class="container sproduct my-5">
			@if (Session::has('error'))

			<div class="alert alert-danger mt-2 text-center">{{ Session::get('error') }} 
			</div>
			@elseif (Session::has('successAtc'))

			<div class="alert alert-success mt-2 text-center">{{ Session::get('successAtc') }} 
			</div>
			@endif

			<div class="cardd">
				<div class="row" style="margin-left: 10px; margin-right: 10px; margin-top: 25px; margin-bottom: 25px">

					<div class="col-lg-4 col-md-12 col-12 product-imgs" class="product-imgs">
	
						<div class = "img-display">
							<div class = "imgshowcase d-none d-lg-flex" id="image-container">
								@if ($productDetail['attachment'] == "")
									<img class="img-fluid w-100" src="/eShop/images/noimage.png" alt="" style="background-color:rgb(176, 176, 176); padding: 5px; height: 60%;">
								@else
									<a href="{{ config('minishop.product_image_url').$productDetail['attachment'] }} "><img onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';" src= "{{ config('minishop.product_image_url').$productDetail['attachment'] }} " class="img-fluid w-100" alt = "Main product image"></a>
								@endif
							</div>
						</div>
	
						<div class="small-img-group img-select d-none d-lg-flex" style="margin-top: 5px;">
							<div class="small-img-col">
								@if ($productDetail['attachment'] == "")
									<img src="/eShop/images/noimage.png" alt="" width="100%" class="small-img" style="background-color:rgb(230, 230, 230); padding: 3px; height: 100%;">
								@else
									<a class="imgg" href="{{ config('minishop.product_image_url').$productDetail['attachment'] }}"><img style="background-color:rgb(230, 230, 230); padding: 3px; object-fit: cover; height: 100%;" onerror="this.onerror=null;this.src='/eShop/images/noimage.png';" src = "{{ config('minishop.product_image_url').$productDetail['attachment'] }}" width="100%" class="small-img" alt = "Product image 1"></a>
								@endif 
							</div>
	
							<div class="small-img-col" class="imgshowcase d-none d-lg-flex">
								@if ($productDetail['attachmentweb1'] == "")
									<img src="/eShop/images/noimage.png" alt="" width="100%" class="small-img" style="background-color:rgb(230, 230, 230); padding: 3px; height: 100%;">
								@else
									<a class="imgg" href="{{ config('minishop.product_image_url').$productDetail['attachmentweb1'] }}"><img style="background-color:rgb(230, 230, 230); padding: 3px; object-fit: cover; height: 100%;" onerror="this.onerror=null;this.src='/eShop/images/noimage.png';" src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb1'] }}" width="100%" class="small-img" alt = "Product image 2"></a>
								@endif 
							</div>
	
							<div class="small-img-col">
								@if ($productDetail['attachmentweb2'] == "")
									<img src="/eShop/images/noimage.png" alt="" width="100%" class="small-img" style="background-color:rgb(230, 230, 230); padding: 3px; height: 100%;">
								@else
									<a class="imgg" href="{{ config('minishop.product_image_url').$productDetail['attachmentweb2'] }}"><img style="background-color:rgb(230, 230, 230); padding: 3px; object-fit: cover; height: 100%;" onerror="this.onerror=null;this.src='/eShop/images/noimage.png';" src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb2'] }}" width="100%" class="small-img" alt = "Product image 3"></a>
								@endif 
							</div>
	
							<div class="small-img-col">
								@if ($productDetail['attachmentweb3'] == "")
									<img src="/eShop/images/noimage.png" alt="" width="100%" class="small-img" style="background-color:rgb(230, 230, 230); padding: 3px; height: 100%;">
								@else
									<a class="imgg" href="{{ config('minishop.product_image_url').$productDetail['attachmentweb3'] }}"><img style="background-color:rgb(230, 230, 230); padding: 3px; object-fit: cover; height: 100%;" onerror="this.onerror=null;this.src='/eShop/images/noimage.png';" src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb3'] }}" width="100%" class="small-img" alt = "Product image 4"></a>
								@endif 
							</div>
						</div>
	
						{{-- mobile view for images--}}
						<div class="mobile-img d-flex d-sm-flex d-lg-none">
							@if ($productDetail['attachment'] == "")
								<img class="default-img" src="/eShop/images/noimage.png" alt="#">
								@else
								<img src= "{{ config('minishop.product_image_url').$productDetail['attachment'] }} " onerror="this.onerror=null;this.src='https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';" alt = "Main product image">
								@endif
							@if ($productDetail['attachmentweb1'] == "")
								<img class="default-img" src="/eShop/images/noimage.png" alt="#">
								@else
								<img src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb1'] }} " alt = "product image 1">
								@endif
							@if ($productDetail['attachmentweb2'] == "")
								<img class="default-img" src="/eShop/images/noimage.png" alt="#">
								@else
								<img src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb2'] }} " alt = "product image 2">
								@endif
							@if ($productDetail['attachmentweb3'] == "")
								<img class="default-img" src="/eShop/images/noimage.png" alt="#">
								@else
								<img src = "{{ config('minishop.product_image_url').$productDetail['attachmentweb3'] }} " alt = "product image 3">
							@endif
						</div>
					</div>
	
					<div class="col-lg-8 col-md-12 col-12 left-productinfo">

						@if($productDetail['PROMOSIHAPPYHOUR'] == "Y")
							{{-- happy hour color --}}
							<div class="happyhoursection">
								<div class="flex-happyhour">
									<div class="texthappyhour">
										HAPPY HOUR
									</div>

									<div id="countdown" style="text-align: center; color: white">
									</div>
									
								</div>
							</div>

						@else
						@endif

						<h3 class="product-name1">{{ $productDetail['productname'] }}</h3>
	
						<div>
							@php
								// if happyhour is Y, use promotionprice. else use price
								$harga;
								if($productDetail['PROMOSIHAPPYHOUR'] == "Y"){
									$harga = $productDetail['promotionprice'];
								}
								else{
									$harga = $productDetail['price'];
								}
							@endphp
						  <h2 class = "productprice">RM {{ $harga }} </h2>
						</div>
			  
						<div class = "product-detail">
							&nbsp;
						  <h4 style="font-weight: 500; font-size: 16px">Description: </h4>
						  @if ($productDetail['productdesc'] != "")
							  
							<div class="desc-container">
								{!! nl2br($productDetail['productdesc']) !!} 

							</div>
							@else
								<p>No description available</p>
							@endif
	
							<hr style="background-color: #e3e3e3">
							
						<?php
							$statusstok_desc;
							$statusstok_color;
	
						if($productDetail['hideActualQuantity'] == "Y"){
							$statusstok_desc = "Ready Stock";
							$statusstok_color = "#32CD32";
						}
						elseif($productDetail['statusstok'] == "Y"){
							$statusstok_desc = "Ready Stock";
							$statusstok_color = "#32CD32";
						}
						elseif($productDetail['bilstok'] > 0){
							$statusstok_desc = $productDetail['bilstok'] . ' Unit Available';
							$statusstok_color = "#ffd700";
						}
						else{
							$statusstok_desc = "Out Of Stock";
							$statusstok_color = "red";
						}
	
						?>
	
						  <ul>
							<li>Brand : <span style="text-transform: capitalize">{{ strtolower($productDetail['productbrand']) }} </span></li>
							<li>Availability : <span style="color: {{ $statusstok_color }}; font-weight: bold"> {{ $statusstok_desc }} </span></li>
							<li>Category : <span style="text-transform: capitalize">{{ strtolower($productDetail['productcategory']) }} </span></li>
							<li>Weight : <span>{{ $productDetail['weight'] }} gram </span></li>
						  </ul>
						</div>
			  
						<div class="purchase-info">
							
							<form action="{{ route('cartMask.store', ['url_param' => $url_param]) }}" method="post">
								<input name="_token" type="hidden" value="{{ csrf_token() }}"/>
								<input name="currentStockCount" type="hidden" value="{{ $productDetail['bilstok'] }}"/>
								<input name="currentStockStatus" type="hidden" value="{{ $productDetail['statusstok'] }}"/>
								<input name="isHappyHour" type="hidden" value="{{ $productDetail['PROMOSIHAPPYHOUR'] }}"/>
								<input name="happyHourPrice" type="hidden" value="{{ $productDetail['promotionprice'] }}"/>
								
								<input name="cartItems[]" type="hidden" value="{{ json_encode($productDetail) }}"/>
	
								
								<label for="html" class="fw-bold" style="margin-right: 1.5rem;">Quantity : </label>
								<button type="button" class="btn-light btn-number qty-desc" style="height: 38px; width:38px;" data-type="minus" onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
										<span class="no-highlight px-1 cursor-pointer hover-opacity-50">-</span>
								</button>
									<input type="number" max="{{ $productDetail['bilstok'] != '-100' ? $productDetail['bilstok'] : '999' }}" min="1" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" name="quantity" value="1" min="0" max="1000"/>
								<button type="button" class="btn-light qty-incr" data-type="plus" style="height: 38px; width:38px;" onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
										<span class="no-highlight px-1 cursor-pointer hover-opacity-50">+</span>
								</button>
	
	
									<br><br>
								<div class="row col-12">
									<button type = "submit" class = "col-6 btn animate btn-lg" style="padding: 0.4rem; margin-left: 10px;">
										Add to Cart <i class = "fa fa-shopping-cart"></i>
									</button>
								</div>
								
							</form>
							<div class="row col-12", style="padding-top: 5px">
								<button type = "submit" class = "col-6 btn animate btn-lg" id="copyproduct" style="padding: 0.4rem; margin-left: 10px; background-color: #F6F7FB; color: rgb(86, 86, 86)">
									Copy Product URL
								</button>
							</div>
							
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Toast #1 (copy link) -->
		<div id="toast-container"></div>

		&nbsp;
		&nbsp;

		<div class="container-product-detail">
			<div class="col-6">
				@php
					$urlShopGrid = "";

					if(isset($agentpid)){
						$urlShopGrid = route('koleksi-to-grid-agent', ['url_param' => $url_param, 'koleksiid' => 'all-collection', 'agent_pid' => $agentpid]);
					} else{
						$urlShopGrid = route('koleksi-to-grid', ['url_param' => $url_param, 'koleksiid' => 'all-collection']);
					}
            	@endphp
				<div >
					<h4><a href="{{ $urlShopGrid }}"> <h4 style="margin-top: 20px">More Products</h4></a>
				</div>
			</div>

			<div>

				<div class="row">
					@foreach ($productList as $item)

					@php
						$urlProductDetail = "";
		
						if(isset($agentpid)){
							$urlProductDetail = route('product-detailsAgent', ['url_param' => $url_param , 'productid' => $item['id'], 'agent_pid' => $agentpid]);
						} else{
							$urlProductDetail = route('product-details', ['url_param' => $url_param , 'productid' => $item['id']]);
						}
					@endphp
					
						
					<div class="col-lg-3 col-md-6 col-6 " id="products_div">
							<a href="{{ $urlProductDetail }}">
						<div class="single-product">
							<div class="product-img">
								<a href="{{ $urlProductDetail }}">
									<img class="fluid-img" style="align-content: center; height: 250px;" src="https://corrad.visionice.net/bizapp/upload/product/{{ $item['attachment'] }}" alt="#">
								</a>
							
							</div>
						</div>
							<p class="title-more-product">{{ $item['productname'] }}</p>
							@php
								// if happyhour is Y, use promotionprice. else use price
								$harga;
								if($item['PROMOSIHAPPYHOUR'] == "Y"){
									$harga = $item['promotionprice'];
								}
								else{
									$harga = $item['price'];
								}
							@endphp
							<p class="price-more-product">RM {{ $harga }}</p>
							</a>

					</div>
				
					@endforeach
				</div>

			</div>


		</div>
		
		&nbsp;
			
		<!-- Start Footer Area -->
			@include('./partials/footer-menu') 


		<!-- /End Footer Area -->
	
		@include('./partials/footer') 
		<script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

		<script>
		$('.imgshowcase').magnificPopup({
			delegate: 'a',
			type: 'image',
			gallery:{
				enabled:true,
				tCounter: '<span class="mfp-counter">%curr% of %total%</span>' 
			}
			});

			$('.img-select').magnificPopup({
			delegate: 'a',
			type: 'image',
			gallery:{
				enabled:true,
				tCounter: '<span class="mfp-counter">%curr% of %total%</span>' 
			}
			});

		$('.mobile-img').slick({
			dots: true,
			slidesToShow: 1,

		}
		);

		// ------------ TIMER COUNTDOWN ------------
		var happyHour = {!! json_encode($happyhour) !!};

		// Update the count down every 1 second
		var x = setInterval(function() {

			if(happyHour.length > 0){
				// Set the date we're counting down to
                var happyHourEnd = happyHour[0]['enddatetime'];
                var countDownDate = new Date(happyHourEnd).getTime();

				// Get today's date and time
				var now = new Date().getTime();

				// Find the distance between now and the count down date
				var distance = countDownDate - now;

				// Time calculations for days, hours, minutes and seconds
				var days = Math.floor(distance / (1000 * 60 * 60 * 24));
				var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
				var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
				var seconds = Math.floor((distance % (1000 * 60)) / 1000);

				// Output the result in an element with id="demo"
				strHtml = 

				'<div style="color: white; font-weight: 500">' +
						'<i class="fa fa-clock-o" style="padding-right: 5px; font-size: 18px;"></i>' + 
						'Ends In: ' + days + 'days ' + hours + 'hours ' + minutes + 'min ' + seconds + 'sec' +
				'</div>';

				document.getElementById("countdown").innerHTML = strHtml;

							// If the count down is over, write some text
			if (distance < 0)
			{
				clearInterval(x);
				document.getElementById("countdown").innerHTML = "EXPIRED";
			}}
			}, 1000);
   
		</script>

<script>
	// Initialize Clipboard.js
	var clipboard = new ClipboardJS('#copyproduct', {
	  text: function() {
		// Return the text you want to copy
		return window.location.href;
	  }
	});
  
	// Show popover on success and hide after a delay
	clipboard.on('success', function(e) {
	  var popover = new bootstrap.Popover(e.trigger);
	  showToast();
	}); 

	function showToast(){
		var toast = document.createElement('div');
		toast.classList.add('toastProd');

		var toastText = document.createElement('span');
		toastText.innerText = "Product URL have been copied to clipboard";
		toast.appendChild(toastText);

		var container = document.getElementById('toast-container');
		container.appendChild(toast);

		// Show the toast element 
		setTimeout(function() {
			toast.classList.add('show');
		}, 500); // Delay the display of each toast message

		// Remove the toast element after 8 seconds
		setTimeout(function() {
			toast.classList.remove('show');
			setTimeout(function() {
				container.removeChild(toast);
			}, 300);
		}, 3000); 
	}

  </script>

</body>
</html>