<!DOCTYPE html>
<html lang="zxx">
    <head>
        <!-- Meta Tag -->
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name='copyright' content=''>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <!-- Link to Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Link to Bootstrap JavaScript -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.min.js"></script>

        <!-- Title Tag  -->
        @section('title', 'Shop Grid')
        <!-- Favicon -->

        <!-- StyleSheet -->
        @include('./partials/head')

    <style>
        #pagination {
            display: flex;
            justify-content: center
        }
        .titlehappyhour{
            color: red;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .deschappyhour{

        }
        .product-area{
            padding-top: 40px; padding-bottom:40px;
        }
        .a1{
            text-align: center; font-size: 20px; height: 30px; color: red;
        }
        .ax{
            background-color:red;
        }

        @media only screen and (max-width: 450px) {
            .product-area{
                padding-top: 0px; padding-bottom: 40px;
            }
            .a1{
                text-align: center; font-size: 30px; height: 30px; color: red;
            }
            .ax{
                background-color:red;
            }
            /* product toast */
            .toastProd {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background-color: #C5FFE1;
                color: #333;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #01a14f;
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
            }
        }
        /* .product-statusdesc1{
            padding-right: 20px;
            padding-left: 20px;
            padding-top: 5px;
            padding-bottom: 5px;
            font-weight: 500;
            font-size: 13px;
            border-radius: 30px;
            color: #ffffff;
        }

        @media (max-width: 450px) {
            .product-statusdesc1 {
                padding-left: 15px;
                padding-right: 15px;
                font-size: 11px;
            }
        }
        .product-name{
            font-size: 14px;
            line-height: 1.5;
            margin-top: 10px;
        }
        .product-price{
            font-size: 15px;
            font-weight: 600;
        }
        .product_categorydesc{
            font-size: 12px;
            text-transform: capitalize;
        }
        .row-price {
            display: flex;
            flex-direction: row;
            margin-top: 15px;
        }
        .product-img:hover{
            opacity: .7;
        }

        .container-product{
            width: 850px;
            margin: 30px auto;
            columns: 3;
            column-gap: 0px;
        }
        .box{
            width: 265px;
            padding: 15px;
            background-color: white;
            border: 1px solid rgb(228, 228, 228);
            margin-bottom: 10px;
            break-inside: avoid;
        }
        .box img{
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        @media only screen and (min-width: 820px) and (max-width: 991px){
            .container-product{
                width: 100%;
                margin: 30px auto;
                columns: 2;
                column-gap: 0px;
            }
            .box{
                width: 180px;
                padding: 20px;
                background-color: white;
                border: 1px solid rgb(228, 228, 228);
                margin-bottom: 10px;
                break-inside: avoid;
            }
        }

        @media only screen and (max-width: 767px) {
            .container-product{
                width: 100%;
                margin: 30px auto;
                columns: 2;
                column-gap: 0px;
            }
            .box{
                width: 180px;
                padding: 20px;
                background-color: white;
                border: 1px solid rgb(228, 228, 228);
                margin-bottom: 10px;
                break-inside: avoid;
            }

            }
            @media only screen and (max-width: 450px) {

            .container-product{
                width: 100%;
                margin: 30px auto;
                columns: 2;
                column-gap: 0px;
            }
            .box{
                width: auto;
                padding: 10px;
                background-color: white;
                border: 1px solid rgb(228, 228, 228);
                margin-bottom: 10px;
                break-inside: avoid;
                margin-right: 10px;
            }
        }

        @media only screen and (max-width: 400px) {
            .container-product{
                width: 100%;
                margin: 30px auto;
                columns: 2;
                column-gap: 0px;
            }
            .box{
                width: auto;
                padding: 10px;
                background-color: white;
                border: 1px solid rgb(228, 228, 228);
                margin-bottom: 10px;
                break-inside: avoid;
                margin-right: 10px;
            }
            .box img{
                width: 100%;
                height: 150px;
                object-fit: cover;
            }
        } */

        /* .box {
            position: relative;
            overflow: hidden;
        }
        .box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .box:hover img {
            transform: scale(1.4);
        }

        .boxhappy {
            position: relative;
            overflow: hidden;
        }
        .boxhappy img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .boxhappy:hover img {
            transform: scale(1.4);
        } */

        .box, .boxhappy {
            position: relative;
            overflow: hidden;
        }

        .box img, .boxhappy img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .box:hover img, .boxhappy:hover img {
            transform: scale(1.1);
        }

        /* add to cart button */
        .btn {
            position: absolute;
            bottom: 50%;
            right: 0;
            margin: 5px;
            background-color: #E6E8FA;
            border: 1px solid black;
        }
        .btn::after {
            content: "+";
            color: #000000;
        }
        .btn:hover {
            transform: scale(1.1);
        }
        .btn:hover::after {
            content: "Add to Cart";
            color: #FFFFFF;
        }

        /* Styles for screens with a maximum width of 768px (adjust as needed) */
        @media (max-width: 768px) {
            .btn::after {
                content: "+";
                color: #000000;
            }
            .btn:hover {
            transform: scale(1.1);
            }
            .btn:hover::after {
                content: "Add to Cart";
                color: #FFFFFF;
            }
        }

        /* cart button when scroll down */
        .cart-button {
            position: fixed;
            bottom: -60px;
            right: 10px;
            z-index: 9999;
            width: 40px;
            height: 40px;
            background-color: #000000; /* #E6E8FA */
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            color: #FFFFFF;
            /* overflow: hidden; */
        }
        .cart-button.show {
            bottom: 60px;
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }
        .cart-button a i.fa-shopping-bag {
            color: #FFFFFF; /* #FF0000 */
        }

        .cart-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 23px;
            height: 23px;
            background-color: #FF0000; /* #000000 */
            color: #FFFFFF;
            text-align: center;
            line-height: 23px;
            border-radius: 50%;
            font-size: 12px;
        }

        /* toast container */
        .custom-toast-container {
            max-width: 300px;
            white-space: nowrap;
        }
        /* toast flying-in animation */
        @keyframes flyIn {
            /* fly in right */
            from {
                transform: translateX(100%);
            }
            to {
                transform: translateX(0);
            }
        }
        .toast.fly-in {
            animation: flyIn 0.2s ease-in-out;
        }
        /* toast flying-out animation */
        @keyframes flyOut {
            /* fly out right */
            from {
                transform: translateX(0);
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        .toast.fly-out {
            animation: flyOut 0.2s ease-in-out;
        }

         /* product toast */
         .toastProd {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #C5FFE1;
            color: #333;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #01a14f;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .toastProd.show {
            opacity: 1;
        }

    </style>

    </head>
    <body class="js">

        <!-- Preloader -->
        {{-- <div class="preloader">
            <div class="preloader-inner">
                <div class="preloader-icon">
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div> --}}
        <!-- End Preloader -->

        <!-- Header -->
        @include('./partials/header-menu')
        <!--/ End Header -->

        {{-- Temporary Debug Section for Collections - Remove after testing --}}
        @if(config('app.debug'))
        <div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc; font-size: 12px;">
            <strong>Collections Debug Info:</strong><br>
            Collection ID (temp): {{ $temp ?? 'N/A' }}<br>
            Product List Count: {{ is_array($productList) ? count($productList) : 'Not Array' }}<br>
            arrCollProd Keys: {{ implode(', ', array_keys($arrCollProd ?? [])) }}<br>
            arrCollProd[0] Count: {{ isset($arrCollProd[0]) ? count($arrCollProd[0]) : 'N/A' }}<br>
            @if(isset($productList[0]))
                Sample Product: {{ $productList[0]['productname'] ?? 'N/A' }}<br>
            @endif
        </div>
        @endif

        <!-- Toast #1 (ATC alert) -->
        <div class = "toast-container position-fixed top-50px end-0 p-3 custom-toast-container" style="z-index: 1050;"></div>

        <!-- Product Style -->
        <section class="product-area shop-sidebar shop section">
            <div class="container">

                <div class="row">

                    {{-- widget kiri --}}
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="shop-sidebar">
                            <!-- Single Widget -->
                            <div class="single-widget category">
                                <h3 class="title">List Collections</h3>
                                <ul class="categor-list">
                                    @forelse ($collectionList as $item)

                                        {{-- display noofproducts > 0 && all collection only --}}
                                        @if ($item['noofproducts'] > 0)
                                            <li><a href="#" id="collectionHref" onclick="collectionHref({{ $item['id'] }})" data-value="{{ $item['id'] }}">• {{ strtolower($item['koleksi']) }} ({{ $item['noofproducts'] }})  </a></li>
                                        @elseif ($item['koleksi'] == "ALL COLLECTION")
                                            <li><a href="#" id="collectionHref" onclick="collectionHref({{ $item['id'] }})" data-value="{{ $item['id'] }}">• {{ strtolower($item['koleksi']) }} </a></li>
                                        @endif

                                        <input type="hidden" value="" id="koleksiid">
                                        <input type="hidden" value="" id="productlistLength">
                                    @empty
                                        <li>INVALID COLLECTION</li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>

                        <div class="shop-sidebar">
                            @if ($happyhour == [])

                            @else
                            <div class="single-widget category" style="margin-top: 20px;">
                                <h3 class="title">Happy Hour</h3>
                                <div class="titlehappyhour"> {{ $happyhour[0]['promotionname'] }}</div>
                                <div class="deschappyhour"> {{ $happyhour[0]['promotiondesc'] }}</div>

                                <div id="countdown" style="text-align: center; margin: auto; padding: 10px; margin-top: 15px;">
                                </div>

                            </div>
                            @endif

                        </div>
                    </div>

                    {{-- widget kanan --}}
                    <div class="col-lg-9 col-md-8 col-12 ">

                        {{-- widget sort --}}
                        <div class="row d-none d-md-block">
                            <div class="col-12">
                                <!-- Sorting -->
                                <div class="shop-top">
                                    <div class="shop-shorter">
                                        <div class="single-shorter">
                                            <label>Sort By :</label>
                                            <select id="sorttype">
                                                <option value="CHARLOWHIGH">Alphabet (A-Z)</option>
                                                <option value="CHARHIGHLOW">Alphabet (Z-A)</option>
                                                <option value="AMOUNTLOWHIGH">Price (Low to High)</option>
                                                <option value="AMOUNTHIGHLOW">Price (High to Low)</option>
                                                <option value="ENTRYOLDNEW">Date (Old to New)</option>
                                                <option value="ENTRYNEWOLD">Date (New to Old)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <!--/ End Sorting -->

                            </div>
                        </div>

                        <div class="container-product" id="productRow"></div>

                        <div id="pagination"></div>

                        <!-- add to cart button when scrolling down -->
                        @php
                            $urlCart = "";
                            $cartItemCount = 0;

                            if(isset($agentpid)){
                                $urlCart = route('cartMaskAgent',['url_param' => $url_param, 'agent_pid' => $agentpid]);
                            }
                            else{
                                $urlCart = route('cartMask',['url_param' => $url_param]);
                            }

                            if(session()->has('cart')){
                                if(count(session('cart')) > 0){
                                    $cartItemCount = count(session('cart'));
                                }
                                else{
                                    $cartItemCount = 0;
                                }
                            }
                        @endphp

                        <div class="cart-button">
                            <a href="{{ $urlCart }}"><i class="fa fa-shopping-bag fa-lg"></i> <span class="cart-badge"> {{ $cartItemCount }} </span></a>
                        </div>

                        <!-- Toast #2 (Product Sold) -->
                        <div id="toast-container-2"></div>

                    </div>
                </div>
            </div>

        </section>
        <!--/ End Product Style 1  -->

        @include('./partials/footer-menu')
        @include('./partials/footer')

        <script>
            function collectionHref(koleksiid)
            {
                $("a[id*=collectionHref]").removeAttr("style");
                $("[data-value="+koleksiid+"]").attr("style", "color:red");
                $("#koleksiid").val(koleksiid); // set hidden input value for koleksiid, will be used in dropdown onchange
                document.querySelector('#sorttype').value = "CHARLOWHIGH"; //default value for sorttype, ubah value at dropdown change
                $('select').niceSelect('update');
                var searchproduct = ''; //default value for searchproduct

                populateProduct(koleksiid, sorttype, searchproduct);
                paginate();
            }

            function populateProduct(koleksiid, sorttype, searchproduct)
            {
                $("#productRow").html('');
                var arrCollProd = {!! json_encode($arrCollProd) !!};

                // Debug logging
                console.log('populateProduct called with koleksiid:', koleksiid);
                console.log('arrCollProd keys:', Object.keys(arrCollProd));
                console.log('arrCollProd[0] length:', arrCollProd[0] ? arrCollProd[0].length : 'undefined');
                console.log('arrCollProd[koleksiid] length:', arrCollProd[koleksiid] ? arrCollProd[koleksiid].length : 'undefined');

                var productList = arrCollProd[koleksiid];
                var url_param = {!! json_encode($url_param) !!};
                var agentpid = {!! json_encode($agentpid ?? "") !!};
                var agent_pid = {!! json_encode($agent_pid ?? "") !!};

                var tempProductList = [];

                if(searchproduct && searchproduct != null)
                {
                    for(var i=0; i<productList.length; i++)
                    {
                        if(productList[i]["productname"].toLowerCase().match(searchproduct.toLowerCase()))
                        {
                            tempProductList.push(productList[i]);
                        }
                    }
                    productList = tempProductList;
                }

                if(productList && productList.length > 0)
                {
                    if (sorttype === "CHARLOWHIGH")
                    {
                        productList.sort(function(a, b) {
                            return a['productname'] - b['productname'];
                        });
                    }
                    else if (sorttype === "CHARHIGHLOW")
                    {
                        productList.sort(function(a, b) {
                            return a['productname'] - b['productname'];
                        }).reverse();
                    }
                    else if (sorttype === "AMOUNTLOWHIGH")
                    {
                        productList.sort(function(a, b) {
                            return a['price'] - b['price'];
                        });
                    }
                    else if (sorttype === "AMOUNTHIGHLOW")
                    {
                        productList.sort(function(a, b) {
                            return a['price'] - b['price'];
                        }).reverse();
                    }
                    else if (sorttype === "ENTRYOLDNEW")
                    {
                        productList.sort(function(a, b) {
                            return a['datecreated'] - b['datecreated'];
                        });
                    }
                    else if (sorttype === "ENTRYNEWOLD")
                    {
                        productList.sort(function(a, b) {
                            return a['datecreated'] - b['datecreated'];
                        }).reverse();
                    }
                    else
                    {
                        productList.sort(function(a, b) {
                            return a['productname'] - b['productname'];
                        });
                    }

                    $("#productlistLength").val(productList.length);
                    for(var i=0; i<productList.length; i++)
                    {

                        // check if is affiliate
                        if(agentpid != ""){
                            var url = '{{ route("product-detailsAgent", [":upa", ":slug", ":slugx"]) }}';
                            url = url.replace(':slug', productList[i]["id"]);
                            url = url.replace(':upa', url_param);
                            url = url.replace(':slugx', agentpid);
                        } else {
                            var url = '{{ route("product-details", [":upa", ":slug"]) }}';
                            url = url.replace(':slug', productList[i]["id"]);
                            url = url.replace(':upa', url_param);
                        }


                        var urlCart = '{{ route("cartMask.store", ":slug") }}';
                        urlCart = urlCart.replace(':slug', url_param);

                        var imageSrc = '/eShop/images/noimage.png';
                        var imageSrcNoImg = 'https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg';

                        if (productList[i]['attachment'] != '' ){
                            imageSrc = 'https://corrad.visionice.net/bizapp/upload/product/'+ productList[i]["attachment"] +'';
                        } else {
                            imageSrc = '/eShop/images/noimage.png';
                        }

                        if(productList[i]['PROMOSIHAPPYHOUR'] == 'Y' ){
                            strhtml = '<div class="boxhappy">' +
                                '<a href="' + url + '">' +
                                    '<img class="fluid-img" src="'+ imageSrc +'" onerror="this.onerror=null;this.src='+"'/eShop/images/noimage.png'"+'">' +
                                '</a>';
                        }
                        else{
                            strhtml = '<div class="box">' +
                                '<a href="' + url + '">' +
                                    '<img class="fluid-img" src="'+ imageSrc +'" onerror="this.onerror=null;this.src='+"'/eShop/images/noimage.png'"+'">' +
                                '</a>';
                        }

                        // ADD TO CART BUTTON
                        if(productList[i]['attachment'] != ''){
                            strhtml = strhtml.concat('<div style="position: relative;"> <button id="atcButton" class="product-statusdesc1 btn addToCartBtn" data-productid="' + productList[i]["id"] +
                            '"> </button> </div>');

                        } else {
                            strhtml = strhtml.concat('<div style="position: relative;"> <button id="atcButton" class="product-statusdesc1 btn addToCartBtn" data-productid="' + productList[i]["id"] +
                            '"> </button> </div>');
                        }

                        // STOCK LABEL
                        strhtml = strhtml.concat('<div class="mt-3">');
                        if(productList[i]["hideActualQuantity"] === "Y")
                        {
                            strhtml = strhtml.concat('<span class="stock-label product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                        }
                        else if(productList[i]["statusstok"] === "Y")
                        {
                            strhtml = strhtml.concat('<span class="stock-label product-statusdesc1" style="background-color: #32CD32;"> READY STOCK </span>');
                        }
                        else if(productList[i]["bilstok"] > 0)
                        {
                            strhtml = strhtml.concat('<span class="stock-label product-statusdesc1" style="background-color: #ffc700;">'+ productList[i]['bilstok'] +' UNIT AVAILABLE </span>');
                        }
                        else
                        {
                            strhtml = strhtml.concat('<span class="stock-label product-statusdesc1" style="background-color: red;"> OUT OF STOCK </span>');
                        }
                        strhtml = strhtml.concat('</div>');

                        // PRODUCTNAME LABEL
                        strhtml = strhtml.concat('<h3 class="product-name"><a style="font-weight: 600" >'+productList[i]["productname"]+'</a></h3>');

                        // PRODUCT CATEGORY LABEL
                        strhtml = strhtml.concat('<div class="mt-0 mb-4">');
                        if (productList[i]["productcategorycodedesc"] === null)
                        {

                        }
                        else
                        {
                            strhtml = strhtml.concat('<i class="text-secondary" style="font-size: 12px">#'+ productList[i]["productcategorycodedesc"].replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();}) +'</i>');
                        }
                        strhtml = strhtml.concat('</div>');

                        // PRICE LABEL
                        if(productList[i]['PROMOSIHAPPYHOUR'] == "Y")
                        {
                            if(Number(productList[i]['lesspercenthappyhour']) > 100)
                            {
                                strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                                    '<span style="color: red; font-size: 14px;">100% off Happy Hour</span>');
                            }
                            if(Number(productList[i]['lesspercenthappyhour']) > 0)
                            {
                                strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                                    '<span style="color: red; font-size: 14px;">' + productList[i]['lesspercenthappyhour'] + '% off Happy Hour</span>');
                            }
                            if(Number(productList[i]['lesspercenthappyhour']) === 0){
                                strhtml = strhtml.concat('RM ' + productList[i]["promotionprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>');
                            }
                        }
                        else
                        {
                            if(Number(productList[i]['diskaunprice']) <= Number(productList[i]['price']) && productList[i]['discounttype'] != "NO_DISCOUNT"){
                                if(Number(productList[i]['lesspercent']) > 100){
                                    strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                                    '<span style="color: red; font-size: 14px;">100% with discount code </span>');
                                }
                                // else if(Number(productList[i]['lesspercent']) > 0){
                                //     strhtml = strhtml.concat('RM ' + productList[i]["diskaunprice"] + '  <s style="font-weight: 250;">RM '+ productList[i]["price"] + '</s> <br>' +
                                //                     '<span style="color: red; font-size: 14px;">' + productList[i]['lesspercent'] + '% with discount code </span>');
                                // }
                                else if(Number(productList[i]['lesspercent']) > 0){
                                    strhtml = strhtml.concat('RM ' + productList[i]["price"] );
                                }
                            }
                            else{
                                strhtml = strhtml.concat('RM ' + productList[i]["price"]);
                            }
                        }

                        // SOLD ITEM INDICATOR
                        if(productList[i]['itemsoldindicator'] == "1")
                        {
                            strhtml = strhtml.concat('<div class="row col-auto justify-content-end pt-2 px-2 pb-0 mb-0" style="color: red; font-size: 15px; font-weight: 500;">'+ productList[i]['itemsold'] +' sold</div></div>');
                        }

                        $("#productRow").append(strhtml);
                    }
                }
                else
                {
                    strhtml ='<div class="row justify-content-center">'+
                                '<div class="col-auto">' +
                                    '<h2>No products</h2>' +
                                '</div>' +
                            '</div>';

                    $("#productRow").append(strhtml);
                }
            }

            function paginate()
            {
                var productlistLength = $("#productlistLength").val();
                var itemPerPage = 20;
                // console.log(productlistLength);
                $(".container-product .box").slice(20).hide();
                    $('#pagination').pagination({
                        // Total number of items present
                        // in wrapper class
                        items: productlistLength,

                        // Items allowed on a single page
                        itemsOnPage: itemPerPage,
                        onPageClick: function (noofele) {
                            $(".container-product .box").hide()
                                .slice(itemPerPage*(noofele-1),
                                itemPerPage+ itemPerPage* (noofele - 1)).show();
                    }
                });
            }

            $(document).ready(function() {

                var tempx = {!! json_encode($temp) !!};

                if(tempx == 'all-collection'){
                    coll_id = "0";
                }
                else {
                    coll_id = {{$temp}};
                }

                $("[data-value="+coll_id+"]").attr("style", "color:red");
                var koleksiid = coll_id; //default value for koleksiid
                var sorttype = 'CHARLOWHIGH'; //default value for sorttype
                var searchproduct = ''; //default value for searchproduct

                $("#koleksiid").val(koleksiid); // set hidden input value for koleksiid to default value

                populateProduct(koleksiid, sorttype, searchproduct);
                paginate();
            });

            $("#sorttype").change(function (event) {
                var sorttype = $(this).val();
                var koleksiid = $("#koleksiid").val();
                var searchproduct = ''; //default value for searchproduct

                console.log(sorttype);
                console.log(koleksiid);

                populateProduct(koleksiid, sorttype, searchproduct);
                paginate();
            });

            // JavaScript code
            function search_product() {
                var input = document.getElementById('searchbar').value;
                var searchproduct = input.toLowerCase();
                var koleksiid = 0;
                var sorttype = "CHARLOWHIGH";

                $("a[id*=collectionHref]").removeAttr("style");
                $("[data-value='0']").attr("style", "color:red");
                $("#koleksiid").val(koleksiid); // set hidden input value for koleksiid to default value
                document.querySelector('#sorttype').value = sorttype; //default value for sorttype, ubah value at dropdown change
                $('select').niceSelect('update');

                populateProduct(koleksiid, sorttype, searchproduct);
            }

            function search_product_mobile() {
                var input = document.getElementById('searchbar_mobile').value;
                var searchproduct = input.toLowerCase();
                var koleksiid = 0;
                var sorttype = "CHARLOWHIGH";

                $("a[id*=collectionHref]").removeAttr("style");
                $("[data-value='0']").attr("style", "color:red");
                $("#koleksiid").val(koleksiid); // set hidden input value for koleksiid to default value
                document.querySelector('#sorttype').value = sorttype; //default value for sorttype, ubah value at dropdown change
                $('select').niceSelect('update');

                populateProduct(koleksiid, sorttype, searchproduct);
            }

        // ------------ TIMER COUNTDOWN ------------
        var happyHour = {!! json_encode($happyhour) !!};

        // Update the count down every 1 second
        var x = setInterval(function() {
            if(happyHour.length > 0){
                // Set the date we're counting down to
                var happyHourEnd = happyHour[0]['enddatetime'];
                var countDownDate = new Date(happyHourEnd).getTime();
                // Get today's date and time
                var now = new Date().getTime();

                // Find the distance between now and the count down date
                var distance = countDownDate - now;

                // Time calculations for days, hours, minutes and seconds
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                 // Output the result in an element with id="demo"
                strHtml =
                    '<div>' +
                        '<div style="display: flex; justify-content: space-around;">' +

                            '<div class="column" style="padding: 5px">' +
                                '<div class="a1">' +
                                    days +
                                '</div>' +

                                '<div style="font-size: 13px; color: red">Days' +
                                '</div>' +
                            '</div>' +

                            '<div class="column" style="padding: 5px">' +
                                '<div class="a1">' +
                                    hours +
                                '</div>' +

                                '<div style="font-size: 13px; color: red">Hours' +
                                '</div>' +
                            '</div>' +

                            '<div class="column" style="padding: 5px">' +
                                '<div class="a1">' +
                                    minutes +
                                '</div>' +

                                '<div style="font-size: 13px; color: red">Minutes' +
                                '</div>' +
                            '</div>' +

                            '<div class="column" style="padding: 5px">' +
                                '<div class="a1">' +
                                    seconds +
                                '</div>' +

                                '<div style="font-size: 13px; color: red">Seconds' +
                                '</div>' +
                            '</div>' +

                        '</div>' +
                    '</div>';

                document.getElementById("countdown").innerHTML = strHtml;

                // If the count down is over, write some text
                if (distance < 0)
                {
                    clearInterval(x);
                    document.getElementById("countdown").innerHTML = "EXPIRED";
                }
            }
        }, 1000);

        </script>

        <script>
        $(document).on('click', '.addToCartBtn', function(e) {
                e.preventDefault();
                var productid = $(this).data('productid');
                var url_param = {!! json_encode($url_param) !!};
                var pid_mask = {!! json_encode($pid ?? "") !!};
                // console.log(pid_mask);

                $.ajax({
                    url: '{{ route("shop-grid-atc", ["url_param" => $url_param]) }}',
                    method: 'POST',
					data: {
						_token: '{{ csrf_token() }}',
                        product_id: productid,
                        pid_mask: pid_mask,
                        // url_paran: url_param,
                        url_param: url_param,
                    },
                    dataType: 'json',
                    success: function (response) {
                        var successMsg = response.successAtc;
                        var errorMsg = response.errorAtc;
                        var cartItemCount = response.cartItemCount;

                        if(successMsg) {
                            console.log('Success message: ' + successMsg);
                            console.log('Total item in cart: ' + cartItemCount);

                            // update cart count
                            $('.total-count').text(cartItemCount);
                            $('.cart-badge').text(cartItemCount);

                            // Create a unique ID for each success toast
                            var toastId = 'success-toast-' + new Date().getTime();

                            var successToast = $('<div id="' + toastId + '" class="toast text-white fly-in" role="alert" aria-live="assertive" aria-atomic="true" data-autohide="true" data-delay="3000" style="background-color: #2AB06F; box-shadow: 0 0 10px rgba(0,0,0,0.2);">');
                            successToast.append('<div class="toast-body text-center">' + successMsg + '</div>');

                            // Append the toast to the toast container
                            $('.toast-container').append(successToast);

                            // Show the toast
                            successToast.toast('show');

                            // Remove the toast with "fly-out" animation after 3 seconds
                            setTimeout(function () {
                                successToast.removeClass('fly-in').addClass('fly-out');
                                setTimeout(function () {
                                    successToast.toast('hide');
                                }, 200); // Animation duration
                            }, 3000);

                        } else if(errorMsg) {
                            console.log('Error message: ' + errorMsg);

                            // Create a unique ID for each error toast
                            var toastId = 'error-toast-' + new Date().getTime();

                            var errorToast = $('<div id="' + toastId + '" class="toast bg-danger text-white fly-in" role="alert" aria-live="assertive" aria-atomic="true" data-autohide="true" data-delay="3000" style="box-shadow: 0 0 10px rgba(0,0,0,0.2);>');
                            errorToast.append('<div class="toast-body text-center">' + errorMsg + '</div>');

                            // Append the toast to the toast container
                            $('.toast-container').append(errorToast);

                            // Show the toast
                            errorToast.toast('show');

                            // Remove the toast with "fly-out" animation after 3 seconds
                            setTimeout(function () {
                                errorToast.removeClass('fly-in').addClass('fly-out');
                                setTimeout(function () {
                                    errorToast.toast('hide');
                                }, 200); // Animation duration
                            }, 3000);
                        }
                    },
                    error: function (xhr, status, error) {
                        // Handle error
                    }
                });
            });
        </script>

        <!-- cart button when user scroll -->
        <script>
            $(document).ready(function() {
                $(window).scroll(function() {
                    if ($(this).scrollTop() > 300) { // Adjust the value as needed
                        $('.cart-button').addClass('show');
                    } else {
                        $('.cart-button').removeClass('show');
                    }
                });
            });
        </script>

        <script>

        // to calculate time
        function formatDateToRelativeTime(dateString) {
            const inputDate = parseCustomDateString(dateString);

            if (!inputDate || isNaN(inputDate)) {
                return "Invalid date format";
            }

            const now = new Date();
            const timeDifference = now - inputDate;

            // Define time intervals in milliseconds
            const minute = 60 * 1000;
            const hour = 60 * minute;
            const day = 24 * hour;
            const week = 7 * day;
            const month = 30 * day;
            const year = 365 * day;

            // Determine the appropriate time unit and value
            if (timeDifference < minute) {
                return "Just now";
            } else if (timeDifference < hour) {
                const minutesAgo = Math.floor(timeDifference / minute);
                return `${minutesAgo} minit${minutesAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < day) {
                const hoursAgo = Math.floor(timeDifference / hour);
                return `${hoursAgo} jam${hoursAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < week) {
                const daysAgo = Math.floor(timeDifference / day);
                return `${daysAgo} hari${daysAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < month) {
                const weeksAgo = Math.floor(timeDifference / week);
                return `${weeksAgo} minggu${weeksAgo > 1 ? '' : ''} yang lalu`;
            } else if (timeDifference < year) {
                const monthsAgo = Math.floor(timeDifference / month);
                return `${monthsAgo} bulan${monthsAgo > 1 ? '' : ''} yang lalu`;
            } else {
                const yearsAgo = Math.floor(timeDifference / year);
                return `${yearsAgo} tahun${yearsAgo > 1 ? '' : ''} yang lalu`;
            }
        }

        function parseCustomDateString(dateString) {
            const parts = dateString.match(/(\d{2})\/(\d{2})\/(\d{4}) (\d{2}):(\d{2}) (AM|PM)/);

            if (!parts) {
                // Handle invalid date format
                return null;
            }

            let [, day, month, year, hours, minutes, ampm] = parts;

            day = parseInt(day, 10);
            month = parseInt(month, 10) - 1; // Months are 0-based in JavaScript (0 = January)
            year = parseInt(year, 10);
            hours = parseInt(hours, 10);
            minutes = parseInt(minutes, 10);

            if (ampm === "PM" && hours < 12) {
                hours += 12; // Convert to 24-hour format
            }

            const date = new Date(year, month, day, hours, minutes);
            return date;
        }

            // ------------ PRODUCT SOLD (TOAST)------------
            var productList = {!! json_encode($recordList) !!};

            // Filter the array to exclude items with 'trackingno' equal to 'CBS'
                var productList = productList.filter(function(product) {
                return product.trackingno !== 'CBS' && product.productid !== "-100";
            });

            // Function to display the toast message
            function showToast(message, index) {
                var toast = document.createElement('div');
                toast.classList.add('toastProd');

                var toastText = document.createElement('span');
                toastText.innerText = message;
                toast.appendChild(toastText);

                var container = document.getElementById('toast-container-2');
                container.appendChild(toast);

                // Show the toast element for 5 seconds
                setTimeout(function() {
                    toast.classList.add('show');
                }, index * 8000 + 3000); // Delay the display of each toast message (5s display + 3s delay)

                // Remove the toast element after 8 seconds
                setTimeout(function() {
                    toast.classList.remove('show');
                    setTimeout(function() {
                        container.removeChild(toast);
                    }, 300);
                }, (index + 1) * 8000); // Delay the removal of each toast message (5s display + 3s delay)

                // Check if the last product is reached
                if (index === productList.length - 1) {
                    setTimeout(function() {
                        var nextIndex = (index + 1) % productList.length;
                        var nextProductName = productList[nextIndex]['customername'] + ' telah membeli ' + productList[nextIndex]['productname'] + '' ;
                        showToast(nextProductName, nextIndex);
                    }, (index + 1) * 8000 + 3000); // Delay the next toast message by 3 seconds after the last toast message
                } else {
                    // Display the next toast message
                    var nextIndex = index + 1;
                    var nextProductName = productList[nextIndex]['customername'] + ' telah membeli ' + productList[nextIndex]['productname'] + '' ;
                    // showToast(nextProductName, nextIndex);
                }
            }

            // Loop through the product list and display the toast messages
            for (var i = 0; i < productList.length; i++) {

                var relativeTime = formatDateToRelativeTime(productList[i]['transactiondate']);
                // convert customer name
                var custName = productList[i]['customername'];
                var word = custName.split(' ');
                var firstName = word[0];
                var finalcustName = firstName.charAt(0) + firstName.slice(1).toLowerCase();

                var productName = finalcustName + ' telah membeli ' + productList[i]['productname'] + '\n'  + relativeTime;

                // console.log("Product Name: " + productName);
                showToast(productName, i);
            }
        </script>

    </body>
</html>
