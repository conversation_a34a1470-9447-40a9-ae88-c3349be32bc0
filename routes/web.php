<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\CartController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PoskodController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\LandingController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\ShopGridController;
use App\Http\Controllers\ProductDetailController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect()->route('homeMask', 'bizappstore');
})->name('index');

// Landing Page
Route::get('/landing-page', [LandingController::class, 'landingView'])->name('landing.page');
Route::post('/landing-page/submit', [LandingController::class, 'landingSubmit'])->name('landing.submit');

//URL Masking
Route::get('/directory', [HomeController::class, 'directory'])->name('directory');
Route::get('/directory/login', function () {
    return view('directoryLogin');
})->name('directoryLogin');
Route::post('/directory/login/submit', [HomeController::class, 'directoryLoginPost'])->name('directoryLoginPost');
Route::post('/directory/submit', [HomeController::class, 'directoryEdit'])->name('directoryEdit');

// POSKOD & STATE
Route::get('/poskod/{poskod}', [PoskodController::class, 'getPoskod'])->name('getPoskod');

// Home
Route::get('/{url_param}', [HomeController::class, 'indexMask'])->name('homeMask');
Route::post('/discEmail/{url_param}',[HomeController::class, 'claimcoupon'])->name('discountemail');

// Shop-Grid
Route::get('/{url_param}/shop-grid', [ShopGridController::class, 'indexMask'])->name('shop-gridMask');
Route::get('/{url_param}/product-details/{productid}', [ProductDetailController::class, 'indexMask'])->name('product-details');

Route::post('/{url_param}/shop-grid-atc', [ShopGridController::class, 'shopGridAtc'])->name('shop-grid-atc');

// Collection
Route::get('/{url_param}/shop-grid/{koleksiid}', [HomeController::class, 'koleksiToGrid'])->name('koleksi-to-grid');

// Contact
Route::get('/{url_param}/contact', [ContactController::class, 'indexMask'])->name('contactMask');

// Cart
Route::get('/cart', [CartController::class, 'index'])->name('cart');
Route::delete('/remove-cart-id', [CartController::class, 'removeCartById'])->name('cart.remove.id');
Route::post('/decrease-cart', [CartController::class, 'decreaseCartQty'])->name('cart.decrease');
Route::post('/increase-cart', [CartController::class, 'increaseCartQty'])->name('cart.increase');
Route::post('/{url_param}/apply-discount',[CartController::class, 'applyDiscountCode'])->name('cart.apply.discount');
Route::get('/{url_param}/cart', [CartController::class, 'indexMask'])->name('cartMask');
Route::post('/{url_param}/add-to-cart', [CartController::class, 'addToCart'])->name('cartMask.store');

Route::post('/update-quantity-cart', [CartController::class, 'updateQuantity'])->name('update.quantity');
Route::post('/update-price-cart', [CartController::class, 'recalculatePrice'])->name('update.recalculateprice');
Route::post('/{url_param}/apply-discount-coupon', [CartController::class, 'applyDiscountCoupon'])->name('cart.apply.coupon');

// Checkout
Route::get('/{url_param}/checkout', [CheckoutController::class, 'indexMask'])->name('checkoutMask');
Route::post('/{url_param}/process-checkout',[CheckoutController::class,'processCheckout'])->name('checkout.process');


// Home Affiliate
Route::get('/{url_param}/{agent_pid?}', [HomeController::class, 'indexMaskAgent'])->name('homeMaskAgent');
// Cart Affiliate
Route::get('/{url_param}/cart/{agent_pid?}', [CartController::class, 'indexMaskAgent'])->name('cartMaskAgent');
// Checkout Affiliate
Route::get('/{url_param}/checkout/{agent_pid?}', [CheckoutController::class, 'indexMaskAgent'])->name('checkoutMaskAgent');
// Contact Affiliate
Route::get('/{url_param}/contact/{agent_pid?}', [ContactController::class, 'indexMaskAgent'])->name('contactMaskAgent');
// Shop-Grid Affiliate
Route::get('/{url_param}/shop-grid/{agent_pid?}', [ShopGridController::class, 'indexMaskAgent'])->name('shop-gridMaskAgent');
Route::get('/{url_param}/product-details/{productid}/{agent_pid?}', [ProductDetailController::class, 'indexMaskAgent'])->name('product-detailsAgent');
// Collection Affiliate
Route::get('/{url_param}/shop-grid/{koleksiid}/{agent_pid?}', [HomeController::class, 'koleksiToGridAgent'])->name('koleksi-to-grid-agent');


// Route::fallback(function (){
//     return back();
// });

// Route::fallback([HomeController::class,'fallback']);

// Cache invalidation endpoint for external systems
Route::post('/api/invalidate-cache/{pid}', function($pid) {
    Cache::forget("shop_collection_{$pid}");
    Cache::forget("product_list_{$pid}");
    Cache::forget("profile_{$pid}");
    Cache::forget("record_{$pid}");

    // Also clear any cache keys that might contain this pid
    $cacheKeys = [
        "shop_collection_{$pid}",
        "profile_{$pid}",
        "record_{$pid}",
    ];

    // Clear product list cache for all possible cleanurl combinations
    // This is a bit broad but ensures cache consistency
    Cache::flush(); // For now, we'll flush all cache when invalidating

    return response()->json(['status' => 'cache_cleared', 'pid' => $pid]);
})->name('cache.invalidate');

// Performance monitoring endpoint
Route::get('/performance/stats', function() {
    $cacheStats = [
        'driver' => config('cache.default'),
        'redis_info' => config('cache.default') === 'redis' ? 'Redis enabled' : 'Not using Redis'
    ];

    return response()->json([
        'cache' => $cacheStats,
        'timestamp' => now(),
        'message' => 'Performance monitoring active'
    ]);
})->name('performance.stats');

Route::fallback(function (\Illuminate\Http\Request $request){
    $url_param = $request->path();
    return App::call('App\Http\Controllers\HomeController@fallback' , ['url_param' => $url_param]);
});
