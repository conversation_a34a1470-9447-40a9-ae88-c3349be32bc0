-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               5.7.33 - MySQL Community Server (GPL)
-- Server OS:                    Win64
-- HeidiSQL Version:             11.2.0.6213
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- Dumping database structure for minishop_laravel
CREATE DATABASE IF NOT EXISTS `minishop_laravel` /*!40100 DEFAULT CHARACTER SET latin1 */;
USE `minishop_laravel`;

-- Dumping structure for table minishop_laravel.url_masking
CREATE TABLE IF NOT EXISTS `url_masking` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `url_param` varchar(225) NOT NULL,
  `cleanurl` varchar(225) NOT NULL,
  `pid` varchar(225) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

-- Dumping data for table minishop_laravel.url_masking: ~2 rows (approximately)
/*!40000 ALTER TABLE `url_masking` DISABLE KEYS */;
INSERT INTO `url_masking` (`id`, `url_param`, `cleanurl`, `pid`, `created_at`, `updated_at`) VALUES
	(1, 'minishop_kami', 'wowskin', '1', '2023-01-04 15:16:17', '2023-01-12 00:00:00'),
	(2, 'selamat_datang', 'vsfaiz', '375270', '2023-01-04 15:16:40', '2023-01-12 00:00:00'),
	(3, 'user_bizapp', 'syariergps', '2078114', '2023-01-04 15:17:12', '2023-01-12 00:00:00'),
	(4, 'bizappstore', 'bizappstore', '69901', '2023-01-13 12:24:19', NULL);
/*!40000 ALTER TABLE `url_masking` ENABLE KEYS */;

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
