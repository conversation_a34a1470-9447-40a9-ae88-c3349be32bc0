<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Helpers\ArraySafetyHelper;

class ArraySafetyTest extends TestCase
{
    public function test_safe_get_with_valid_key()
    {
        $array = ['key1' => 'value1', 'key2' => 'value2'];
        $result = ArraySafetyHelper::safeGet($array, 'key1');
        $this->assertEquals('value1', $result);
    }

    public function test_safe_get_with_invalid_key()
    {
        $array = ['key1' => 'value1'];
        $result = ArraySafetyHelper::safeGet($array, 'nonexistent', 'default');
        $this->assertEquals('default', $result);
    }

    public function test_has_valid_first_element_with_valid_array()
    {
        $array = [['pid' => '123'], ['pid' => '456']];
        $result = ArraySafetyHelper::hasValidFirstElement($array);
        $this->assertTrue($result);
    }

    public function test_has_valid_first_element_with_empty_array()
    {
        $array = [];
        $result = ArraySafetyHelper::hasValidFirstElement($array);
        $this->assertFalse($result);
    }

    public function test_get_first_element_property_with_valid_data()
    {
        $array = [['pid' => '123', 'name' => 'test']];
        $result = ArraySafetyHelper::getFirstElementProperty($array, 'pid');
        $this->assertEquals('123', $result);
    }

    public function test_get_first_element_property_with_invalid_data()
    {
        $array = [];
        $result = ArraySafetyHelper::getFirstElementProperty($array, 'pid', 'default');
        $this->assertEquals('default', $result);
    }

    public function test_get_first_element_property_with_missing_property()
    {
        $array = [['name' => 'test']];
        $result = ArraySafetyHelper::getFirstElementProperty($array, 'pid', 'default');
        $this->assertEquals('default', $result);
    }
}
